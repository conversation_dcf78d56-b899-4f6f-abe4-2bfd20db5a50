import type { ClientLoader } from '@app/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import { type ClientLoaderFunction, Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';
import { MonitoringPageHeaderModel } from '@views/monitoring';

export const clientLoader: ClientLoaderFunction = action((): ClientLoader => {
    return {
        pageHeader: new MonitoringPageHeaderModel(),
        tabs: [
            {
                topicPath: 'compliance/monitoring/production',
                label: t`Production`,
            },
            {
                topicPath: 'compliance/monitoring/codebases',
                label: t`Codebase`,
            },
            {
                topicPath: 'compliance/monitoring/pipelines',
                label: t`Pipeline`,
            },
        ],
    };
});

export const meta: MetaFunction = () => [{ title: 'Monitoring' }];

const Monitoring = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Monitoring" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Monitoring;

import type { ClientLoader } from '@app/types';
import {
    sharedFrameworkCreateController,
    sharedFrameworksController,
} from '@controllers/frameworks';
import { DEFAULT_PAGE_SIZE } from '@cosmos/components/datatable';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedFrameworksModel } from '@models/frameworks';
import { FrameworksPageHeaderModel } from '@models/frameworks-page-header';
import type { MetaFunction } from '@remix-run/node';
import { Outlet } from '@remix-run/react';
import { RouteLandmark } from '@ui/layout-landmarks';

export const meta: MetaFunction = () => [{ title: t`Frameworks` }];

export const clientLoader = action((): ClientLoader => {
    const { isFrameworksDomainEnabled } = sharedFrameworksModel;

    if (!isFrameworksDomainEnabled) {
        throw new Error('Missing permission to access Frameworks', {
            cause: '403',
        });
    }

    sharedFrameworkCreateController.load();

    sharedCurrentCompanyController.load();

    sharedFrameworksController.loadPage(1, DEFAULT_PAGE_SIZE);

    sharedFrameworksController.getUnmappedFrameworksQuery.load();

    return {
        pageHeader: new FrameworksPageHeaderModel(),
    };
});

const Frameworks = (): React.JSX.Element => {
    return (
        <RouteLandmark as="section" data-testid="Frameworks" data-id="jPwZmxJb">
            <Outlet />
        </RouteLandmark>
    );
};

export default Frameworks;

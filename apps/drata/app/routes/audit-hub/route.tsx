import { isEmpty } from 'lodash-es';
import type { ClientLoader } from '@app/types';
import { MainAppTopicsNavComponent } from '@components/main-app-topics-nav';
import { sharedAuthController } from '@controllers/auth';
import { routeController } from '@controllers/route';
import { Box } from '@cosmos/components/box';
import { Icon, type IconName } from '@cosmos/components/icon';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { breakpointLg } from '@cosmos/constants/tokens';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import type { MetaFunction } from '@remix-run/node';
import {
    type ClientLoaderFunction,
    Outlet,
    useLocation,
    useNavigate,
} from '@remix-run/react';
import { MIN_APPMAIN_WIDTH } from '@ui/layout-constants';
import { PageAsideUi } from '@ui/page-aside';
import { ContentNavigationMenu } from '@ui/page-content';
import { PageHeaderUi } from '@ui/page-header';

export const meta: MetaFunction = () => [{ title: t`Audit Hub` }];

export const clientLoader: ClientLoaderFunction = (): ClientLoader => {
    return {
        subdomainConfig: {
            id: 'audit-hub',
            userPart: `/audit-hub`,
            authRoute: '/auth/login',
        },
    };
};

export interface Tab {
    topicPath: string;
    label: string;
    iconName?: IconName;
}

const AuditHub = observer(() => {
    const navigate = useNavigate();
    const location = useLocation();
    const { contentNavItems, layout } = routeController;
    const isSettingsPage = location.pathname.includes('/audit-hub/settings');
    const isCenterLayout = layout?.centered ?? false;

    return (
        <Stack
            direction="column"
            display="flex"
            data-id="QqMJifIh"
            data-testid={isSettingsPage ? 'AuditHubSettings' : 'AuditHub'}
            height="100%"
            width="100%"
            minHeight="0"
        >
            <Box
                backgroundColor="neutralBackgroundNone"
                borderColor="neutralBorderFaded"
                borderWidth="borderWidthSm"
                data-id="Header"
                width="100%"
            >
                <Stack
                    direction="row"
                    align="center"
                    justify="between"
                    p="md"
                    data-id="HeaderNav"
                >
                    <Stack direction="row" align="center" gap="2x">
                        <Icon name="DrataFilled" size="500" />
                        <Text type="title">{t`Audit Hub`}</Text>
                    </Stack>
                    <Stack direction="row" align="center" gap="2x">
                        <SchemaDropdown
                            isIconOnly
                            label={t`User menu`}
                            startIconName="UserCircleSingle"
                            level="tertiary"
                            colorScheme="neutral"
                            data-testid="UserMenu"
                            data-id="user-dropdown"
                            items={[
                                {
                                    id: 'setting',
                                    label: t`Setting`,
                                    onSelect: () => {
                                        navigate('/audit-hub/settings/profile');
                                    },
                                },
                                {
                                    id: 'signout',
                                    label: t`Sign out`,
                                    onSelect: () => {
                                        sharedAuthController.logout();
                                    },
                                },
                            ]}
                        />
                    </Stack>
                </Stack>
            </Box>
            <Stack
                direction="row"
                display="flex"
                height="100%"
                minHeight="0"
                width="100%"
            >
                <Box width="auto">
                    <MainAppTopicsNavComponent />
                </Box>

                {/* The Box allows Stack to horizontally overflow within Box */}
                <Box height="100%" width="100%" overflowX="auto">
                    <Stack
                        data-id="domains-flat-child-stack"
                        display="flex"
                        direction="column"
                        height="100%"
                        minHeight="0"
                        overflowY="scroll"
                        minWidth={MIN_APPMAIN_WIDTH}
                    >
                        {isCenterLayout ? (
                            <>
                                <Stack align="center" width="100%">
                                    <Box width="100%" maxWidth={breakpointLg}>
                                        <PageHeaderUi />

                                        {!isEmpty(contentNavItems) && (
                                            <ContentNavigationMenu
                                                data-id="page-header-ui-content-navigation-menu"
                                                value={location.pathname}
                                                items={contentNavItems}
                                            />
                                        )}
                                    </Box>
                                </Stack>
                                {isEmpty(contentNavItems) && (
                                    <Divider
                                        orientation="horizontal"
                                        size="sm"
                                    />
                                )}
                            </>
                        ) : (
                            <>
                                <PageHeaderUi />

                                {!isEmpty(contentNavItems) && (
                                    <ContentNavigationMenu
                                        data-id="page-header-ui-content-navigation-menu"
                                        value={location.pathname}
                                        items={contentNavItems}
                                    />
                                )}
                                {isEmpty(contentNavItems) && (
                                    <Divider
                                        orientation="horizontal"
                                        size="sm"
                                    />
                                )}
                            </>
                        )}

                        <Stack
                            data-id="domains-flat-content-stack"
                            width="100%"
                            minWidth={MIN_APPMAIN_WIDTH}
                            height="100%"
                            minHeight="0"
                            p="3xl"
                            direction="column"
                            {...(isCenterLayout
                                ? { maxWidth: breakpointLg }
                                : {})}
                        >
                            <Outlet />
                        </Stack>
                    </Stack>
                </Box>

                <PageAsideUi />
            </Stack>
        </Stack>
    );
});

export default AuditHub;

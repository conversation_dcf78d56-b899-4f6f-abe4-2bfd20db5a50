const isLocalhost = process.env.NODE_ENV === 'development';

// This is separate from is not production to allow for more granular control
const isDevelopmentOrTesting = process.env.NODE_ENV !== 'production';

// `nonce-remix-server` allows the inline scripts in root.server.tsx
// these are extracted into a separate file in production build
const SHAs = {
    remixHello: "'sha256-WbxxzrCZut/IB2F9JJiBp7TbpEofPsmzSMSwNriNL+4='",
    remixContext: "'sha256-azxeYiKPS6skAKNRlqeG+M7Ey+v9QxNKSKRc9S3R678='",
    remixRCSetup: "'sha256-pD1IvxrgXgKrAhNJmdMwtplCR1BZCy9ekf7LyKljrWI='",
    remixStreamControllerClose:
        "'sha256-FeKBGzRHcROv0xd1BUE68Cy1yefbayaeP0AhzHm/mNo='",
    remixRCActivate: "'sha256-p7GE78bbMHDrE4IWzpiMSttAsTpUu7wwi5/wvnH54Os='",
};

export const csp = `

upgrade-insecure-requests;

frame-ancestors
    'none'
;

frame-src
    'self'
    *.apideck.com
    *.flatfile.com
    *.amazonaws.com
    https://fast.chameleon.io/
    ${isDevelopmentOrTesting ? 'https://org-storage-staging-bucket.safebase.io' : 'https://org-storage.safebase.io'}
;

form-action
    'none'
;

base-uri
    'none'
;

default-src
    'none'
;

style-src
    'self'
    'unsafe-inline'
    drata.com
    *.drata.com
    fonts.googleapis.com
    cdn.jsdelivr.net/npm/monaco-editor@0.52.2/
;

font-src
    drata.com
    *.drata.com
    fonts.gstatic.com
;

img-src
    'self'
    drata.com
    *.drata.com
    dratacdn.com
    *.dratacdn.com
    *.amazonaws.com
    https://fast.chameleon.io/
    data:
    img.logo.dev
    ${isDevelopmentOrTesting ? 'https://*.iconarchive.com/' : ''}
    app.safebase.io
    ${isDevelopmentOrTesting ? 'https://cdn-staging-bucket.safebase.io' : ''}
    https://org-storage.safebase.io
    ${isDevelopmentOrTesting ? 'https://org-storage-staging-bucket.safebase.io' : ''}
;

script-src
    'self'
    drata.com
    *.drata.com
    fonts.googleapis.com
    launchdarkly.com
    *.launchdarkly.com
    *.pusher.com
    cdn.jsdelivr.net/npm/monaco-editor@0.52.2/
    https://fast.chameleon.io/
    https://cdn.segment.com/

    ${isLocalhost ? "'nonce-remix-server'" : ''}
    ${isLocalhost ? SHAs.remixHello : ''}
    ${isLocalhost ? SHAs.remixContext : ''}
    ${isLocalhost ? SHAs.remixRCSetup : ''}
    ${isLocalhost ? SHAs.remixStreamControllerClose : ''}
    ${isLocalhost ? SHAs.remixRCActivate : ''}
;

connect-src
    drata.com
    *.drata.com
    *.launchdarkly.com
    *.pusher.com
    *.flatfile.com
    wss://*.pusher.com
    browser-intake-datadoghq.com
    *.amazonaws.com
    https://fast.chameleon.io/
    localhost:3000
    ${isLocalhost ? 'ws://localhost:5173' : ''}
    https://api.segment.io/
    https://cdn.segment.com/

;

`
    .trim()
    .replaceAll(/ {2,}/g, ' ') // remove extra spaces
    .replaceAll('\n', '');

import { useRef } from 'react';
import { ViewEditCardComponent } from '@components/view-edit-card';
import {
    sharedControlDetailsController,
    sharedControlsUpdateMutationController,
} from '@controllers/controls';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { sharedControlsInfoViewEditCardModel } from '../models/controls-info-view-edit-card.model';
import { ControlsInfoForm } from './controls-info-form.component';
import { ControlsInfoReadOnlyCard } from './controls-info-read-only-card.component';

export const ControlsInfoViewEditCard = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit, getFormValues, triggerResetForm } =
        useFormSubmit();
    const cardRef = useRef<HTMLDivElement>(null);
    const { isControlUpdateLoading, hasControlUpdateError } =
        sharedControlsUpdateMutationController;
    const { controlDetails } = sharedControlDetailsController;
    const { handleOnSubmit, getAdditionalEditActions } =
        sharedControlsInfoViewEditCardModel;
    const { hasWriteControlPermission } = sharedFeatureAccessModel;
    const additionalEditActions = getAdditionalEditActions(
        getFormValues,
        triggerResetForm,
    );

    const scrollToThisComponent = () => {
        cardRef.current?.scrollIntoView({
            behavior: 'smooth',
            block: 'center',
        });
    };

    return (
        <div ref={cardRef} data-id="KD8xephE">
            <ViewEditCardComponent
                title={t`Info`}
                readOnlyComponent={<ControlsInfoReadOnlyCard />}
                data-testid="ControlsOverviewInfoCardComponent"
                data-id="controls-info-card"
                isMutationPending={isControlUpdateLoading}
                hasMutationError={hasControlUpdateError}
                additionalEditActions={additionalEditActions}
                editComponent={
                    hasWriteControlPermission ? (
                        <ControlsInfoForm
                            formRef={formRef}
                            initialValues={{
                                name: controlDetails?.name,
                                code: controlDetails?.code,
                                description: controlDetails?.description,
                                question: controlDetails?.question,
                                activity: controlDetails?.activity,
                            }}
                            onSubmit={(values) => {
                                handleOnSubmit(values, scrollToThisComponent);
                            }}
                        />
                    ) : null
                }
                onSave={triggerSubmit}
            />
        </div>
    );
});

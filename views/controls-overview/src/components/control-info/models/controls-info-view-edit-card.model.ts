import { isNil } from 'lodash-es';
import { handleOpenCompareControlFieldsModal } from '@components/controls';
import {
    sharedControlCustomFieldsController,
    sharedControlDetailsController,
    sharedControlsUpdateMutationController,
} from '@controllers/controls';
import type { Action } from '@cosmos/components/action-stack';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedCustomFieldsManager } from '@models/custom-fields';
import type { FormValues } from '@ui/forms';

class ControlsInfoViewEditCardModel {
    constructor() {
        makeAutoObservable(this);
    }

    handleOnSubmit = (values: FormValues, onScrollTo?: () => void) => {
        const { controlDetails } = sharedControlDetailsController;
        const { controlCustomFields } = sharedControlCustomFieldsController;
        const customFieldsData = controlCustomFields[0]?.customFields ?? [];

        if (!controlDetails) {
            return;
        }

        const customFieldsValues =
            sharedCustomFieldsManager.extractCustomFieldsFromFormValues(
                values,
                customFieldsData,
            );

        sharedControlsUpdateMutationController.updateControl(
            controlDetails.id,
            {
                name: values.name as string,
                code: values.code as string,
                description: values.description as string,
                question: values.question ? (values.question as string) : null,
                activity: values.activity ? (values.activity as string) : null,
            },
            customFieldsValues,
            onScrollTo,
        );
    };

    getAdditionalEditActions = (
        getFormValues: () => FormValues | null,
        triggerResetForm: (values: Partial<FormValues>) => void,
    ): Action[] => {
        const { hasControlTemplatePermission } = sharedFeatureAccessModel;
        const { controlDetails } = sharedControlDetailsController;
        const isTemplatedControl = !isNil(
            controlDetails?.fk_control_template_id,
        );
        const displayCompareToDefaults =
            hasControlTemplatePermission && isTemplatedControl;

        return displayCompareToDefaults
            ? [
                  {
                      id: 'view-latest-template-edit-card-button-id',
                      actionType: 'button',
                      typeProps: {
                          label: t`View latest template`,
                          'data-id':
                              'view-latest-template-edit-card-button-data-id',
                          level: 'tertiary',
                          onClick: () => {
                              handleOpenCompareControlFieldsModal(
                                  getFormValues,
                                  triggerResetForm,
                              );
                          },
                      },
                  },
              ]
            : [];
    };
}

export const sharedControlsInfoViewEditCardModel =
    new ControlsInfoViewEditCardModel();

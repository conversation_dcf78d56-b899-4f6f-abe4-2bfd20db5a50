import { useEffect, useRef } from 'react';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { ControlLinkedWorkspacesEditComponent } from './components/control-linked-workspaces-edit.component';
import { ControlLinkedWorkspacesReadComponent } from './components/control-linked-workspaces-read.component';
import { controlLinkedWorkspacesCardModel } from './models/control-linked-workspaces-card.model';

export const ControlsOverviewLinkedWorkspacesCardComponent = observer(
    (): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const { isEditing, actions, shouldScrollIntoView } =
            controlLinkedWorkspacesCardModel;

        const controlWorkspaceRef = useRef<HTMLDivElement | null>(null);

        useEffect(() => {
            controlLinkedWorkspacesCardModel.triggerSubmit = triggerSubmit;
        }, [triggerSubmit]);

        useEffect(() => {
            if (shouldScrollIntoView) {
                controlWorkspaceRef.current?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'end',
                });
            }
        }, [shouldScrollIntoView]);

        return (
            <Box
                gridRow="3"
                gridColumn="2"
                width="100%"
                data-id="x9qbAb-H"
                ref={controlWorkspaceRef}
            >
                <Card
                    title={t`Linked workspaces`}
                    isEditMode={isEditing}
                    actions={actions}
                    body={
                        isEditing ? (
                            <ControlLinkedWorkspacesEditComponent
                                formRef={formRef}
                            />
                        ) : (
                            <ControlLinkedWorkspacesReadComponent />
                        )
                    }
                />
            </Box>
        );
    },
);

import { isNil } from 'lodash-es';
import React from 'react';
import { MARGIN_DAYS } from '@components/control-approval';
import { getStageColor, getStageTextLabel } from '@components/controls';
import {
    sharedControlApprovalReviewersController,
    sharedControlApprovalsController,
    sharedControlApprovalsReviewersMutationController as mutationController,
    sharedControlDetailsController,
    sharedControlOwnersController,
} from '@controllers/controls';
import {
    sharedUsersInfiniteController,
    type UserListBoxItemData,
} from '@controllers/users';
import type { Action } from '@cosmos/components/action-stack';
import { Avatar } from '@cosmos/components/avatar';
import type { TDateISODate } from '@cosmos/components/date-picker-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import type { MetadataProps } from '@cosmos/components/metadata';
import { ApprovalStatus } from '@drata/enums';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, when } from '@globals/mobx';
import { compareDayIsPast, formatDate } from '@helpers/date-time';
import { getFullName, getInitials } from '@helpers/formatters';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import type { FormSchema, FormValues } from '@ui/forms';

class ControlReviewCardModel {
    isEditing = false;
    shouldScrollIntoView = false;
    triggerSubmit: () => Promise<boolean> | undefined = () => undefined;

    constructor() {
        makeAutoObservable(this);
    }

    get isUpdating(): boolean {
        return (
            mutationController.isUpdating ||
            sharedControlApprovalReviewersController.isFetching
        );
    }

    get isCurrentApprovalApproved(): boolean {
        const { currentControlApproval } = sharedControlApprovalsController;

        return (
            currentControlApproval?.approvalStatus === ApprovalStatus.APPROVED
        );
    }

    get isCurrentApprovalEmpty(): boolean {
        const { currentControlApproval, isLoading } =
            sharedControlApprovalsController;

        if (isLoading) {
            return false;
        }

        return (
            !currentControlApproval ||
            currentControlApproval.approvalStatus === ApprovalStatus.INITIALIZED
        );
    }

    get hasApprovalWithoutOwners(): boolean {
        const { controlHasOwners, isLoading } = sharedControlOwnersController;

        if (isLoading) {
            return false;
        }

        return !controlHasOwners && !this.isCurrentApprovalEmpty;
    }

    get actions(): Action[] {
        const { controlHasOwners } = sharedControlOwnersController;
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        if (!hasWriteControlPermission) {
            return [];
        }

        if (this.isEditing) {
            return [
                ...(controlHasOwners
                    ? [
                          {
                              id: 'save-button',
                              actionType: 'button' as const,
                              typeProps: {
                                  label: t`Save`,
                                  level: 'primary' as const,
                                  size: 'md' as const,
                                  isLoading: this.isUpdating,
                                  onClick: this.handleSaveClick,
                              },
                          },
                      ]
                    : []),
                {
                    id: 'cancel-button',
                    actionType: 'button',
                    typeProps: {
                        label: t`Cancel`,
                        level: 'secondary',
                        size: 'md',
                        cosmosUseWithCaution_isDisabled: this.isUpdating,
                        onClick: this.handleCancelClick,
                    },
                },
            ];
        }

        return [
            ...this.deleteAction,
            {
                id: 'edit-button',
                actionType: 'button',
                typeProps: {
                    label: this.isCurrentApprovalEmpty ? t`Set up` : t`Edit`,
                    level: 'secondary',
                    size: 'md',
                    onClick: this.handleEditClick,
                },
            },
        ];
    }

    get deleteAction(): Action[] {
        if (this.isCurrentApprovalEmpty) {
            return [];
        }

        return [
            {
                id: 'delete-button',
                actionType: 'button',
                typeProps: {
                    isIconOnly: true,
                    startIconName: 'Trash',
                    label: t`Delete`,
                    level: 'secondary',
                    colorScheme: 'danger',
                    isLoading: mutationController.isDeleting,
                    onClick: this.handleDeleteClick,
                },
            },
        ];
    }

    handleEditClick = () => {
        const { isCreating, createCurrentControlApproval } = mutationController;
        const { controlId } = sharedControlDetailsController;
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        this.shouldScrollIntoView = true;

        if (!controlId || !hasWriteControlPermission) {
            return;
        }

        createCurrentControlApproval(controlId);

        when(
            () => !isCreating,
            () => {
                this.isEditing = true;
            },
        );
    };

    handleCancelClick = () => {
        if (this.isUpdating) {
            return;
        }

        this.shouldScrollIntoView = false;
        this.isEditing = false;
    };

    handleSaveClick = () => {
        this.triggerSubmit()?.catch(() => {
            console.error('Failed to submit form');
        });
        this.shouldScrollIntoView = false;
    };

    handleDeleteClick = () => {
        openConfirmationModal({
            title: t`Delete approval?`,
            body: t`Deleting the approval means this control will no longer need an approval before it shows as "ready".`,
            confirmText: t`Delete approval`,
            cancelText: t`Cancel`,
            type: 'danger',
            isLoading: () => mutationController.isDeleting,
            onConfirm: action(() => {
                mutationController.deleteCurrentControlApproval();
                when(
                    () => !mutationController.isDeleting,
                    () => {
                        closeConfirmationModal();
                    },
                );
            }),
            onCancel: closeConfirmationModal,
        });
    };

    handleSubmit = (values: FormValues): void => {
        const typedValues = values as {
            approvers: UserListBoxItemData[];
            deadlineDate: string;
            skipNextDeadline: boolean;
        };

        const { currentControlApproval } = sharedControlApprovalsController;

        if (!currentControlApproval) {
            return;
        }

        const newStatus =
            currentControlApproval.approvalStatus === ApprovalStatus.INITIALIZED
                ? ApprovalStatus.PREPARE_FOR_REVIEWS
                : currentControlApproval.approvalStatus;

        if (this.isCurrentApprovalApproved) {
            mutationController.updateNextControlApproval({
                deadlineDate: typedValues.skipNextDeadline
                    ? null
                    : formatDate('timestamp', typedValues.deadlineDate),
            });
        }

        mutationController.updateCurrentControlApproval({
            status: newStatus,
            deadlineDate: typedValues.skipNextDeadline
                ? null
                : formatDate('timestamp', typedValues.deadlineDate),
            approverIds: typedValues.approvers.map((approver) => {
                return Number(approver.id);
            }),
        });

        when(
            () => !this.isUpdating,
            () => {
                this.isEditing = false;
                this.shouldScrollIntoView = false;
            },
        );
    };

    get deadlineCurrentValue(): TDateISODate | undefined {
        const { currentControlApproval, nextControlApproval } =
            sharedControlApprovalsController;

        if (this.isCurrentApprovalEmpty) {
            return undefined;
        }

        if (this.isCurrentApprovalApproved) {
            return isNil(nextControlApproval?.deadline)
                ? undefined
                : (formatDate('timestamp', nextControlApproval.deadline).split(
                      'T',
                  )[0] as TDateISODate);
        }

        return isNil(currentControlApproval?.deadline)
            ? undefined
            : (formatDate('timestamp', currentControlApproval.deadline).split(
                  'T',
              )[0] as TDateISODate);
    }

    get approversCurrentValue(): ListBoxItemData[] {
        const { controlApprovalsReviewers } =
            sharedControlApprovalReviewersController;

        if (this.isCurrentApprovalEmpty) {
            return [];
        }

        return controlApprovalsReviewers.map((approver) => {
            const fullName = getFullName(approver.firstName, approver.lastName);

            return {
                id: approver.id.toString(),
                label: fullName,
                startSlot: React.createElement(Avatar, {
                    fallbackText: getInitials(fullName),
                    imgSrc: approver.avatarUrl,
                    imgAlt: fullName,
                    size: 'sm',
                }),
                description: approver.email,
                value: approver.id.toString(),
            };
        });
    }

    get formSchema(): FormSchema {
        const { options, hasNextPage, isFetching, isLoading, onFetchUsers } =
            sharedUsersInfiniteController;

        return {
            approvers: {
                type: 'combobox',
                label: t`Approvers`,
                loaderLabel: t`Loading results`,
                removeAllSelectedItemsLabel: t`Clear all`,
                getSearchEmptyState: () => t`No approvers found`,
                placeholder: t`Search by name`,
                isMultiSelect: true,
                options,
                hasMore: hasNextPage,
                isLoading: isFetching && isLoading,
                onFetchOptions: (params) => {
                    onFetchUsers({
                        ...params,
                        roles: [
                            'ADMIN',
                            'TECHGOV',
                            'WORKSPACE_ADMINISTRATOR',
                            'CONTROL_MANAGER',
                        ],
                        excludeReadOnlyUsers: true,
                    });
                },
                initialValue: this.approversCurrentValue,
            },
            deadlineDate: {
                type: 'date',
                label: this.isCurrentApprovalApproved
                    ? t`Next approval deadline`
                    : t`Approval deadline`,
                helpText: t`Set a deadline for when the control needs to be approved`,
                getIsDateUnavailable: this.isCurrentApprovalApproved
                    ? (date) => {
                          const futureDate = new Date();

                          futureDate.setDate(
                              futureDate.getDate() + MARGIN_DAYS,
                          );

                          return compareDayIsPast(date, futureDate);
                      }
                    : compareDayIsPast,
                initialValue: this.deadlineCurrentValue,
                ...(this.isCurrentApprovalApproved && {
                    shownIf: {
                        fieldName: 'skipNextDeadline',
                        operator: 'equals',
                        value: false,
                    },
                }),
            },
            ...(this.isCurrentApprovalApproved && {
                skipNextDeadline: {
                    type: 'checkbox',
                    label: t`Set approval deadline later`,
                    initialValue: false,
                },
            }),
        };
    }

    get currentApprovalStatusMetadata(): MetadataProps {
        const { currentControlApproval } = sharedControlApprovalsController;

        return {
            label: getStageTextLabel(currentControlApproval?.approvalStatus),
            colorScheme: getStageColor(currentControlApproval?.approvalStatus),
        };
    }
}

export const controlReviewCardModel = new ControlReviewCardModel();

import { sharedControlApprovalReviewersController } from '@controllers/controls';
import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import { EmptyState } from '@cosmos/components/empty-state';
import { Icon } from '@cosmos/components/icon';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import {
    StackedList,
    StackedListItem,
} from '@cosmos-lab/components/stacked-list';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getFullName, getInitials } from '@helpers/formatters';
import { sharedControlDetailsApproversActionsModel } from '@models/control-details';
import { AppLink } from '@ui/app-link';
import { controlReviewCardModel } from '../models/control-review-card.model';

export const ControlApprovalsReadComponent = observer((): React.JSX.Element => {
    const { actionsForCard } = sharedControlDetailsApproversActionsModel;
    const {
        isCurrentApprovalApproved,
        isCurrentApprovalEmpty,
        currentApprovalStatusMetadata,
        deadlineCurrentValue,
        hasApprovalWithoutOwners,
    } = controlReviewCardModel;

    const { controlApprovalsReviewers } =
        sharedControlApprovalReviewersController;

    if (isCurrentApprovalEmpty) {
        return (
            <EmptyState
                title={t`Required approvals`}
                description={t`Set up a process so readiness for this control will require specific people to approve it.`}
            />
        );
    }

    return (
        <Stack gap="3x" direction="column" data-id="p2Wq1Tjt">
            {hasApprovalWithoutOwners && (
                <Banner
                    title={t`This control needs an owner for required approvals. Assign a new owner to stay on track with this control.`}
                    severity="critical"
                />
            )}
            {isCurrentApprovalApproved && (
                <>
                    <Banner
                        title={t`Approvals and control readiness`}
                        severity="primary"
                        body={
                            <AppLink
                                isExternal
                                href="https://help.drata.com/en/articles/6188149-framework-readiness"
                            >
                                {t`Learn how approvals impact control readiness`}
                            </AppLink>
                        }
                    />
                    <Stack gap="2x" direction="row">
                        <Icon
                            name="Check"
                            backgroundType="round"
                            colorScheme="success"
                            size="300"
                        />
                        <Text size="400">
                            {currentApprovalStatusMetadata.label}
                        </Text>
                    </Stack>
                </>
            )}
            {!isCurrentApprovalApproved && (
                <Stack gap="2x" direction="row">
                    <KeyValuePair
                        type="BADGE"
                        label={t`Stage`}
                        value={currentApprovalStatusMetadata}
                    />
                    <KeyValuePair
                        type="TEXT"
                        label={t`Approval deadline`}
                        value={formatDate('sentence', deadlineCurrentValue)}
                    />
                </Stack>
            )}
            <StackedList>
                {controlApprovalsReviewers.map((reviewer) => (
                    <StackedListItem
                        key={reviewer.id}
                        data-id="Cx14w5u2"
                        primaryColumn={
                            <AvatarIdentity
                                secondaryLabel={reviewer.email}
                                imgSrc={reviewer.avatarUrl}
                                fallbackText={getInitials(
                                    getFullName(
                                        reviewer.firstName,
                                        reviewer.lastName,
                                    ),
                                )}
                                primaryLabel={getFullName(
                                    reviewer.firstName,
                                    reviewer.lastName,
                                )}
                            />
                        }
                    />
                ))}
            </StackedList>
            {isCurrentApprovalApproved && (
                <KeyValuePair
                    type="REACT_NODE"
                    label={t`Next approval deadline`}
                    value={
                        deadlineCurrentValue ? (
                            <Text>
                                {formatDate('sentence', deadlineCurrentValue)}
                            </Text>
                        ) : (
                            <EmptyValue label={t`No deadline set`} />
                        )
                    }
                />
            )}
            <ActionStack actions={actionsForCard} />
        </Stack>
    );
});

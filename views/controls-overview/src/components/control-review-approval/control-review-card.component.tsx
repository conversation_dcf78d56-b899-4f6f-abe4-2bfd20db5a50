import { useEffect, useRef } from 'react';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useFormSubmit } from '@ui/forms';
import { ControlApprovalsEditComponent } from './components/control-approvals-edit.component';
import { ControlApprovalsReadComponent } from './components/control-approvals-read.component';
import { controlReviewCardModel } from './models/control-review-card.model';

export const ControlsOverviewReviewCardComponent = observer(
    (): React.JSX.Element => {
        const { formRef, triggerSubmit } = useFormSubmit();
        const { isEditing, actions, shouldScrollIntoView } =
            controlReviewCardModel;

        const controlReviewRef = useRef<HTMLDivElement | null>(null);

        useEffect(() => {
            controlReviewCardModel.triggerSubmit = triggerSubmit;
        }, [triggerSubmit]);

        useEffect(() => {
            if (shouldScrollIntoView) {
                controlReviewRef.current?.scrollIntoView({
                    behavior: 'smooth',
                    block: 'end',
                });
            }
        }, [shouldScrollIntoView]);

        return (
            <Box
                gridRow="2"
                gridColumn="2"
                width="100%"
                data-id="mMACPn9w"
                ref={controlReviewRef}
            >
                <Card
                    title={t`Review and approval`}
                    isEditMode={isEditing}
                    actions={actions}
                    body={
                        isEditing ? (
                            <ControlApprovalsEditComponent formRef={formRef} />
                        ) : (
                            <ControlApprovalsReadComponent />
                        )
                    }
                />
            </Box>
        );
    },
);

import { useEffect, useRef, useState } from 'react';
import { AppDatatable } from '@components/app-datatable';
import { ControlPanel } from '@components/controls';
import { sharedControlDetailsOrchestratorController } from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { sharedRiskPartiallyMutationController } from '@controllers/risk';
import {
    sharedRiskDetailsController,
    sharedRiskMitigationControlsController,
} from '@controllers/risk-details';
import {
    type DatatableRef,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';
import { getRiskRegisterMitigatingControlsColumns } from '../constants/columns.constant';

export const RiskMitigationControlsTableComponent = observer(
    (): React.JSX.Element => {
        const { riskId } = useParams();
        const {
            isLoading: isLoadingRiskDetails,
            paginatedControls,
            loadControlsWithFreshPagination,
        } = sharedRiskDetailsController;

        const { isPending: isMappingControls } =
            sharedRiskPartiallyMutationController;

        // Show loading state when fetching risk details OR when mapping/unmapping controls
        const isLoading = isLoadingRiskDetails || isMappingControls;

        const {
            canMapControls,
            canUnmapControls,
            handleRowSelection,
            resetSelections,
        } = sharedRiskMitigationControlsController;

        // Feature flags for enhanced functionality
        const {
            isReleaseBulkImportRiskEnabled,
            isReleaseBulkImportControlsEnabled,
        } = sharedFeatureAccessModel;

        // Enable enhanced features based on feature flags
        const enableBulkOperations = isReleaseBulkImportControlsEnabled;
        const enableAdvancedRiskFeatures = isReleaseBulkImportRiskEnabled;

        const datatableRef = useRef<DatatableRef>(null);
        const [resetKey, setResetKey] = useState(0);

        // Reset pagination when component mounts (navigating to this tab)
        useEffect(() => {
            sharedRiskDetailsController.resetControlsPagination();
            setResetKey((prev) => prev + 1);
        }, []);

        const handleBulkOperationSuccess = (): void => {
            datatableRef.current?.resetRowSelection();
            resetSelections();
        };

        const handleMapControls = (): void => {
            if (!riskId) {
                return;
            }
            sharedRiskMitigationControlsController.handleMapControls(riskId);
        };

        const handleBulkUnmapControls = (): void => {
            if (!riskId) {
                return;
            }
            sharedRiskMitigationControlsController.handleBulkUnmapControls(
                riskId,
                handleBulkOperationSuccess,
            );
        };

        const getEmptyStateProps = () => {
            return {
                title: t`This risk is not mapped to any controls`,
                body: enableAdvancedRiskFeatures
                    ? t`Map controls to this risk to track mitigation efforts, compliance requirements, and advanced risk analytics.`
                    : t`Map controls to this risk to track mitigation efforts and compliance requirements.`,
                icon: 'activeControl',
                iconSize: 150,
                action: canMapControls
                    ? {
                          label: t`Map controls`,
                          onClick: handleMapControls,
                          level: 'secondary' as const,
                      }
                    : undefined,
            };
        };

        const handleRowClick = action(
            ({ row }: { row: RiskControlResponseDto }): void => {
                sharedControlDetailsOrchestratorController.load(row.id);
                panelController.openPanel({
                    id: 'risk-mitigation-control-panel',
                    content: () => (
                        <ControlPanel
                            controlSource="RISK_MITIGATION_CONTROLS"
                            data-id="risk-mitigation-control-panel-content"
                        />
                    ),
                });
            },
        );

        return (
            <AppDatatable
                isFullPageTable
                key={`risk-controls-table-${resetKey}`}
                isRowSelectionEnabled={canUnmapControls && enableBulkOperations}
                isLoading={isLoading}
                imperativeHandleRef={datatableRef}
                tableId="risk-register-mitigating-controls-datatable"
                total={paginatedControls.total}
                data={paginatedControls.data}
                columns={getRiskRegisterMitigatingControlsColumns()}
                getRowId={(row: RiskControlResponseDto) => row.id.toString()}
                data-id="iyYzVX4r"
                emptyStateProps={getEmptyStateProps()}
                defaultPaginationOptions={{
                    pageIndex: 0,
                    pageSize: DEFAULT_PAGE_SIZE,
                    pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                }}
                tableActions={
                    canMapControls && !isMappingControls
                        ? [
                              {
                                  actionType: 'button',
                                  id: 'map-controls-button',
                                  typeProps: {
                                      label: t`Map controls`,
                                      level: 'secondary',
                                      onClick: handleMapControls,
                                  },
                              },
                          ]
                        : []
                }
                bulkActionDropdownItems={
                    canUnmapControls &&
                    enableBulkOperations &&
                    !isMappingControls
                        ? [
                              {
                                  actionType: 'button',
                                  id: 'bulk-unmap-controls-button',
                                  typeProps: {
                                      label: t`Unmap selected controls`,
                                      level: 'tertiary',
                                      onClick: handleBulkUnmapControls,
                                  },
                              },
                          ]
                        : []
                }
                tableSearchProps={{
                    hideSearch: true,
                }}
                onFetchData={loadControlsWithFreshPagination}
                onRowSelection={handleRowSelection}
                onRowClick={handleRowClick}
            />
        );
    },
);

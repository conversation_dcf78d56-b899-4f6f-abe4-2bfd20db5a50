import { sharedRiskPartiallyMutationController } from '@controllers/risk';
import { sharedRiskMitigationControlsController } from '@controllers/risk-details';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { RiskControlResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';

export const ActionCell = observer(
    ({
        row: { original: control },
    }: {
        row: { original: RiskControlResponseDto };
    }): React.JSX.Element => {
        const { riskId } = useParams();

        const { canUnmapControls } = sharedRiskMitigationControlsController;
        const { isPending: isMappingControls } =
            sharedRiskPartiallyMutationController;

        const handleUnmapControl = () => {
            if (!canUnmapControls || !riskId) {
                return;
            }

            sharedRiskMitigationControlsController.handleUnmapControl(
                riskId,
                control,
            );
        };

        const isDisabled = !canUnmapControls || isMappingControls;
        const getTooltip = () => {
            if (!canUnmapControls) {
                return t`You don't have permission to unmap controls`;
            }
            if (isMappingControls) {
                return t`Please wait while controls are being mapped`;
            }

            return undefined;
        };

        const dropdownItems = [
            {
                id: 'unmap-control-option',
                label: t`Unmap control`,
                type: 'item' as const,
                value: 'unlink',
                onClick: handleUnmapControl,
                disabled: isDisabled,
                tooltip: getTooltip(),
            },
        ];

        return (
            <SchemaDropdown
                isIconOnly
                label={t`Actions`}
                level="tertiary"
                size="md"
                colorScheme="neutral"
                startIconName="Action"
                data-testid="ActionCell"
                data-id="xuanRTQW"
                items={dropdownItems}
            />
        );
    },
);

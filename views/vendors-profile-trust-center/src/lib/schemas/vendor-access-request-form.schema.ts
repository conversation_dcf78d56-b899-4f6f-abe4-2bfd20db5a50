import { isEmpty } from 'lodash-es';
import { z } from 'zod';
import { t } from '@globals/i18n/macro';
import type { FormSchema } from '@ui/forms';

// The unicode ranges cover Latin letters, Latin-1 Supplement, and Latin Extended-A
// Excludes multiplication (×) and division (÷) signs for security
const NAME_REGEX_STRING =
    '^[\u00DF-\u00F6\u00F8-\u017Fa-z0-9 |,./\'’\\-+()"&\\\\]';

// eslint-disable-next-line regexp/no-obscure-range -- this is used to match validation on safebase
const NAME_REGEX = new RegExp(`${NAME_REGEX_STRING}+$`, 'i');

const createNameValidator = (
    fieldName: string,
    minLength = 1,
    maxLength = 24,
) => {
    const unsupportedCharsMessage = t`You have entered unsupported characters for this field`;
    const requiredMessage = t`Enter ${fieldName}`;

    return z
        .string()
        .min(minLength, requiredMessage)
        .max(maxLength)
        .regex(NAME_REGEX, unsupportedCharsMessage)
        .refine((val) => !isEmpty(val.trim()), {
            message: requiredMessage,
        });
};

const createEmailValidator = () => {
    return z.string().min(1, t`Enter email`).email(t`This email is invalid`);
};

const createAgreementValidator = () => {
    return z.boolean().refine((val) => val, {
        message: t`You must agree to terms before submitting a request`,
    });
};

export const buildVendorAccessRequestFormSchema = (): FormSchema => {
    return {
        firstName: {
            type: 'text',
            label: t`First name`,
            initialValue: '',
            validator: createNameValidator('First name'),
        },
        lastName: {
            type: 'text',
            label: t`Last name`,
            initialValue: '',
            validator: createNameValidator('Last name'),
        },
        companyName: {
            type: 'text',
            label: t`Company name`,
            initialValue: '',
            validator: createNameValidator('Company name', 2, 100),
        },
        email: {
            type: 'text',
            label: t`Work email address`,
            initialValue: '',
            validator: createEmailValidator(),
        },
        agree: {
            type: 'checkbox',
            label: t`I have read and agree to SafeBase's Terms of Service and Privacy Notice.`,
            initialValue: false,
            validator: createAgreementValidator(),
        },
    };
};

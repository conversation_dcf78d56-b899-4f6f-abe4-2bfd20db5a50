import { noop } from 'lodash-es';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { Form, useFormSubmit } from '@ui/forms';
import { closeVendorAccessRequestModal } from '../helpers/vendor-access-request-modal.helper';
import { buildVendorAccessRequestFormSchema } from '../schemas/vendor-access-request-form.schema';

export const VendorAccessRequestModal = observer((): React.JSX.Element => {
    const { formRef, triggerSubmit } = useFormSubmit();
    const { vendorDetails } = sharedVendorsDetailsController;

    const vendorName = vendorDetails?.name ?? '';

    return (
        <>
            <Modal.Header
                title={t`Request access`}
                closeButtonAriaLabel={t`Close request access modal`}
                onClose={closeVendorAccessRequestModal}
            />
            <Modal.Body>
                <Stack gap="2xl" direction="column">
                    <Text type="title">
                        {t`Provide your information so ${vendorName} can review your access request.`}
                    </Text>

                    <Form
                        hasExternalSubmitButton
                        formId="vendor-access-request-form"
                        data-id="K7mN9pQx"
                        ref={formRef}
                        schema={buildVendorAccessRequestFormSchema()}
                        onSubmit={noop}
                    />

                    <Stack gap="md">
                        <AppLink
                            isExternal
                            size="sm"
                            href="https://drata.com/terms"
                        >
                            {t`Terms of Service`}
                        </AppLink>
                        <AppLink
                            isExternal
                            size="sm"
                            href="https://drata.com/privacy"
                        >
                            {t`Privacy Notice`}
                        </AppLink>
                    </Stack>
                </Stack>
            </Modal.Body>
            <Modal.Footer
                rightActionStack={[
                    {
                        label: t`Back`,
                        level: 'tertiary',
                        onClick: closeVendorAccessRequestModal,
                    },
                    {
                        label: t`Submit request`,
                        onClick: () => {
                            triggerSubmit();
                        },
                    },
                ]}
            />
        </>
    );
});

import { Button } from '@cosmos/components/button';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { openVendorAccessRequestModal } from '../helpers/vendor-access-request-modal.helper';

export const VendorAccessRequestButton = observer((): React.JSX.Element => {
    return (
        <Button
            label={t`Request access`}
            startIconName="Lock"
            data-testid="vendor-access-request-button"
            data-id="XFuaws4Z"
            onClick={openVendorAccessRequestModal}
        />
    );
});

import { Box } from '@cosmos/components/box';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Organization } from '@cosmos-lab/components/organization';
import type { VendorTrustCenterSubProcessorItemResponseDto } from '@globals/api-sdk/types';
import { getInitials } from '@helpers/formatters';
import { AppLink } from '@ui/app-link';

export const CompanyCell = ({
    row: { original },
}: {
    row: { original: VendorTrustCenterSubProcessorItemResponseDto };
}): React.JSX.Element => {
    const { name = '', logo = '', website } = original.company ?? {};

    return (
        <Stack
            gap="2x"
            align="center"
            data-testid="CompanyCell"
            data-id="b430fci-"
        >
            <Box>
                <Organization
                    data-id={`${name}-organization`}
                    fallbackText={getInitials(name)}
                    imgSrc={logo}
                    imgAlt={name}
                    size="xs"
                />
            </Box>
            {website ? (
                <Stack gap="1x">
                    <AppLink isExternal size="md" href={website}>
                        {name}
                    </AppLink>
                    <Icon name="SafeBase" size="200" />
                </Stack>
            ) : (
                <Text
                    shouldWrap={false}
                    data-id={`${name}-primary-label`}
                    type="title"
                    size="200"
                    as="div"
                >
                    {name}
                </Text>
            )}
        </Stack>
    );
};

import { modalController } from '@controllers/modal';
import { VendorAccessRequestModal } from '../components/vendor-access-request-modal.component';

const VENDOR_ACCESS_REQUEST_MODAL_ID = 'vendor-access-request-modal';

export const closeVendorAccessRequestModal = (): void => {
    modalController.closeModal(VENDOR_ACCESS_REQUEST_MODAL_ID);
};

export const openVendorAccessRequestModal = (): void => {
    modalController.openModal({
        id: VENDOR_ACCESS_REQUEST_MODAL_ID,
        content: () => <VendorAccessRequestModal data-id="dtvTuZ1Y" />,
        centered: true,
        disableClickOutsideToClose: true,
        size: 'lg',
    });
};

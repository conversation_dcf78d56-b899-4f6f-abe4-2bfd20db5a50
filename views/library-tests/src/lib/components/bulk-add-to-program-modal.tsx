import { isEmpty } from 'lodash-es';
import { useCallback, useState } from 'react';
import { sharedAddTestToProgramController } from '@controllers/add-test-to-program';
import { Banner } from '@cosmos/components/banner';
import { Box } from '@cosmos/components/box';
import { ComboboxField } from '@cosmos/components/combobox-field';
import { Feedback } from '@cosmos/components/feedback';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { ToggleGroup } from '@cosmos/components/toggle-group';
import type { LibraryTestTemplateResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { BulkAddToProgramValidateModal } from './bulk-add-to-program-validate-modal';

interface AddToProgramModalProps {
    onClose: () => void;
    selectedTemplates: LibraryTestTemplateResponseDto[];
    isAllRowsSelected: boolean;
    total: number;
}

interface Workspace {
    id: string;
    label: string;
    value: string;
}

export const BulkAddToProgramModal = observer(
    ({
        onClose,
        selectedTemplates,
        isAllRowsSelected,
        total,
    }: AddToProgramModalProps): React.JSX.Element => {
        const {
            selectedWorkspaces,
            availableWorkspaces,
            isMultiProduct,
            isWorkspacesStepValid,
            resetWorkspacesValidation,
            setSelectedWorkspaces,
        } = sharedAddTestToProgramController;

        const { primaryWorkspace } = sharedWorkspacesController;
        const [filteredOptions, setFilteredOptions] = useState<
            ListBoxItemData[]
        >(availableWorkspaces as ListBoxItemData[]);

        const [isDraft, setIsDraft] = useState(true);

        const selectedTestCount = isAllRowsSelected
            ? total
            : selectedTemplates.length;

        const handleToggleChange = (value: string | undefined) => {
            if (value !== undefined) {
                setIsDraft(value === 'draft');
            }
        };

        const handleChange = (options: ListBoxItemData[]) => {
            setSelectedWorkspaces(options as Workspace[]);
        };

        const handleConfirm = useCallback(async () => {
            // For multi-product case, validate workspace selection
            if (isMultiProduct && isEmpty(selectedWorkspaces)) {
                sharedAddTestToProgramController.hasAttemptedWorkspacesValidation = true;

                return;
            }

            const workspaces = isMultiProduct
                ? selectedWorkspaces
                : [
                      {
                          id: primaryWorkspace?.id.toString() ?? '',
                          label: primaryWorkspace?.name ?? '',
                          value: primaryWorkspace?.id.toString() ?? '',
                      },
                  ];

            resetWorkspacesValidation();

            await BulkAddToProgramValidateModal({
                onClose,
                selectedWorkspaces: workspaces,
                isDraft,
                selectedTemplates,
                isAllRowsSelected,
            });
        }, [
            isMultiProduct,
            selectedWorkspaces,
            primaryWorkspace?.id,
            primaryWorkspace?.name,
            resetWorkspacesValidation,
            onClose,
            isDraft,
            selectedTemplates,
            isAllRowsSelected,
        ]);

        const handleFetchOptions = useCallback(
            ({ search }: { search?: string }) => {
                if (search) {
                    const filtered = availableWorkspaces.filter((workspace) =>
                        workspace.label
                            .toLowerCase()
                            .includes(search.toLowerCase()),
                    );

                    setFilteredOptions(filtered as ListBoxItemData[]);
                } else {
                    setFilteredOptions(
                        availableWorkspaces as ListBoxItemData[],
                    );
                }
            },
            [availableWorkspaces],
        );

        return (
            <>
                <Modal.Header
                    title={t`Bulk add to program`}
                    closeButtonAriaLabel={t`Close bulk add to program modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Stack gap="2x" direction="column">
                        <Stack
                            gap="2x"
                            direction="row"
                            data-id="HvMWuoVp"
                            data-testid="BulkAddToProgramModal"
                        >
                            <Feedback
                                title={t`You are bulk adding ${selectedTestCount} tests`}
                                severity="primary"
                            />
                        </Stack>
                        {isMultiProduct && (
                            <ComboboxField
                                isMultiSelect
                                formId="buk-add-to-program-form"
                                name="workspaces"
                                label={t`Choose workspaces`}
                                placeholder={t`Search workspaces`}
                                loaderLabel={t`Loading workspaces...`}
                                removeAllSelectedItemsLabel={t`Clear all`}
                                data-testid="bulk-add-to-program-workspaces-combobox"
                                data-id="bulk-add-to-program-workspaces-combobox"
                                options={filteredOptions}
                                searchDebounce={300}
                                onFetchOptions={handleFetchOptions}
                                onChange={handleChange}
                            />
                        )}
                        {!isWorkspacesStepValid && (
                            <Banner
                                displayMode="section"
                                severity="critical"
                                title={''}
                                body={t`At least one workspace must be selected to continue`}
                                data-testid="workspaces-validation-banner"
                                data-id="workspaces-validation-banner"
                            />
                        )}
                        <Stack gap="2x" direction="column">
                            <Text type="body" colorScheme="neutral">
                                {t`Configure the test type to start running
                                Drata's automation. Test in a draft state
                                will not impact your readiness.`}
                            </Text>
                            <Text type="title" colorScheme="neutral">
                                {t`Bulk apply test type`}
                            </Text>
                            <Box gridArea="toggle">
                                <ToggleGroup
                                    data-id="draft-published-toggle"
                                    data-testid="DraftPublishedToggle"
                                    initialSelectedOption={
                                        isDraft ? 'draft' : 'published'
                                    }
                                    options={[
                                        {
                                            label: t`Draft`,
                                            value: 'draft',
                                        },
                                        {
                                            label: t`Published`,
                                            value: 'published',
                                        },
                                    ]}
                                    onChange={handleToggleChange}
                                />
                            </Box>
                        </Stack>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'tertiary',
                            onClick: onClose,
                        },
                        {
                            label: t`Add`,
                            level: 'primary',
                            onClick: () => {
                                handleConfirm().catch((error: Error) => {
                                    console.error(
                                        'Error in handleConfirm:',
                                        error,
                                    );
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);

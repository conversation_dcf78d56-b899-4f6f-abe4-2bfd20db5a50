import { AppDatatable } from '@components/app-datatable';
import { libraryTestTemplatesController } from '@controllers/library-tests';
import { routeController } from '@controllers/route';
import type { LibraryTestTemplateResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { getLibraryTestTemplateBulkActionDropdownItems } from '../constants/library-test-template-bulk-action.constants';
import { LIBRARY_TEST_TEMPLATE_COLUMNS } from '../constants/library-test-template-data-table.constants';

export const LibraryTestTemplateView = observer((): React.JSX.Element => {
    const {
        getTestTemplates,
        isLoading,
        getTotal,
        loadTestTemplates,
        selectedTemplates,
        handleRowSelection,
        isAllRowsSelected,
        filterProps,
    } = libraryTestTemplatesController;

    const navigate = useNavigate();

    const handleRowClick = ({
        row,
    }: {
        row: LibraryTestTemplateResponseDto;
    }) => {
        navigate(
            `${routeController.userPartOfUrl}/library/tests/${row.testId}/overview`,
        );
    };

    return (
        <AppDatatable<LibraryTestTemplateResponseDto>
            isFullPageTable
            isRowSelectionEnabled
            isLoading={isLoading}
            tableId="library-test-datatable"
            data-id="library-test-datatable"
            columns={LIBRARY_TEST_TEMPLATE_COLUMNS}
            total={getTotal}
            filterProps={filterProps}
            data={getTestTemplates}
            getRowId={(row: LibraryTestTemplateResponseDto) =>
                String(row.templateId)
            }
            bulkActionDropdownItems={getLibraryTestTemplateBulkActionDropdownItems(
                selectedTemplates,
                isAllRowsSelected,
                getTotal,
            )}
            tableSearchProps={{
                hideSearch: false,
                placeholder: t`Search by name or description`,
                debounceDelay: 500,
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'pinned',
                    initialSelectedOption: 'pinned',
                    togglePinnedLabel: t`Pin filters to page`,
                    toggleUnpinnedLabel: t`Move filters to dropdown`,
                },
                viewMode: 'toggleable',
            }}
            emptyStateProps={{
                illustrationName: `Warning`,
                title: t`Tests Library`,
                description: t`No test templates were found`,
            }}
            onFetchData={loadTestTemplates}
            onRowSelection={handleRowSelection}
            onRowClick={handleRowClick}
        />
    );
});

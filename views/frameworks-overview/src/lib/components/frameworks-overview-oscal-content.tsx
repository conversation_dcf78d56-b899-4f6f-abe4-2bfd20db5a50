// eslint-disable-next-line no-restricted-imports -- this is a case for which this library was added
import type { FormikProps, FormikValues } from 'formik';
import { isError } from 'lodash-es';
import { useCallback, useMemo, useRef, useState } from 'react';
import {
    OscalRequirementDetailsForm,
    OscalRequirementDetailsReadOnly,
} from '@components/frameworks';
import { sharedFrameworkDetailsController } from '@controllers/frameworks';
import { sharedOscalRequirementsController } from '@controllers/oscal-requirements';
import { snackbarController } from '@controllers/snackbar';
import type { Action } from '@cosmos/components/action-stack';
import { Card } from '@cosmos/components/card';
import { useLingui } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { observer, when } from '@globals/mobx';

export const FrameworksOverviewOscalContent = observer(
    (): JSX.Element | null => {
        const { t } = useLingui();

        const [isEdit, setIsEdit] = useState(false);

        const { frameworkDetails } = sharedFrameworkDetailsController;

        const handleCancel = useCallback(() => {
            setIsEdit(false);
        }, []);

        const submitImperativeHandlerRef = useRef<
            ((e?: React.FormEvent<HTMLFormElement>) => void) | undefined
        >();

        const actions: Action[] = useMemo(() => {
            if (sharedOscalRequirementsController.isRequirementReadOnly) {
                return [];
            }

            if (!isEdit) {
                return [
                    {
                        id: 'view-edit-card-edit-btn',
                        actionType: 'button',
                        typeProps: {
                            label: t`Edit`,
                            'data-id': 'view-edit-card-edit-btn',
                            onClick: () => {
                                setIsEdit(true);
                            },
                            level: 'secondary',
                        },
                    },
                ];
            }

            return [
                {
                    id: 'view-edit-card-save-btn',
                    actionType: 'button',
                    typeProps: {
                        label: t`Save`,
                        'data-id': 'view-edit-card-save-btn',
                        onClick: () => {
                            submitImperativeHandlerRef.current?.();
                        },
                    },
                },
                {
                    id: 'view-edit-card-cancel-btn',
                    actionType: 'button',
                    typeProps: {
                        label: t`Cancel`,
                        'data-id': 'view-edit-card-cancel-btn',
                        onClick: handleCancel,
                        level: 'secondary',
                    },
                },
            ];
        }, [handleCancel, isEdit, t]);

        const handleSubmit = useCallback(
            (
                values: {
                    paramId: string;
                    selectedValues: string[];
                }[],
            ) => {
                try {
                    sharedOscalRequirementsController.updateParamValues(values);

                    when(
                        () => !sharedOscalRequirementsController.isUpdating,
                        () => {
                            setIsEdit(false);
                        },
                    );
                } catch (error) {
                    snackbarController.addSnackbar({
                        id: 'oscal-requirement-update-error',
                        props: {
                            title: t`Failed to update requirement`,
                            description: t`An error occurred while updating the requirement. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });

                    logger.error({
                        message:
                            'Failed to update Oscal requirement parameters',
                        additionalInfo: {
                            values,
                        },
                        errorObject: {
                            message: isError(error)
                                ? error.message
                                : String(error),
                            statusCode: '500',
                        },
                    });
                }
            },
            [t],
        );

        const innerRefCallback = useCallback(
            (ref: FormikProps<FormikValues> | null) => {
                submitImperativeHandlerRef.current = ref?.handleSubmit;
            },
            [],
        );

        if (!frameworkDetails?.tag) {
            return null;
        }

        return (
            <Card
                title={t`Overview`}
                size="lg"
                actions={actions}
                isEditMode={isEdit}
                isLoading={sharedOscalRequirementsController.isUpdating}
                data-testid="ViewEditCardComponent"
                data-id="oudFIgCW"
                cardHeight="fit-content"
                body={
                    isEdit ? (
                        <OscalRequirementDetailsForm
                            frameworkTag={frameworkDetails.tag}
                            data-testid="OscalRequirementDetailsForm"
                            innerRef={innerRefCallback}
                            onSubmit={handleSubmit}
                        />
                    ) : (
                        <OscalRequirementDetailsReadOnly
                            frameworkTag={frameworkDetails.tag}
                            data-testid="OscalRequirementDetailsReadOnly"
                        />
                    )
                }
            />
        );
    },
);

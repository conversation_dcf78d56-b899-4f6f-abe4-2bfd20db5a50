import { useMemo } from 'react';
import {
    sharedRiskInsightsController,
    sharedRiskSettingsController,
} from '@controllers/risk';
import { Card } from '@cosmos/components/card';
import { NoComponent } from '@cosmos/components/no-component';
import { Skeleton } from '@cosmos/components/skeleton';
import { DataPosture } from '@cosmos-lab/components/data-posture';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { generateCategoryBreakdownBoxes } from './helper/category-breakdown.helper';

export const CategoryBreakdownCard = observer((): React.JSX.Element => {
    const { riskInsights, isLoading } = sharedRiskInsightsController;
    const { riskSettings } = sharedRiskSettingsController;

    const { categoryBreakdown = [] } = riskInsights ?? {};
    const { thresholds = [] } = riskSettings ?? {};

    const boxesPerCategory = useMemo(() => {
        return generateCategoryBreakdownBoxes(categoryBreakdown, thresholds);
    }, [categoryBreakdown, thresholds]);

    return (
        <Card
            title={'Category Breakdown'}
            tooltipText={'Category Breakdown'}
            data-testid="CategoryBreakdownCard"
            data-id="SuFqJEr7"
            body={
                isLoading ? (
                    <Skeleton barCount={5} />
                ) : (
                    <>
                        {boxesPerCategory.map(({ category, boxes }) => {
                            return (
                                <div key={category} data-id="ri-JARNE">
                                    <AppLink
                                        size="sm"
                                        href="#"
                                        label={category}
                                    />
                                    <DataPosture
                                        legend
                                        key={category}
                                        boxes={boxes}
                                        data-id="Cx1ET0qR"
                                    />
                                </div>
                            );
                        })}
                        <NoComponent ticketLink="https://drata.atlassian.net/browse/ENG-53885" />
                    </>
                )
            }
            actions={[
                {
                    actionType: 'button',
                    id: 'download-category-breakdown-button',
                    typeProps: {
                        isIconOnly: true,
                        label: 'Download',
                        startIconName: 'Download',
                        level: 'tertiary',
                        cosmosUseWithCaution_isDisabled: isLoading,
                    },
                },
            ]}
        />
    );
});

import { isObject, isString, noop } from 'lodash-es';
import { sharedRiskInsightsController } from '@controllers/risk';
import type { FilterState } from '@cosmos/components/filter-field';
import { Filters } from '@cosmos/components/filters';
import type { RiskManagementControllerGetDashboardData } from '@globals/api-sdk/types';
import { action, observer, toJS } from '@globals/mobx';
import { sharedRiskInsightsCategoriesFilterModel } from './risk-insights.categories-filter.model';

const transformFilterStateToApiParams = (
    filterState: FilterState,
): Partial<RiskManagementControllerGetDashboardData['query']> => {
    const { id, value } = filterState;

    if (!value) {
        return {};
    }

    switch (id) {
        case 'categoriesIds': {
            if (Array.isArray(value)) {
                const categoriesIds = (value as { value: string }[]).map(
                    (item) => Number(item.value),
                );

                return { categoriesIds };
            }

            return {};
        }
        case 'ownersIds': {
            if (isObject(value) && 'value' in value) {
                const ownersIds = [Number((value as { value: string }).value)];

                return { ownersIds };
            }

            return {};
        }
        case 'riskFilter': {
            if (isString(value)) {
                return {
                    riskFilter: value as 'INTERNAL_ONLY' | 'EXTERNAL_ONLY',
                };
            }

            return {};
        }
        case 'status': {
            if (Array.isArray(value)) {
                return {
                    'status[]': value as ('ACTIVE' | 'ARCHIVED' | 'CLOSED')[],
                };
            }

            return {};
        }
        default: {
            return {};
        }
    }
};

export const RiskInsightsFilters = observer((): React.JSX.Element => {
    const { tableFilters, filterValues, setFilterValue, clearAllFilters } =
        sharedRiskInsightsCategoriesFilterModel;

    const filters = toJS(tableFilters);
    const filterValuesCopy = toJS(filterValues);

    const handleFilterChange = action((filterState: FilterState): void => {
        const apiParams = transformFilterStateToApiParams(filterState);

        setFilterValue(filterState);
        sharedRiskInsightsController.load(apiParams);
    });

    const handleClearAll = action((): void => {
        clearAllFilters();
        sharedRiskInsightsController.clearAllFilters();
    });

    return (
        //TODO: PINNED FILTER -> https://drata.atlassian.net/browse/ENG-64250
        <Filters
            showViewModeToggle
            initialIsOpen
            clearAllButtonLabel="Clear all"
            data-id="cosmos-filters"
            filters={filters}
            formId="filters"
            triggerId="cosmos-filters-trigger"
            triggerLabel="Filters"
            data-testid="RiskInsightsFilters"
            filterValues={filterValuesCopy}
            viewModeToggleProps={{
                onChange: noop,
                selectedOption: 'pinned',
                togglePinnedLabel: 'Pin filters to the page',
                toggleUnpinnedLabel: 'Move filters to a dropdown',
            }}
            onChange={handleFilterChange}
            onClearAll={handleClearAll}
        />
    );
});

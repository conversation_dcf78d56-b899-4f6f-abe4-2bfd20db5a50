import { RiskInsightsOverTimeChart } from '@components/risk-insights-over-time-chart';
import { getPeriodLabel } from '@controllers/risk-insights-over-time';
import { Card } from '@cosmos/components/card';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { sharedRiskInsightsOverTimeModel } from '@models/risk-insights-over-time';

export const RisksOverTimeCard = observer((): React.JSX.Element => {
    const { controller, periodOptions, isValidPeriod } =
        sharedRiskInsightsOverTimeModel;

    const handlePeriodChange = (value: string) => {
        if (isValidPeriod(value)) {
            runInAction(() => {
                controller.setPeriod(value);
            });
        }
    };

    const handleDownload = () => {
        const { data } = controller;

        if (!data) {
            return;
        }

        // TODO: Implement CSV export when needed
        // const csv = generateCSV(data);
        // downloadFile(csv, `risk-trends-${data.period}.csv`);
    };

    return (
        <Card
            title={t`Risks Over Time`}
            tooltipText={t`Risks over time`}
            data-testid="RisksOverTimeCard"
            data-id="irCH46rU"
            body={<RiskInsightsOverTimeChart />}
            actions={[
                {
                    actionType: 'button',
                    id: 'download-risk-over-time-button',
                    typeProps: {
                        isIconOnly: true,
                        label: t`Download`,
                        startIconName: 'Download',
                        level: 'tertiary',
                        onClick: handleDownload,
                    },
                },
                {
                    actionType: 'dropdown',
                    id: 'action-3',
                    typeProps: {
                        items: [
                            {
                                id: 'actions-group',
                                type: 'group',
                                label: '',
                                items: periodOptions.map((option) => ({
                                    id: option.id,
                                    label: option.label,
                                    type: 'item' as const,
                                    value: option.value,
                                })),
                            },
                        ],
                        label: getPeriodLabel(controller.period),
                        level: 'secondary',
                        endIconName: 'ChevronDown',
                        onSelectGlobalOverride: (item) => {
                            // The dropdown can pass either { id } or { value }
                            const itemId = item.id;
                            const itemValue = (item as { value?: string })
                                .value;

                            if (itemValue && isValidPeriod(itemValue)) {
                                handlePeriodChange(itemValue);
                            } else if (itemId) {
                                const option = periodOptions.find(
                                    (opt) => opt.id === itemId,
                                );

                                if (option) {
                                    handlePeriodChange(option.value);
                                }
                            }
                        },
                    },
                },
            ]}
        />
    );
});

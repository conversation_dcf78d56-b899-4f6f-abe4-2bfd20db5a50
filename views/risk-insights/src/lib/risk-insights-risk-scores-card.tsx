import { useMemo } from 'react';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Text } from '@cosmos/components/text';
import {
    ToggleGroup,
    type ToggleGroupOption,
} from '@cosmos/components/toggle-group';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedRiskInsightsScoreTypeModel } from '@models/risk-insights';
import { CategoryBreakdownCard } from './risk-insights-category-breakdown-card';
import { HeatmapCard } from './risk-insights-heatmap-card';
import { PostureCard } from './risk-insights-posture-card';
import { RisksOverTimeCard } from './risk-insights-risks-over-time-card';

const handleToggleChange = (value?: string) => {
    if (!value) {
        return;
    }

    if (value === 'INHERENT' || value === 'RESIDUAL') {
        sharedRiskInsightsScoreTypeModel.scoreType = value;
    }
};

export const RiskScoresCard = observer((): React.JSX.Element => {
    const options = useMemo(
        (): ToggleGroupOption[] => [
            {
                label: t`Inherent`,
                value: 'INHERENT',
            },
            {
                label: t`Residual`,
                value: 'RESIDUAL',
            },
        ],
        [],
    );

    return (
        <Card
            title={t`Risk Scores`}
            tooltipText={t`Risk scores`}
            data-testid="RiskScoresCard"
            data-id="ziH9WCGy"
            body={
                <>
                    <Grid
                        areas='"empty label toggle"'
                        columns="1fr auto auto"
                        gapX="md"
                        align="center"
                    >
                        <Box gridArea="toggle">
                            <ToggleGroup
                                options={options}
                                orientation="horizontal"
                                initialSelectedOption={
                                    sharedRiskInsightsScoreTypeModel.scoreType
                                }
                                onChange={handleToggleChange}
                            />
                        </Box>
                        <Box gridArea="label">
                            <Text>
                                {t`Inherent risk is displayed if no residual score
                                is available`}
                            </Text>
                        </Box>
                    </Grid>
                    <Grid
                        areas='"posture" "risksOverTime" "gridArea"'
                        columns="1"
                        gapX="2xl"
                        gapY="3xl"
                    >
                        <Box gridArea="posture">
                            <PostureCard />
                        </Box>
                        <Box gridArea="risksOverTime">
                            <RisksOverTimeCard />
                        </Box>
                        <Grid
                            areas='"heatmap categoryBreakdown"'
                            columns="2"
                            gapX="2xl"
                            gapY="3xl"
                            gridArea="gridArea"
                        >
                            <Box gridArea="heatmap">
                                <HeatmapCard />
                            </Box>
                            <Box gridArea="categoryBreakdown">
                                <CategoryBreakdownCard />
                            </Box>
                        </Grid>
                    </Grid>
                </>
            }
        />
    );
});

import { isEmpty } from 'lodash-es';
import { TextBreakLine } from '@components/text-break-line';
import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Metadata } from '@cosmos/components/metadata';
import { PanelBody } from '@cosmos/components/panel';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import { PaginationControls } from '@cosmos-lab/components/pagination-controls';
import { ShowMore } from '@cosmos-lab/components/show-more';
import { Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import {
    getFrameworkRequirementAdditionalInfo1,
    getFrameworkRequirementAdditionalInfo2,
    getFrameworkRequirementAdditionalInfo3,
    getFrameworkRequirementDescription,
    getFrameworkRequirementName,
    shouldDisplayDescription,
} from '../constants/framework-label.constant';
import { sharedRequirementDetailsModel } from '../models/requirement-details.model';
import type { RequirementDetailsProps } from '../types/requirement-details-props.type';
import { AccordionControlsComponent } from './accordion-controls.component';

export const RequirementDetailsBodyPanelComponent = observer(
    ({
        requirement,
        frameworkSlug = 'none',
    }: RequirementDetailsProps): React.JSX.Element => {
        const {
            controlsIsLoading,
            totalMappedControls,
            controls,
            defaultMappedControlsPageSize,
            requirementsDetailsPanelPaginationControlsTestId,
            mappedControlsPaginationOnPageChange,
        } = sharedRequirementDetailsModel;

        const shouldDisplayNameSection =
            shouldDisplayDescription(frameworkSlug);

        const headerRequirementName =
            getFrameworkRequirementName(frameworkSlug);

        const headerRequirementDescription =
            getFrameworkRequirementDescription(frameworkSlug);

        const headerRequirementAdditionalInfo1 =
            getFrameworkRequirementAdditionalInfo1(frameworkSlug);

        const headerRequirementAdditionalInfo2 =
            getFrameworkRequirementAdditionalInfo2(frameworkSlug);

        const headerRequirementAdditionalInfo3 =
            getFrameworkRequirementAdditionalInfo3(frameworkSlug);

        return (
            <PanelBody
                data-testid="RequirementDetailsBodyPanelComponent"
                data-id="EskUU3"
            >
                <Grid gap="3xl">
                    <Stack direction="column" gap="2xl">
                        <Text type="title" size="400">
                            <Trans>Requirement details</Trans>
                        </Text>
                        <Stack direction="column" gap="xl">
                            {shouldDisplayNameSection && (
                                <KeyValuePair
                                    label={headerRequirementName}
                                    value={requirement.description}
                                />
                            )}
                            {!isEmpty(requirement.longDescription) && (
                                <KeyValuePair
                                    type="REACT_NODE"
                                    label={headerRequirementDescription}
                                    value={
                                        <TextBreakLine
                                            text={requirement.longDescription}
                                        />
                                    }
                                />
                            )}
                            {(requirement.additionalInfo ||
                                requirement.additionalInfo2 ||
                                requirement.additionalInfo3) && (
                                <Box>
                                    <ShowMore
                                        content={
                                            <Stack direction="column" gap="2x">
                                                {requirement.additionalInfo && (
                                                    <KeyValuePair
                                                        type="REACT_NODE"
                                                        label={
                                                            headerRequirementAdditionalInfo1
                                                        }
                                                        value={
                                                            <TextBreakLine
                                                                text={
                                                                    requirement.additionalInfo
                                                                }
                                                            />
                                                        }
                                                    />
                                                )}
                                                {requirement.additionalInfo2 && (
                                                    <KeyValuePair
                                                        type="REACT_NODE"
                                                        label={
                                                            headerRequirementAdditionalInfo2 ===
                                                            headerRequirementAdditionalInfo1
                                                                ? ''
                                                                : headerRequirementAdditionalInfo2
                                                        }
                                                        value={
                                                            <TextBreakLine
                                                                text={
                                                                    requirement.additionalInfo2
                                                                }
                                                            />
                                                        }
                                                    />
                                                )}
                                                {requirement.additionalInfo3 && (
                                                    <KeyValuePair
                                                        type="REACT_NODE"
                                                        label={
                                                            headerRequirementAdditionalInfo3 ===
                                                            headerRequirementAdditionalInfo1
                                                                ? ''
                                                                : headerRequirementAdditionalInfo3
                                                        }
                                                        value={
                                                            <TextBreakLine
                                                                text={
                                                                    requirement.additionalInfo3
                                                                }
                                                            />
                                                        }
                                                    />
                                                )}
                                            </Stack>
                                        }
                                    />
                                </Box>
                            )}
                        </Stack>
                    </Stack>
                    <Divider />
                    <Stack direction="column" gap="2xl">
                        <Stack gap="2x">
                            <Text type="title" size="400">
                                <Trans>Mapped controls</Trans>
                            </Text>
                            <Metadata
                                colorScheme="neutral"
                                type="tag"
                                label={
                                    controlsIsLoading
                                        ? '-'
                                        : `${totalMappedControls}`
                                }
                            />
                        </Stack>
                        <>
                            {controlsIsLoading && (
                                <Skeleton barCount={10} barHeight="56px" />
                            )}
                            {!controlsIsLoading && (
                                <Stack direction="column" gap="xl">
                                    {controls.map((control) => {
                                        return (
                                            <AccordionControlsComponent
                                                data-id={`${control.id}-acc`}
                                                key={control.id}
                                                isReady={control.isReady}
                                                id={control.id}
                                                name={control.name}
                                                description={
                                                    control.description
                                                }
                                            />
                                        );
                                    })}
                                </Stack>
                            )}
                        </>
                        <PaginationControls
                            hidePageSizeOptions
                            total={totalMappedControls}
                            pageSize={defaultMappedControlsPageSize}
                            data-id={
                                requirementsDetailsPanelPaginationControlsTestId
                            }
                            onPageChange={mappedControlsPaginationOnPageChange}
                        />
                    </Stack>
                </Grid>
            </PanelBody>
        );
    },
);

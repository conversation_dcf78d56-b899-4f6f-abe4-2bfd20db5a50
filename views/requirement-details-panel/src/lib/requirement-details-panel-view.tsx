import { isEmpty } from 'lodash-es';
import { useCallback } from 'react';
import { sharedControlFrameworksController } from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { PanelControls } from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import type { RequirementWithControlsShortListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { RequirementDetailsBodyOscalPanelComponent } from '../components/requirement-details-body-oscal-panel.component';
import { RequirementDetailsBodyPanelComponent } from '../components/requirement-details-body-panel.component';
import { RequirementDetailsHeaderPanelComponent } from '../components/requirement-details-header-panel.component';
import { openRequirementDetailsPanelPaginated } from '../helpers/open-panel.helper';
import { sharedRequirementDetailsModel } from '../models/requirement-details.model';

export const RequirementDetailsPanelView = observer((): React.JSX.Element => {
    const paginatePanelContent = useCallback(
        (requirementId: number, offset: number) => {
            openRequirementDetailsPanelPaginated(requirementId, offset, () => (
                <RequirementDetailsPanelView
                    data-id={`${requirementId}-panel`}
                />
            ));
        },
        [],
    );
    const { queryParams } = panelController;
    const frameworkSlug =
        queryParams.frameworkSlug as RequirementWithControlsShortListResponseDto['frameworkSlug'];
    const { allControlFrameworks } = sharedControlFrameworksController;

    const framework = allControlFrameworks.find(
        (f) => f.frameworkSlug === frameworkSlug,
    );
    const frameworkTag = framework?.frameworkTag;

    const { panelIsLoading, requirement } = sharedRequirementDetailsModel;

    if (panelIsLoading || !requirement) {
        return (
            <Stack align="center" justify="center" height="100%" width="100%">
                <Loader size="lg" label={t`Loading...`} />
            </Stack>
        );
    }

    const currentIndex =
        allControlFrameworks.findIndex((c) => c.id === requirement.id) + 1;

    return (
        <Grid data-testid="RequirementDetailsPanelContent" data-id="JZfrAprn">
            <PanelControls
                closeButtonLabel={t`Close`}
                pagination={{
                    currentItem: currentIndex,
                    onNextPageClick: () => {
                        paginatePanelContent(requirement.id, 1);
                    },
                    onPrevPageClick: () => {
                        paginatePanelContent(requirement.id, -1);
                    },
                    totalItems: allControlFrameworks.length,
                }}
                onClose={() => {
                    panelController.closePanel();
                }}
            />
            <RequirementDetailsHeaderPanelComponent requirement={requirement} />
            {isEmpty(requirement.parts) ? (
                <RequirementDetailsBodyPanelComponent
                    requirement={requirement}
                    frameworkSlug={frameworkSlug}
                />
            ) : (
                <RequirementDetailsBodyOscalPanelComponent
                    requirement={requirement}
                    frameworkTag={frameworkTag}
                />
            )}
        </Grid>
    );
});

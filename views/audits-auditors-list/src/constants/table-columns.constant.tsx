import type { DatatableProps } from '@cosmos/components/datatable';
import type { AuditorWithAuditsResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ApplicationAccessCell } from '../components/application-access-cell.component';
import { AuditorCell } from '../components/auditor-cell.component';

export const AUDITORS_LIST_INITIAL_SORTING = [
    {
        id: 'auditor',
        desc: false,
    },
];

export const getAuditorListTableColumns =
    (): DatatableProps<AuditorWithAuditsResponseDto>['columns'] => [
        {
            accessorKey: 'firstName',
            header: t`Auditor`,
            id: 'auditor',
            enableSorting: true,
            cell: AuditorCell,
        },
        {
            accessorKey: 'firmName',
            header: t`Firm`,
            id: 'firmName',
            enableSorting: true,
        },
        {
            accessorKey: 'id',
            header: t`Application access`,
            id: 'applicationAccess',
            enableSorting: false,
            cell: ApplicationAccessCell,
        },
    ];

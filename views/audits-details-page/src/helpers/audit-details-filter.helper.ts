import type { FilterProps } from '@cosmos/components/datatable';
import type { UserResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getFullName } from '@helpers/formatters';

export const getFilterProps = (
    totalUnreadMessages: number,
    requestOwners: UserResponseDto[] = [],
): FilterProps => {
    const messagesLabel =
        totalUnreadMessages > 0 ? t`Unread: ${totalUnreadMessages}` : t`Unread`;

    // Transform users into combobox options
    const ownerOptions = requestOwners.map((user) => ({
        id: `${user.id}`,
        label: getFullName(user.firstName, user.lastName),
        value: `${user.id}`,
    }));

    return {
        clearAllButtonLabel: t`Reset`,
        filters: [
            {
                filterType: 'checkbox',
                id: 'onlyWithNewMessages',
                label: t`Messages`,
                options: [
                    {
                        label: messagesLabel,
                        value: 'true',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'status',
                label: t`Status`,
                options: [
                    {
                        label: t`Completed`,
                        value: 'ACCEPTED',
                    },
                    {
                        label: t`Prepared`,
                        value: 'IN_REVIEW',
                    },
                    {
                        label: t`New`,
                        value: 'OUTSTANDING',
                    },
                ],
            },
            {
                filterType: 'radio',
                id: 'isOwned',
                label: t`Request owners`,
                options: [
                    {
                        label: t`Owners assigned`,
                        value: 'true',
                    },
                    {
                        label: t`No owners assigned`,
                        value: 'false',
                    },
                ],
            },
            {
                filterType: 'combobox',
                id: 'filterByUserIds',
                label: t`Filter by request owner`,
                isMultiSelect: true,
                options: ownerOptions,
            },
        ],
        triggerLabel: t`Filters`,
    };
};

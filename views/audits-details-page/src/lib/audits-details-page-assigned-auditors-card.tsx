import { isEmpty } from 'lodash-es';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { Button } from '@cosmos/components/button';
import { Card } from '@cosmos/components/card';
import { EmptyState } from '@cosmos/components/empty-state';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AssignedAuditorsCardBody } from './assigned-auditors-card-body';

export const AssignedAuditorsCard = observer((): React.JSX.Element => {
    const { auditByIdData } = sharedAuditHubController;

    if (isEmpty(auditByIdData?.auditors)) {
        return (
            <EmptyState
                illustrationName="AddCircle"
                imageSize="sm"
                title={t`No auditors assigned yet`}
                leftAction={<Button label={t`Assign now`} level="secondary" />}
            />
        );
    }

    return (
        <Card
            title={t`Assigned auditors`}
            data-testid="AssignedAuditorsCard"
            data-id="6oaqRAzI"
            body={<AssignedAuditorsCardBody />}
            actions={[
                {
                    actionType: 'button',
                    id: 'add-auditor-action-button',
                    typeProps: {
                        label: t`Edit`,
                        level: 'secondary',
                    },
                },
            ]}
        />
    );
});

import { AppDatatable } from '@components/app-datatable';
import { sharedAuditHubController } from '@controllers/audit-hub';
import { sharedAuditorController } from '@controllers/auditor';
import { sharedCustomerRequestsController } from '@controllers/customer-requests';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { getAuditorsListTableColumns } from '../constants/audits-details-page.constants';
import { getFilterProps } from '../helpers/audit-details-filter.helper';

export const AuditDetailsRequestTable = observer((): React.JSX.Element => {
    const navigate = useNavigate();
    const {
        customerRequests,
        isLoading,
        total,
        getCustomerRequestList,
        totalUnreadMessages,
    } = sharedCustomerRequestsController;

    const { auditSummaryByIdData } = sharedAuditorController;
    const { customRequestOwners } = sharedAuditHubController;
    const navigateToRequestDetail = (requestId: number) => {
        navigate(
            `/compliance/audits/${auditSummaryByIdData?.auditorFrameworkId}/request/${requestId}/details`,
        );
    };

    return (
        <AppDatatable
            isFullPageTable
            isRowSelectionEnabled
            getRowId={(row) => String(row.id)}
            isLoading={isLoading}
            tableId="datatable-audit-details-requests"
            total={total}
            data={customerRequests}
            columns={getAuditorsListTableColumns()}
            data-testid="AuditDetailsRequestTable"
            data-id="ojxb0h8q"
            filterProps={getFilterProps(
                totalUnreadMessages,
                customRequestOwners,
            )}
            tableSearchProps={{
                hideSearch: false,
                placeholder: t`Search`,
                defaultValue: '',
            }}
            filterViewModeProps={{
                props: {
                    selectedOption: 'unpinned',
                    initialSelectedOption: 'unpinned',
                    togglePinnedLabel: t`Pin filters to page`,
                    toggleUnpinnedLabel: t`Move filters to dropdown`,
                },
                viewMode: 'toggleable',
            }}
            emptyStateProps={{
                title: t`We couldn't find any matches`,
                description: t`Try expanding your filter or search criteria.`,
            }}
            onFetchData={getCustomerRequestList}
            onRowClick={({ row }) => {
                navigateToRequestDetail(row.id);
            }}
        />
    );
});

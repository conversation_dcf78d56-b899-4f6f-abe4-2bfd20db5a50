import { sharedAuditor<PERSON>ontroller } from '@controllers/auditor';
import { Card } from '@cosmos/components/card';
import { Skeleton } from '@cosmos/components/skeleton';
import {
    dataEmphasizeNeutralModerate,
    dataEmphasizeSuccessStrong,
    dataEmphasizeWarningStrong,
} from '@cosmos/constants/tokens';
import {
    DataDonut,
    type DataDonutSliceData,
} from '@cosmos-lab/components/data-donut';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const RequestSummaryCard = observer((): React.JSX.Element => {
    const { auditSummaryByIdData, auditSummaryByIdIsLoading } =
        sharedAuditorController;

    // Transform audit summary data into donut chart format
    const donutData: DataDonutSliceData[] = auditSummaryByIdData
        ? [
              {
                  label: t`Completed`,
                  value: auditSummaryByIdData.acceptedRequests,
                  color: dataEmphasizeSuccessStrong,
              },
              {
                  label: t`Prepared`,
                  value: auditSummaryByIdData.inReviewRequests,
                  color: dataEmphasizeWarningStrong,
              },
              {
                  label: t`New`,
                  value: auditSummaryByIdData.outstandingRequests,
                  color: dataEmphasizeNeutralModerate,
              },
          ]
        : [];

    return (
        <Card
            title={t`Request Summary`}
            data-testid="RequestSummaryCard"
            data-id="1NIjTERV"
            body={
                auditSummaryByIdIsLoading ? (
                    <Skeleton />
                ) : (
                    <DataDonut
                        showLegend
                        data-id="request-summary-donut"
                        size="lg"
                        values={donutData}
                        unit={t`requests`}
                        legendPosition="right"
                    />
                )
            }
        />
    );
});

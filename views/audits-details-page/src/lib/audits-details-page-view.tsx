import { Box } from '@cosmos/components/box';
import { Grid } from '@cosmos/components/grid';
import { AssignedAuditorsCard } from './audits-details-page-assigned-auditors-card';
import { RequestSummaryCard } from './audits-details-page-request-summary-card';
import { AuditDetailsRequestTable } from './audits-details-page-request-table';

export const AuditsDetailsPageView = (): React.JSX.Element => {
    return (
        <Grid
            areas='"assignedAuditors requestSummary" "requestsTable requestsTable"'
            columns="2"
            gapX="2xl"
            gapY="3xl"
            data-testid="AuditsDetailsPageView"
            data-id="2vTMlcSV"
        >
            <Box gridArea="assignedAuditors">
                <AssignedAuditorsCard />
            </Box>

            <Box gridArea="requestSummary">
                <RequestSummaryCard />
            </Box>
            <Box gridArea="requestsTable">
                <AuditDetailsRequestTable />
            </Box>
        </Grid>
    );
};

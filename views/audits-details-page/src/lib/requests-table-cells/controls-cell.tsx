import { Metadata } from '@cosmos/components/metadata';
import { TagGroup } from '@cosmos/components/tag-group';
import type { CustomerRequestListItemResponseDto } from '@globals/api-sdk/types';

interface Control {
    code: string;
    description: string;
    id: number;
    isReady: boolean;
    name: string;
}

interface ControlsCellProps {
    row: { original: CustomerRequestListItemResponseDto };
}

export const ControlsCell = ({
    row: { original },
}: ControlsCellProps): React.JSX.Element => {
    const { controls } = original;
    const MAX_VISIBLE_CONTROLS = 3;

    return (
        <TagGroup
            data-testid="ControlsCell"
            data-id="uImNmGVm"
            maxVisibleTags={MAX_VISIBLE_CONTROLS}
        >
            {controls.map((control) => {
                const typedControl = control as unknown as Control;

                return (
                    <Metadata
                        key={typedControl.id}
                        type="tag"
                        colorScheme="neutral"
                        label={typedControl.code}
                        data-id="5d0A-a7C"
                    />
                );
            })}
        </TagGroup>
    );
};

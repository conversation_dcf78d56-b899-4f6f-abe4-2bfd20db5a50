import { isNil } from 'lodash-es';
import {
    getEmptyPoliciesOverviewMetadata,
    mapOverviewMetricMetadata,
    mapRenewalDateAdaptor,
    type PoliciesData,
    type PolicyOverviewMetadata,
} from '@components/policies';
import {
    sharedPoliciesController,
    sharedPoliciesOwnersController,
} from '@controllers/policies';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type { PolicyTableResponseDto } from '@globals/api-sdk/types';
import { sharedCurrentCompanyController } from '@globals/current-company';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedPoliciesPageHeaderModel } from '@models/policies';
import {
    getPoliciesApprovalStatusOptions,
    getPoliciesRenewalStatusOptions,
} from './policies-constants';
import {
    createPoliciesEmptyStateProps,
    getPoliciesActiveColumns,
    getPoliciesArchivedColumns,
    getPoliciesTableActions,
} from './policies-view.helpers';

/**
 * Controller for the Policies Views (Active and Archive) that encapsulates business logic,
 * data transformations, and computed properties.
 */
export class PoliciesViewController {
    constructor() {
        makeAutoObservable(this);
    }

    // ===== COMPUTED PROPERTIES =====

    /**
     * Whether there is an external policy connection.
     */
    get externalPolicyConnection(): boolean {
        return sharedPoliciesPageHeaderModel.externalPolicyConnection;
    }

    /**
     * Whether there is a BambooHR connection.
     */
    get hasBambooHrConnection(): boolean {
        return sharedPoliciesPageHeaderModel.hasBambooHrConnection;
    }

    /**
     * User select filter configuration.
     */
    get userSelectFilter(): Filter {
        const { ownersComboboxOptions, isLoading, hasNextPage, loadNextPage } =
            sharedPoliciesOwnersController;

        return {
            filterType: 'combobox',
            id: 'userId',
            label: t({
                message: 'Owner',
                comment: 'Filter label for policy owner',
            }),
            placeholder: t({
                message: 'Search by owner',
                comment: 'Placeholder text for owner search filter',
            }),
            isMultiSelect: false,
            isLoading,
            hasMore: hasNextPage,
            onFetchOptions: loadNextPage,
            options: ownersComboboxOptions,
        };
    }

    /**
     * Mapped overview data for the metrics display.
     */
    get mappedOverviewData(): PolicyOverviewMetadata[] {
        const { overviewData } = sharedPoliciesController;

        return isNil(overviewData)
            ? getEmptyPoliciesOverviewMetadata()
            : mapOverviewMetricMetadata(overviewData);
    }

    /**
     * Whether policies can be downloaded based on published policies and domain restrictions.
     */
    get canDownloadPolicies(): boolean {
        const { hasPublishedPolicies } = sharedPoliciesController;

        return (
            hasPublishedPolicies &&
            !sharedCurrentCompanyController.domainHasPolicyDownloadsDisabled
        );
    }

    /**
     * Transformed active policies data for the datatable.
     */
    get activePolicies(): PoliciesData[] {
        const { activePoliciesList } = sharedPoliciesController;

        return this.policiesAdaptor(
            activePoliciesList,
            this.externalPolicyConnection,
            this.canDownloadPolicies,
        );
    }

    /**
     * Column configuration for the active policies datatable.
     */
    get columns(): DatatableProps<PoliciesData>['columns'] {
        return getPoliciesActiveColumns(this.externalPolicyConnection);
    }

    /**
     * Transformed archived policies data for the datatable.
     */
    get archivedPolicies(): PoliciesData[] {
        const { archivedPoliciesList } = sharedPoliciesController;

        return this.policiesAdaptor(archivedPoliciesList);
    }

    /**
     * Column configuration for the archived policies datatable.
     */
    get archivedColumns(): DatatableProps<PoliciesData>['columns'] {
        return getPoliciesArchivedColumns(this.externalPolicyConnection);
    }

    /**
     * Table actions configuration for the datatable.
     */
    get tableActions(): ReturnType<typeof getPoliciesTableActions> {
        const { hasPublishedPolicies, isDownloadingAllPolicies } =
            sharedPoliciesController;

        return getPoliciesTableActions(
            this.canDownloadPolicies,
            hasPublishedPolicies,
            isDownloadingAllPolicies,
            this.handleDownloadAllPolicies,
        );
    }

    /**
     * Empty state props based on current parameters and filters.
     */
    getEmptyStateProps(
        type: 'active' | 'archived' = 'active',
    ): ReturnType<typeof createPoliciesEmptyStateProps> {
        return createPoliciesEmptyStateProps(this.currentParams, type);
    }

    // ===== ACTIONS =====

    /**
     * Handles downloading all policies.
     */
    handleDownloadAllPolicies = (): void => {
        sharedPoliciesController.downloadAllPoliciesFile();
    };

    // ===== DELEGATED PROPERTIES =====
    // These properties are directly delegated to controllers for convenience

    get activePoliciesList(): typeof sharedPoliciesController.activePoliciesList {
        return sharedPoliciesController.activePoliciesList;
    }

    get loadActivePolicies(): typeof sharedPoliciesController.loadActivePolicies {
        return sharedPoliciesController.loadActivePolicies;
    }

    get isActivePoliciesLoading(): typeof sharedPoliciesController.isActivePoliciesLoading {
        return sharedPoliciesController.isActivePoliciesLoading;
    }

    get activePoliciesTotal(): typeof sharedPoliciesController.activePoliciesTotal {
        return sharedPoliciesController.activePoliciesTotal;
    }

    get overviewData(): typeof sharedPoliciesController.overviewData {
        return sharedPoliciesController.overviewData;
    }

    get hasPublishedPolicies(): typeof sharedPoliciesController.hasPublishedPolicies {
        return sharedPoliciesController.hasPublishedPolicies;
    }

    get currentParams(): typeof sharedPoliciesController.currentParams {
        return sharedPoliciesController.currentParams;
    }

    get isDownloadingAllPolicies(): typeof sharedPoliciesController.isDownloadingAllPolicies {
        return sharedPoliciesController.isDownloadingAllPolicies;
    }

    get downloadAllPoliciesFile(): typeof sharedPoliciesController.downloadAllPoliciesFile {
        return sharedPoliciesController.downloadAllPoliciesFile;
    }

    get overviewFilter(): typeof sharedPoliciesController.overviewFilter {
        return sharedPoliciesController.overviewFilter;
    }

    get archivedPoliciesList(): typeof sharedPoliciesController.archivedPoliciesList {
        return sharedPoliciesController.archivedPoliciesList;
    }

    get loadArchivedPolicies(): typeof sharedPoliciesController.loadArchivedPolicies {
        return sharedPoliciesController.loadArchivedPolicies;
    }

    get isArchivedPoliciesLoading(): typeof sharedPoliciesController.isArchivedPoliciesLoading {
        return sharedPoliciesController.isArchivedPoliciesLoading;
    }

    get archivedPoliciesTotal(): typeof sharedPoliciesController.archivedPoliciesTotal {
        return sharedPoliciesController.archivedPoliciesTotal;
    }

    get filters(): ReturnType<typeof this.getFilters> {
        return this.getFilters();
    }

    /**
     * Get filter configuration for the datatable.
     */
    private getFilters() {
        const { overviewFilter } = sharedPoliciesController;

        // Determine which filter should be set based on the overview filter
        const approvalStatusValue = [
            'NEEDS_APPROVAL',
            'APPROVED',
            'PUBLISHED',
            'DRAFT',
        ].includes(overviewFilter as string)
            ? overviewFilter
            : '';
        const renewalStatusValue = ['EXPIRE_SOON', 'EXPIRED'].includes(
            overviewFilter as string,
        )
            ? overviewFilter
            : '';

        return {
            clearAllButtonLabel: t({
                message: 'Reset',
                comment: 'Button label to clear all filters',
            }),
            onClearFiltersFn: () => {
                sharedPoliciesController.clearOverviewFilter();
            },
            filters: [
                {
                    filterType: 'radio' as const,
                    id: 'policyApprovalStatus',
                    label: t({
                        message: 'Status',
                        comment: 'Filter label for policy approval status',
                    }),
                    value: approvalStatusValue,
                    options: getPoliciesApprovalStatusOptions(),
                },
                {
                    filterType: 'radio' as const,
                    id: 'policyRenewal',
                    label: t({
                        message: 'Renewal status',
                        comment: 'Filter label for policy renewal status',
                    }),
                    value: renewalStatusValue,
                    options: getPoliciesRenewalStatusOptions(),
                },
                this.userSelectFilter,
            ],
        };
    }
    private policiesAdaptor(
        dtoData: PolicyTableResponseDto[] | null,
        hasExternalPolicyConnection?: boolean,
        canDownloadPolicies?: boolean,
    ): PoliciesData[] {
        return (dtoData ?? []).map(
            (policy) =>
                ({
                    ...policy,
                    version: {
                        ...policy.version,
                    },
                    versionPolicyStatus: mapRenewalDateAdaptor(policy.version),
                    hasExternalPolicyConnection,
                    hasBambooHrConnection: this.hasBambooHrConnection,
                    canDownloadPolicies,
                }) satisfies PoliciesData,
        );
    }
}

export const sharedPoliciesActiveViewModel = new PoliciesViewController();

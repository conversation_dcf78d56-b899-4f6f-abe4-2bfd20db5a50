import { isNil } from 'lodash-es';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Stack } from '@cosmos/components/stack';
import { Callout } from '@cosmos-lab/components/callout';
import { observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    buildCalloutActions,
    buildCalloutSubtitle,
    buildCalloutTitle,
} from '../helpers/vendors-security-review-completed.helper';

export const VendorsSecurityReviewsCompletedCalloutCard = observer(
    (): React.JSX.Element => {
        const { vendorDetails, isProspectiveVendor } =
            sharedVendorsDetailsController;
        const { socDocument } = sharedVendorsSecurityReviewDocumentsController;

        const { renewalDate, renewalScheduleType, renewalDateStatus } =
            vendorDetails ?? {};

        const isSocReview = !isNil(socDocument);

        const isCustomReminder = renewalScheduleType === 'CUSTOM';
        const isNoRenewalStatus =
            isNil(renewalDateStatus) || renewalDateStatus === 'NO_RENEWAL';
        const isNotNone = renewalScheduleType !== 'NONE';

        const title = buildCalloutTitle(isSocReview);
        const subtitle = buildCalloutSubtitle(
            isProspectiveVendor,
            isCustomReminder,
            isNoRenewalStatus,
            renewalDate,
            isNotNone,
        );

        const actions = buildCalloutActions(
            isProspectiveVendor,
            isCustomReminder,
            isNoRenewalStatus,
            renewalDate,
            vendorDetails?.id,
            isNotNone,
            sharedWorkspacesController.currentWorkspaceId?.toString() ?? null,
            vendorDetails?.id,
        );

        return (
            <Stack
                direction="column"
                mb="xl"
                data-testid="VendorsSecurityReviewsCompletedCalloutCard"
                data-id="BrXvkp9h"
            >
                <Callout
                    data-id="vendors-security-review-completed-view-callout"
                    illustrationName="Schedule"
                    illustrationPosition="left"
                    primaryLabelText={title}
                    secondaryLabelText={subtitle}
                    size="md"
                    actionStack={actions}
                />
            </Stack>
        );
    },
);

import { isUndefined } from 'lodash-es';
import {
    formatBooleanToYesNo,
    formatEndUserControls,
    formatFindingsInReports,
    formatLocations,
    formatServices,
    formatSubserviceQuestion,
    formatTrustServiceCategories,
    getAuditPeriodEndDate,
    getAuditPeriodStartDate,
    getReportOpinion,
    getSocCertification,
    getSocReportType,
    sharedVendorsSecurityReviewDocumentsController,
    shouldShowAuditPeriod,
    shouldShowAuditPeriodRange,
    shouldShowScopeType,
    shouldShowTrustServiceCriteria,
} from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

const VendorsSecuritySOCCompletedAuditPeriod = observer(
    (): React.JSX.Element => {
        const { reviewDocument: { vendorReview } = {} } =
            sharedVendorsSecurityReviewDocumentsController;

        if (shouldShowAuditPeriodRange(vendorReview)) {
            const rangeStartDate = getAuditPeriodStartDate(vendorReview);
            const rangeEndDate = getAuditPeriodEndDate(vendorReview);

            if (rangeStartDate && rangeEndDate) {
                return (
                    <>
                        <DateTime
                            format="sentence"
                            data-testid="SocReportAuditPeriodStartDate"
                            data-id="SocReportAuditPeriodStartDate"
                            date={rangeStartDate}
                        />
                        <DateTime
                            format="sentence"
                            data-testid="SocReportAuditPeriodEndDate"
                            data-id="SocReportAuditPeriodEndDate"
                            date={rangeEndDate}
                        />
                    </>
                );
            }

            return <EmptyValue label="-" />;
        }

        const singleStartDate = getAuditPeriodStartDate(vendorReview);

        if (shouldShowAuditPeriod(vendorReview) && singleStartDate) {
            return (
                <DateTime
                    format="sentence"
                    data-testid="SocReportAuditPeriodStartDate"
                    data-id="SocReportAuditPeriodStartDate"
                    date={singleStartDate}
                />
            );
        }

        return (
            <EmptyValue
                label="-"
                data-testid="renderAuditPeriodValue"
                data-id="tTY17Ca7"
            />
        );
    },
);

export const VendorsSecuritySOCCompletedReportReview = observer(
    (): React.JSX.Element => {
        const { reviewDocument: { vendorReview } = {} } =
            sharedVendorsSecurityReviewDocumentsController;

        return (
            <Card
                title={t`Report review`}
                data-id="JIfNu-TV"
                body={
                    <Stack direction="column" gap="xl">
                        <KeyValuePair
                            label={t`Reviewer`}
                            type="TEXT"
                            value={vendorReview?.reviewer ?? ''}
                        />
                        <KeyValuePair
                            label={t`SOC report issue date`}
                            type="REACT_NODE"
                            value={
                                vendorReview?.reportIssueDate ? (
                                    <DateTime
                                        format="sentence"
                                        data-testid="SocReportIssueDate"
                                        data-id="socReportIssueDate"
                                        date={vendorReview.reportIssueDate}
                                    />
                                ) : (
                                    <EmptyValue label="-" />
                                )
                            }
                        />
                        <KeyValuePair
                            label={t`Review date`}
                            type="REACT_NODE"
                            value={
                                vendorReview?.reviewDate ? (
                                    <DateTime
                                        format="sentence"
                                        data-testid="SocReportReviewDate"
                                        data-id="SocReportReviewDate"
                                        date={vendorReview.reviewDate}
                                    />
                                ) : (
                                    <EmptyValue label="-" />
                                )
                            }
                        />
                        <KeyValuePair
                            label={t`Certification`}
                            type="TEXT"
                            value={getSocCertification(vendorReview?.socReport)}
                        />
                        {shouldShowScopeType(vendorReview) && (
                            <KeyValuePair
                                label={t`Type`}
                                type="TEXT"
                                value={getSocReportType(vendorReview)}
                            />
                        )}
                        {shouldShowTrustServiceCriteria(vendorReview) && (
                            <KeyValuePair
                                label={t`Trust service criteria`}
                                type="TEXT"
                                value={
                                    formatTrustServiceCategories(
                                        vendorReview?.trustServiceCategories,
                                    ) || t`Not available`
                                }
                            />
                        )}
                        {(shouldShowAuditPeriod(vendorReview) ||
                            shouldShowAuditPeriodRange(vendorReview)) && (
                            <KeyValuePair
                                label={t`Audit period`}
                                type="REACT_NODE"
                                value={
                                    <VendorsSecuritySOCCompletedAuditPeriod />
                                }
                            />
                        )}
                        <KeyValuePair
                            label={t`Report opinion`}
                            type="TEXT"
                            value={getReportOpinion(
                                vendorReview?.reportOpinion,
                            )}
                        />

                        {vendorReview?.encompassBusinessNeeds !== undefined && (
                            <KeyValuePair
                                label={t`Do Control Objectives Or Trust Principles Encompass Business Needs?`}
                                type="TEXT"
                                value={formatBooleanToYesNo(
                                    vendorReview.encompassBusinessNeeds,
                                )}
                            />
                        )}

                        {vendorReview?.followUpActivity && (
                            <KeyValuePair
                                label={t`Follow Up Activity If Opinion Is Qualified (If Needed)`}
                                type="TEXT"
                                value={vendorReview.followUpActivity}
                            />
                        )}

                        {formatFindingsInReports(vendorReview?.findings) && (
                            <KeyValuePair
                                label={t`List Of Findings In Report`}
                                type="TEXT"
                                value={formatFindingsInReports(
                                    vendorReview?.findings,
                                )}
                            />
                        )}

                        {formatFindingsInReports(vendorReview?.findings) &&
                            !isUndefined(vendorReview?.hasMaterialImpact) && (
                                <KeyValuePair
                                    label={t`Do The Findings Have Any Material Impact On Your Control Environment?`}
                                    type="TEXT"
                                    value={formatBooleanToYesNo(
                                        vendorReview.hasMaterialImpact,
                                    )}
                                />
                            )}

                        {formatEndUserControls(vendorReview?.userControls) && (
                            <KeyValuePair
                                label={t`For Applicable End-user Controls Documented, Do You Have Controls In Place?`}
                                type="TEXT"
                                value={formatEndUserControls(
                                    vendorReview?.userControls,
                                )}
                            />
                        )}

                        {formatServices(vendorReview?.services) && (
                            <KeyValuePair
                                label={t`List Of Services Included In Report`}
                                type="TEXT"
                                value={formatServices(vendorReview?.services)}
                            />
                        )}

                        {formatLocations(vendorReview?.locations) && (
                            <KeyValuePair
                                label={t`Locations Covered By Report (If Applicable)`}
                                type="TEXT"
                                value={formatLocations(vendorReview?.locations)}
                            />
                        )}

                        {vendorReview?.cpaFirm && (
                            <KeyValuePair
                                label={t`CPA Firm That Performed The Audit`}
                                type="TEXT"
                                value={vendorReview.cpaFirm}
                            />
                        )}

                        {vendorReview?.cpaProcedurePerformed && (
                            <KeyValuePair
                                label={t`Procedures Performed To Assess Reputation Of CPA Firm`}
                                type="TEXT"
                                value={vendorReview.cpaProcedurePerformed}
                            />
                        )}

                        {vendorReview?.subserviceOrganization && (
                            <KeyValuePair
                                label={t`Subservice Organizations In Report`}
                                type="TEXT"
                                value={vendorReview.subserviceOrganization}
                            />
                        )}

                        {vendorReview?.subserviceOrganizationUsingInclusiveMethod !==
                            undefined && (
                            <KeyValuePair
                                label={t`Are Subservice Organizations Presented In The Report Using The Inclusive Method?`}
                                type="TEXT"
                                value={formatSubserviceQuestion(
                                    vendorReview.subserviceOrganizationUsingInclusiveMethod,
                                )}
                            />
                        )}

                        {vendorReview?.subserviceOrganizationProcedurePerformed && (
                            <KeyValuePair
                                label={t`Procedures Performed To Assess Subservice Organizations`}
                                type="TEXT"
                                value={
                                    vendorReview.subserviceOrganizationProcedurePerformed
                                }
                            />
                        )}
                    </Stack>
                }
            />
        );
    },
);

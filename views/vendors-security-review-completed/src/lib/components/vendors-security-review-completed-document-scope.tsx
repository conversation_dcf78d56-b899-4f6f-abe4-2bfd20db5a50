import { isEmpty, uniqueId } from 'lodash-es';
import { snackbarController } from '@controllers/snackbar';
import {
    type DocumentFiles,
    sharedVendorsProfileReportsAndDocumentsMutationController,
} from '@controllers/vendors';
import { Button } from '@cosmos/components/button';
import { Icon } from '@cosmos/components/icon';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

interface VendorsSecurityReviewsCompletedDocumentScopeProps {
    document: DocumentFiles;
    canViewFiles?: boolean;
}

export const VendorsSecurityReviewsCompletedDocumentScope = observer(
    ({
        document,
        canViewFiles = true,
    }: VendorsSecurityReviewsCompletedDocumentScopeProps): React.JSX.Element => {
        const { name, value = '' } = document;

        const { isDownloadPending, downloadFile } =
            sharedVendorsProfileReportsAndDocumentsMutationController;

        const handleDownloadFile = () => {
            if (isDownloadPending || !canViewFiles) {
                return;
            }
            const { documentId } = document;

            if (!documentId) {
                snackbarController.addSnackbar({
                    id: `document-download-error-${uniqueId()}`,
                    props: {
                        title: t`Unable to download document - missing document ID`,
                        severity: 'critical',
                        closeButtonAriaLabel: t`Close`,
                    },
                });

                return;
            }

            downloadFile(documentId);
        };

        return (
            <Stack
                gap="2x"
                data-testid="VendorsSecurityReviewsCompletedDocumentScope"
                data-id="w1GtR6KM"
                align="center"
                justify="between"
            >
                <Stack direction="column" gap="2x">
                    <Text
                        allowBold
                        shouldWrap
                        align="left"
                        as="span"
                        colorScheme="neutral"
                        size="200"
                        type="body"
                    >
                        {name}
                    </Text>

                    {!isEmpty(value) && (
                        <Stack gap="1x">
                            <Icon
                                backgroundType="minimal"
                                colorScheme="neutral"
                                name="AI"
                                size="100"
                            />

                            <Text
                                allowBold
                                shouldWrap
                                align="left"
                                as="span"
                                colorScheme="neutral"
                                size="100"
                                type="body"
                            >
                                {value}
                            </Text>
                        </Stack>
                    )}
                </Stack>

                {canViewFiles && (
                    <Button
                        isIconOnly
                        colorScheme="primary"
                        data-id="vendors-security-reviews-completed-document-scope-download"
                        id="vendors-security-reviews-completed-document-scope-download-id"
                        startIconName="Download"
                        label={t`Download document`}
                        level="tertiary"
                        size="md"
                        width="auto"
                        isLoading={isDownloadPending}
                        a11yLoadingLabel={t`Downloading document...`}
                        onClick={handleDownloadFile}
                    />
                )}
            </Stack>
        );
    },
);

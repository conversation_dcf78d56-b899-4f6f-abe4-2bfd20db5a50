import { isNil } from 'lodash-es';
import { openArchiveVendorModalFromProfile } from '@components/vendors-current-archive-vendor';
import { openRecurringReviewsModal } from '@components/vendors-recurring-reviews';
import type { MetadataProps } from '@cosmos/components/metadata';
import type { CalloutActionStack } from '@cosmos-lab/components/callout';
import type { VendorSecurityReviewResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { openMarkVendorAsActiveModal } from '@views/vendors-mark-vendor-as-active';

/**
 * Builds the security review decision status object based on the given decision.
 * This function maps the decision to a corresponding color scheme and label,
 * which can be used for visual representation in the UI.
 *
 * @param decision - The security review decision.
 * @returns An object containing the color scheme and label for the decision status.
 */
export const buildSecurityReviewDecisionStatus = (
    decision?: VendorSecurityReviewResponseDto['decision'] | null,
): { colorScheme: MetadataProps['colorScheme']; label: string } => {
    switch (decision) {
        case 'PENDING': {
            return { colorScheme: 'neutral', label: t`Pending` };
        }
        case 'APPROVED': {
            return { colorScheme: 'success', label: t`Approved` };
        }
        case 'APPROVED_WITH_CONDITIONS': {
            return {
                colorScheme: 'warning',
                label: t`Approved with conditions`,
            };
        }
        case 'REJECTED': {
            return { colorScheme: 'critical', label: t`Rejected` };
        }
        default: {
            return { colorScheme: 'neutral', label: '-' };
        }
    }
};

export const buildCalloutTitle = (isSocReview: boolean): string => {
    if (isSocReview) {
        return t`Report review complete!`;
    }

    return t`Security review complete!`;
};

export const buildCalloutSubtitle = (
    isProspective: boolean,
    isCustomReminder: boolean,
    isNoRenewalStatus: boolean,
    renewalDate: string | null | undefined,
    isNotNone: boolean,
): string => {
    if (isProspective) {
        return t`Once you have a procurement decision, mark the vendor as active or archive them.`;
    }

    if (
        !isNoRenewalStatus &&
        !isCustomReminder &&
        !isNil(renewalDate) &&
        isNotNone
    ) {
        const formattedDate = formatDate('field', renewalDate);

        return t`The next review deadline for this vendor will be on ${formattedDate}`;
    }

    return '';
};

export const buildCalloutActions = (
    isProspective: boolean,
    isCustomReminder: boolean,
    isNoRenewalStatus: boolean,
    renewalDate: string | undefined | null,
    vendorId: number | undefined,
    isNotNone: boolean,
    workspaceId?: string | null,
    currentVendorId?: number,
): CalloutActionStack | undefined => {
    if (isProspective) {
        return {
            actions: [
                {
                    actionType: 'button',
                    id: 'vendor-security-review-completed-mark-as-active-button',
                    typeProps: {
                        label: t`Mark as active`,
                        level: 'secondary',
                        onClick: () => {
                            const redirectPath = `/workspaces/${workspaceId}/vendors/current/${currentVendorId}/overview`;

                            openMarkVendorAsActiveModal(redirectPath);
                        },
                    },
                },
                {
                    actionType: 'button',
                    id: 'vendor-security-review-completed-archive-vendor-button',
                    typeProps: {
                        label: t`Archive vendor`,
                        level: 'tertiary',
                        onClick: () => {
                            const redirectPath = `/workspaces/${workspaceId}/vendors/current/${currentVendorId}/overview`;

                            openArchiveVendorModalFromProfile({
                                isProspective: true,
                                shouldRedirect: true,
                                redirectPath,
                            });
                        },
                    },
                },
            ],
        };
    }

    if (
        !isNoRenewalStatus &&
        !isCustomReminder &&
        !isNil(renewalDate) &&
        isNotNone
    ) {
        return {
            actions: [
                {
                    actionType: 'button',
                    id: 'vendor-security-review-completed-change-review-frequency-button',
                    typeProps: {
                        label: t`Change review frequency`,
                        level: 'secondary',
                        onClick: action(() => {
                            openRecurringReviewsModal(vendorId);
                        }),
                    },
                },
            ],
        };
    }

    return {
        actions: [
            {
                actionType: 'button',
                id: 'vendor-security-review-completed-set-up-recurring-reviews-button',
                typeProps: {
                    label: t`Set up recurring reviews`,
                    level: 'secondary',
                    onClick: action(() => {
                        openRecurringReviewsModal(vendorId);
                    }),
                },
            },
        ],
    };
};

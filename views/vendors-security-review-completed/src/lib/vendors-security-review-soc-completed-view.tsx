import { useEffect } from 'react';
import { VendorSecurityReviewsAISummaryComponent } from '@components/vendors-security-reviews';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { PdfViewer } from '@cosmos-lab/components/pdf-viewer';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { ContentAside } from '@ui/content-aside';
import { VendorsSecurityReviewsCompletedCalloutCard } from './components/vendors-security-review-completed-callout-card';
import { VendorsSecuritySOCCompletedReportReview } from './components/vendors-security-review-soc-completed-report-review';

export const VendorsSecurityReviewSOCCompletedView = observer(
    (): React.JSX.Element => {
        const { pdfDownloadUrl } =
            sharedVendorsSecurityReviewDocumentsController;

        const { isVendorsDomainReadEnabled } = sharedFeatureAccessModel;

        const { securityReviewDetails, isLoading: isSecurityReviewLoading } =
            sharedVendorsSecurityReviewDetailsController;

        const navigate = useNavigate();

        useEffect(() => {
            if (
                !sharedVendorsDetailsController.vendorDetails ||
                !securityReviewDetails?.id ||
                !securityReviewDetails.vendor?.id
            ) {
                return;
            }
            const isVendorProspective =
                sharedVendorsDetailsController.vendorDetails.status ===
                'PROSPECTIVE';

            if (!isVendorsDomainReadEnabled) {
                navigate(
                    `${routeController.userPartOfUrl}/vendors/${isVendorProspective ? 'prospective' : 'current'}`,
                );
            }

            if (
                securityReviewDetails.type === 'SECURITY' ||
                securityReviewDetails.type === 'UPLOAD_REPORT'
            ) {
                navigate(
                    `${routeController.userPartOfUrl}/vendors/${isVendorProspective ? 'prospective' : 'current'}/${securityReviewDetails.vendor.id}/security-reviews/${securityReviewDetails.id}/completed`,
                );
            }

            if (
                !isSecurityReviewLoading &&
                securityReviewDetails.status !== 'COMPLETED'
            ) {
                navigate(
                    `${routeController.userPartOfUrl}/vendors/${isVendorProspective ? 'prospective' : 'current'}/${securityReviewDetails.vendor.id}/security-reviews/soc/${securityReviewDetails.id}`,
                );
            }
        }, [
            isVendorsDomainReadEnabled,
            navigate,
            isSecurityReviewLoading,
            securityReviewDetails?.status,
            securityReviewDetails?.vendor?.id,
            securityReviewDetails?.id,
            securityReviewDetails?.type,
        ]);

        if (
            !isVendorsDomainReadEnabled ||
            isSecurityReviewLoading ||
            securityReviewDetails?.type !== 'SOC_REPORT' ||
            securityReviewDetails.status !== 'COMPLETED'
        ) {
            return <Loader isSpinnerOnly label={t`Loading...`} />;
        }

        return (
            <ContentAside
                data-testid="VendorsProfileSecurityReviewSocView"
                data-id="FmVVf_kM"
                content={<VendorsSecuritySOCCompletedReportReview />}
            >
                <Stack direction="column" gap="xl">
                    <VendorsSecurityReviewsCompletedCalloutCard />

                    <VendorSecurityReviewsAISummaryComponent />
                    <PdfViewer
                        src={pdfDownloadUrl?.signedUrl ?? ''}
                        label={'pdf-viewer'}
                        data-id={'cosmos-pdf-viewer'}
                    />
                </Stack>
            </ContentAside>
        );
    },
);

import type { DatatableProps } from '@cosmos/components/datatable';
import type { CustomerRequestEvidenceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ControlCodeCell } from '../cells/control-code-cell';
import { OptionsTableActionCell } from '../cells/options-table-action-cell';

export const getEvidenceRequestDetailsControlsColumns =
    (): DatatableProps<CustomerRequestEvidenceResponseDto>['columns'] => [
        {
            id: 'options',
            header: '',
            isActionColumn: true,
            size: 40,
            cell: OptionsTableActionCell,
        },
        {
            accessorKey: 'code',
            header: t`Code`,
            id: 'code',
            size: 100,
            enableSorting: true,
            cell: ControlCodeCell,
        },
        {
            accessorKey: 'name',
            header: t`Title`,
            id: 'name',
            enableSorting: true,
            size: 300,
        },
        {
            accessorKey: 'quantityOfRegularValidEvidence',
            id: 'quantityOfRegularValidEvidence',
            header: t`Evidence`,
            enableSorting: false,
            size: 80,
        },
    ];

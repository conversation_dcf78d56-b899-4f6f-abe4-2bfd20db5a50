import {
    <PERSON>s<PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON>,
    <PERSON>ver<PERSON>Badge<PERSON>ell,
    TicketingLinkCell,
} from '@components/monitoring-details-findings';
import { COLUMN_SIZE_CONSTRAINTS } from '../constants/monitoring-details-findings.constants';

const {
    SMALL: SMALL_COLUMN_SIZE_CONSTRAINT,
    MEDIUM: MEDIUM_COLUMN_SIZE_CONSTRAINT,
    LARGE: LARGE_COLUMN_SIZE_CONSTRAINT,
    XLARGE: XLARGE_COLUMN_SIZE_CONSTRAINT,
} = COLUMN_SIZE_CONSTRAINTS;

// Define common column type to avoid repetition
interface ColumnDefinition {
    id: string;
    header: string;
    accessorKey: string;
    enableSorting?: boolean;
    cell?: React.ComponentType<unknown>;
    minSize?: number;
    size?: number;
    maxSize?: number;
    meta?: {
        shouldIgnoreRowClick?: boolean;
    };
}

// Common columns that can be reused across different column sets
const COMMON_COLUMNS = {
    applicablePersonnel: (accessorKey = 'name'): ColumnDefinition => ({
        id: 'NAME',
        header: 'Applicable personnel',
        enableSorting: false,
        accessorKey,
        cell: NameCell as React.ComponentType<unknown>,
        minSize: LARGE_COLUMN_SIZE_CONSTRAINT,
    }),
    email: (): ColumnDefinition => ({
        id: 'EMAIL',
        header: 'Email',
        enableSorting: false,
        accessorKey: 'email',
        minSize: XLARGE_COLUMN_SIZE_CONSTRAINT,
    }),
    connection: (accessorKey = 'connection'): ColumnDefinition => ({
        id: 'CONNECTION',
        header: 'Connection',
        enableSorting: false,
        accessorKey,
        cell: ConnectionsCell as React.ComponentType<unknown>,
    }),
    connectionAlias: (accessorKey = 'connectionAlias'): ColumnDefinition => ({
        id: 'CONNECTIONID_ALIAS',
        header: 'Account ID / alias',
        enableSorting: false,
        accessorKey,
    }),
    severity: (): ColumnDefinition => ({
        id: 'SEVERITY',
        header: 'Severity',
        enableSorting: false,
        accessorKey: 'severity',
        cell: SeverityBadgeCell as React.ComponentType<unknown>,
        minSize: SMALL_COLUMN_SIZE_CONSTRAINT,
        size: SMALL_COLUMN_SIZE_CONSTRAINT,
        maxSize: SMALL_COLUMN_SIZE_CONSTRAINT,
    }),
};

export function getInfrastructureColumns(): ColumnDefinition[] {
    return [
        {
            id: 'RESOURCE',
            header: 'Resource name',
            accessorKey: 'resourceName',
        },
        {
            id: 'REGION',
            header: 'Region',
            enableSorting: false,
            accessorKey: 'region',
        },
        COMMON_COLUMNS.connection('connectionId'),
        COMMON_COLUMNS.connectionAlias(),
    ];
}

export function getInfrastructureOrgUnitsColumns(): ColumnDefinition[] {
    return [];
}

export function getCaCColumns(): ColumnDefinition[] {
    return [
        {
            id: 'REPOSITORY',
            header: 'Repository',
            enableSorting: false,
            accessorKey: 'repository',
            minSize: MEDIUM_COLUMN_SIZE_CONSTRAINT,
        },
        COMMON_COLUMNS.severity(),
        {
            id: 'RESOURCE_TYPE',
            header: 'Resource type',
            enableSorting: false,
            accessorKey: 'resourceType',
            minSize: LARGE_COLUMN_SIZE_CONSTRAINT,
        },
        {
            id: 'RESOURCE',
            header: 'Resource name',
            enableSorting: false,
            accessorKey: 'name',
            minSize: LARGE_COLUMN_SIZE_CONSTRAINT,
        },
        {
            id: 'PROPERTY',
            header: 'Property',
            enableSorting: false,
            accessorKey: 'property',
            minSize: LARGE_COLUMN_SIZE_CONSTRAINT,
        },
    ];
}

export function getInDrataColumns(): ColumnDefinition[] {
    return [COMMON_COLUMNS.applicablePersonnel(), COMMON_COLUMNS.email()];
}

export function getVersionControlColumns(): ColumnDefinition[] {
    return [
        COMMON_COLUMNS.applicablePersonnel(),
        COMMON_COLUMNS.connection(),
        COMMON_COLUMNS.connectionAlias(),
    ];
}

export function getVersionControlRepositoryColumns(): ColumnDefinition[] {
    return [];
}

export function getTicketingColumns(): ColumnDefinition[] {
    return [
        {
            id: 'TICKET_DESCRIPTION',
            header: 'Ticket description',
            enableSorting: false,
            accessorKey: 'ticketDescription',
        },
        COMMON_COLUMNS.connection(),
        {
            id: 'LINK',
            header: '',
            enableSorting: false,
            accessorKey: 'link',
            cell: TicketingLinkCell as React.ComponentType<unknown>,
        },
    ];
}

export function getEDRColumns(
    additionalProperties: string[],
): ColumnDefinition[] {
    const result: ColumnDefinition[] = [
        COMMON_COLUMNS.applicablePersonnel(),
        {
            id: 'DRATA_DEVICE',
            header: 'Drata device',
            enableSorting: false,
            accessorKey: 'drataDevice',
        },
    ];

    const isCrowdStrike = additionalProperties.includes(
        'PROVIDER_PREVENTION_POLICY',
    );

    result.push(
        {
            id: 'SERIAL_NUMBER',
            header: `${isCrowdStrike ? 'CrowdStrike' : 'SentinelOne'} serial number`,
            enableSorting: false,
            accessorKey: 'serialNumber',
        },
        {
            id: 'PREVENTION_POLICY',
            header: isCrowdStrike ? 'Prevention policy' : 'Operational state',
            enableSorting: false,
            accessorKey: 'operationalState',
        },
    );

    return result;
}

export function getIdentityColumns(): ColumnDefinition[] {
    return [
        COMMON_COLUMNS.applicablePersonnel(),
        COMMON_COLUMNS.email(),
        COMMON_COLUMNS.connection(),
        COMMON_COLUMNS.connectionAlias(),
    ];
}

export function getWizColumns(): ColumnDefinition[] {
    return [
        {
            id: 'ISSUE_ID',
            header: 'Issue ID',
            enableSorting: false,
            accessorKey: 'issueId',
        },
        {
            id: 'ISSUE_TITLE',
            header: 'Issue title',
            enableSorting: false,
            accessorKey: 'issueTitle',
        },
        {
            id: 'ENTITY_NAME',
            header: 'Entity name',
            enableSorting: false,
            accessorKey: 'entityName',
        },
        COMMON_COLUMNS.severity(),
        {
            id: 'LINK',
            header: 'Link to Wiz Platform',
            enableSorting: false,
            accessorKey: 'link',
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: LinkCell as React.ComponentType<unknown>,
        },
    ];
}

export function getVulnerabilityColumns(): ColumnDefinition[] {
    return [
        {
            id: 'VULNERABILITY',
            header: 'Vulnerability title',
            enableSorting: false,
            accessorKey: 'vulnerabilityTitle',
        },
        {
            id: 'RESOURCE_ID',
            header: 'Resource ID',
            enableSorting: false,
            accessorKey: 'resourceId',
        },
        {
            id: 'DUE_DATE',
            header: 'Due date',
            enableSorting: false,
            accessorKey: 'dueDate',
            cell: DueDateCell as React.ComponentType<unknown>,
        },
    ];
}

export function getCustomColumns(): ColumnDefinition[] {
    return [
        {
            id: 'RESOURCE',
            header: 'Resource name',
            accessorKey: 'resourceName',
        },
        COMMON_COLUMNS.connectionAlias(),
    ];
}

export function getPolicyColumns(): ColumnDefinition[] {
    return [COMMON_COLUMNS.applicablePersonnel(), COMMON_COLUMNS.email()];
}

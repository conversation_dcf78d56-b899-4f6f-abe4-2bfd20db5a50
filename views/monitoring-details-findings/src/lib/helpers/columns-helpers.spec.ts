import { describe, expect, test, vi } from 'vitest';
import {
    getCaCColumns,
    getCustomColumns,
    getEDRColumns,
    getIdentityColumns,
    getInDrataColumns,
    getInfrastructureColumns,
    getPolicyColumns,
    getTicketingColumns,
    getVersionControlColumns,
    getVulnerabilityColumns,
    getWizColumns,
} from './columns-helpers';

// Looks like somewhere in the dependency tree, @controllers/vendors are imported
vi.mock('@controllers/vendors', () => ({}));
describe('columns-helpers', () => {
    describe('getInfrastructureColumns', () => {
        test('returns correct column structure', () => {
            const columns = getInfrastructureColumns();

            expect(columns).toHaveLength(4);
            expect(columns[0].id).toBe('RESOURCE');
            expect(columns[0].header).toBe('Resource name');
            expect(columns[1].id).toBe('REGION');
            expect(columns[2].id).toBe('CONNECTION');
            expect(columns[3].id).toBe('CONNECTIONID_ALIAS');
        });
    });

    describe('getCaCColumns', () => {
        test('returns correct column structure', () => {
            const columns = getCaCColumns();

            expect(columns).toHaveLength(5);
            expect(columns[0].id).toBe('REPOSITORY');
            expect(columns[1].id).toBe('SEVERITY');
            expect(columns[2].id).toBe('RESOURCE_TYPE');
            expect(columns[3].id).toBe('RESOURCE');
            expect(columns[4].id).toBe('PROPERTY');
        });
    });

    describe('getInDrataColumns', () => {
        test('returns correct column structure', () => {
            const columns = getInDrataColumns();

            expect(columns).toHaveLength(2);
            expect(columns[0].id).toBe('NAME');
            expect(columns[1].id).toBe('EMAIL');
        });
    });

    describe('getVersionControlColumns', () => {
        test('returns correct column structure', () => {
            const columns = getVersionControlColumns();

            expect(columns).toHaveLength(3);
            expect(columns[0].id).toBe('NAME');
            expect(columns[1].id).toBe('CONNECTION');
            expect(columns[2].id).toBe('CONNECTIONID_ALIAS');
        });
    });

    describe('getTicketingColumns', () => {
        test('returns correct column structure', () => {
            const columns = getTicketingColumns();

            expect(columns).toHaveLength(3);
            expect(columns[0].id).toBe('TICKET_DESCRIPTION');
            expect(columns[1].id).toBe('CONNECTION');
            expect(columns[2].id).toBe('LINK');
        });
    });

    describe('getEDRColumns', () => {
        test('returns correct columns for CrowdStrike', () => {
            const columns = getEDRColumns(['PROVIDER_PREVENTION_POLICY']);

            expect(columns).toHaveLength(4);
            expect(columns[0].id).toBe('NAME');
            expect(columns[1].id).toBe('DRATA_DEVICE');
            expect(columns[2].id).toBe('SERIAL_NUMBER');
            expect(columns[3].id).toBe('PREVENTION_POLICY');
            expect(columns[2].header).toContain('CrowdStrike');
        });

        test('returns correct columns for SentinelOne', () => {
            const columns = getEDRColumns([]);

            expect(columns).toHaveLength(4);
            expect(columns[0].id).toBe('NAME');
            expect(columns[1].id).toBe('DRATA_DEVICE');
            expect(columns[2].id).toBe('SERIAL_NUMBER');
            expect(columns[3].id).toBe('PREVENTION_POLICY');
            expect(columns[2].header).toContain('SentinelOne');
        });
    });

    describe('getIdentityColumns', () => {
        test('returns correct column structure', () => {
            const columns = getIdentityColumns();

            expect(columns).toHaveLength(4);
            expect(columns[0].id).toBe('NAME');
            expect(columns[1].id).toBe('EMAIL');
            expect(columns[2].id).toBe('CONNECTION');
            expect(columns[3].id).toBe('CONNECTIONID_ALIAS');
        });
    });

    describe('getWizColumns', () => {
        test('returns correct column structure', () => {
            const columns = getWizColumns();

            expect(columns).toHaveLength(5);
            expect(columns[0].id).toBe('ISSUE_ID');
            expect(columns[1].id).toBe('ISSUE_TITLE');
            expect(columns[2].id).toBe('ENTITY_NAME');
            expect(columns[3].id).toBe('SEVERITY');
            expect(columns[4].id).toBe('LINK');
            expect(columns[4].meta?.shouldIgnoreRowClick).toBeTruthy();
        });
    });

    describe('getVulnerabilityColumns', () => {
        test('returns correct column structure', () => {
            const columns = getVulnerabilityColumns();

            expect(columns).toHaveLength(3);
            expect(columns[0].id).toBe('VULNERABILITY');
            expect(columns[1].id).toBe('RESOURCE_ID');
            expect(columns[2].id).toBe('DUE_DATE');
        });
    });

    describe('getCustomColumns', () => {
        test('returns correct column structure', () => {
            const columns = getCustomColumns();

            expect(columns).toHaveLength(2);
            expect(columns[0].id).toBe('RESOURCE');
            expect(columns[1].id).toBe('CONNECTIONID_ALIAS');
        });
    });

    describe('getPolicyColumns', () => {
        test('returns correct column structure', () => {
            const columns = getPolicyColumns();

            expect(columns).toHaveLength(2);
            expect(columns[0].id).toBe('NAME');
            expect(columns[1].id).toBe('EMAIL');
        });
    });
});

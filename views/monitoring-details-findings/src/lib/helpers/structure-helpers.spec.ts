import { isFunction } from 'lodash-es';
import { beforeEach, describe, expect, test, vi } from 'vitest';
import type { MonitorsV2FindingsFiltersListResponseDto } from '@globals/api-sdk/types';
import {
    CPSM_TEST_IDS,
    MALWARE_DETECTION_SOFTWARE_INSTALLED,
    VULNERABILITY_MONITORING_TEST_IDS,
} from '@views/monitoring';
import * as columnsHelpers from './columns-helpers';
import * as filtersHelpers from './filters-helpers';
import { getDatatableStructure } from './structure-helpers';

// Mock all the imported helper functions
vi.mock('./columns-helpers', () => ({
    getCaCColumns: vi.fn(),
    getCustomColumns: vi.fn(),
    getEDRColumns: vi.fn(),
    getIdentityColumns: vi.fn(),
    getInDrataColumns: vi.fn(),
    getInfrastructureColumns: vi.fn(),
    getInfrastructureOrgUnitsColumns: vi.fn(),
    getPolicyColumns: vi.fn(),
    getTicketingColumns: vi.fn(),
    getVersionControlColumns: vi.fn(),
    getVulnerabilityColumns: vi.fn(),
    getWizColumns: vi.fn(),
}));

vi.mock('./filters-helpers', () => ({
    getAgentFilters: vi.fn(),
    getCaCFilters: vi.fn(),
    getCustomFilters: vi.fn(),
    getEDRFilters: vi.fn(),
    getIdentityFilters: vi.fn(),
    getInDrataFilters: vi.fn(),
    getInfrastructureFilters: vi.fn(),
    getPolicyFilters: vi.fn(),
    getTicketingFilters: vi.fn(),
    getVersionControlFilters: vi.fn(),
    getVulnerabilityFilters: vi.fn(),
    getWizFilters: vi.fn(),
}));

describe('getDatatableStructure', () => {
    const mockFiltersValues = { key: ['value'] };
    const mockAdditionalProperties = [{ id: 'prop1' }];

    beforeEach(() => {
        // Reset all mocks before each test
        vi.resetAllMocks();

        // Set default return values for mocks
        Object.values(columnsHelpers).forEach((mock) => {
            if (isFunction(mock)) {
                vi.mocked(mock).mockReturnValue([
                    {
                        id: 'mock-column',
                        header: 'Mock Column',
                        accessorKey: 'mockColumn',
                    },
                ]);
            }
        });

        Object.values(filtersHelpers).forEach((mock) => {
            if (isFunction(mock)) {
                vi.mocked(mock).mockReturnValue([
                    {
                        filterType: 'checkbox',
                        id: 'mock-filter',
                        label: 'Mock Filter',
                        options: [],
                    },
                ]);
            }
        });
    });

    test('should return infrastructure structure by default', () => {
        const result = getDatatableStructure({
            category:
                'UNKNOWN' as unknown as MonitorsV2FindingsFiltersListResponseDto['category'],
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getInfrastructureColumns).toHaveBeenCalled();
        expect(filtersHelpers.getInfrastructureFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return INFRASTRUCTURE_ACORN structure when category is INFRASTRUCTURE and source is ACORN', () => {
        const result = getDatatableStructure({
            category: 'INFRASTRUCTURE',
            source: 'ACORN',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getCaCColumns).toHaveBeenCalled();
        expect(filtersHelpers.getCaCFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return OBSERVABILITY_CPSM structure when category is OBSERVABILITY and testId is in CPSM_TEST_IDS', () => {
        const testId = CPSM_TEST_IDS[0];
        const result = getDatatableStructure({
            category: 'OBSERVABILITY',
            source: 'DRATA',
            testId,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getWizColumns).toHaveBeenCalled();
        expect(filtersHelpers.getWizFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return OBSERVABILITY_VULNERABILITY structure when category is OBSERVABILITY and testId is in VULNERABILITY_MONITORING_TEST_IDS', () => {
        const testId = VULNERABILITY_MONITORING_TEST_IDS[0];
        const result = getDatatableStructure({
            category: 'OBSERVABILITY',
            source: 'DRATA',
            testId,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getVulnerabilityColumns).toHaveBeenCalled();
        expect(filtersHelpers.getVulnerabilityFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return AGENT_MALWARE_DETECTION structure when category is AGENT and testId is MALWARE_DETECTION_SOFTWARE_INSTALLED', () => {
        const result = getDatatableStructure({
            category: 'AGENT',
            source: 'DRATA',
            testId: MALWARE_DETECTION_SOFTWARE_INSTALLED,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getEDRColumns).toHaveBeenCalledWith(['prop1']);
        expect(filtersHelpers.getEDRFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return POLICY structure when category is POLICY', () => {
        const result = getDatatableStructure({
            category: 'POLICY',
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getPolicyColumns).toHaveBeenCalled();
        expect(filtersHelpers.getPolicyFilters).toHaveBeenCalled();
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return CUSTOM structure when category is CUSTOM', () => {
        const result = getDatatableStructure({
            category: 'CUSTOM',
            source: 'CUSTOM',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getCustomColumns).toHaveBeenCalled();
        expect(filtersHelpers.getCustomFilters).toHaveBeenCalled();
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return IDENTITY structure when category is IDENTITY', () => {
        const result = getDatatableStructure({
            category: 'IDENTITY',
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getIdentityColumns).toHaveBeenCalled();
        expect(filtersHelpers.getIdentityFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return TICKETING structure when category is TICKETING', () => {
        const result = getDatatableStructure({
            category: 'TICKETING',
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getTicketingColumns).toHaveBeenCalled();
        expect(filtersHelpers.getTicketingFilters).toHaveBeenCalledWith(
            mockFiltersValues,
        );
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return VERSION_CONTROL structure when category is VERSION_CONTROL', () => {
        const result = getDatatableStructure({
            category: 'VERSION_CONTROL',
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getVersionControlColumns).toHaveBeenCalled();
        expect(filtersHelpers.getVersionControlFilters).toHaveBeenCalled();
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return IN_DRATA structure when category is IN_DRATA', () => {
        const result = getDatatableStructure({
            category: 'IN_DRATA',
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getInDrataColumns).toHaveBeenCalled();
        expect(filtersHelpers.getInDrataFilters).toHaveBeenCalled();
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });

    test('should return AGENT structure when category is AGENT and testId is not MALWARE_DETECTION_SOFTWARE_INSTALLED', () => {
        const result = getDatatableStructure({
            category: 'AGENT',
            source: 'DRATA',
            testId: -1,
            filtersValues: mockFiltersValues,
            additionalProperties: mockAdditionalProperties,
        });

        expect(columnsHelpers.getInDrataColumns).toHaveBeenCalled();
        expect(filtersHelpers.getAgentFilters).toHaveBeenCalled();
        expect(result).toStrictEqual({
            columns: [
                {
                    id: 'mock-column',
                    header: 'Mock Column',
                    accessorKey: 'mockColumn',
                },
            ],
            filters: [
                {
                    filterType: 'checkbox',
                    id: 'mock-filter',
                    label: 'Mock Filter',
                    options: [],
                },
            ],
        });
    });
});

import type { MonitorsV2FindingsFiltersListResponseDto } from '@globals/api-sdk/types';
import type { Provider } from '@globals/providers';
import {
    CPSM_TEST_IDS,
    MALWARE_DETECTION_SOFTWARE_INSTALLED,
    VERSION_CONTROL_REPOSITORY_TEST_IDS,
    VULNERABILITY_MONITORING_TEST_IDS,
} from '@views/monitoring';
import {
    getCaCColumns,
    getCustomColumns,
    getEDRColumns,
    getIdentityColumns,
    getInDrataColumns,
    getInfrastructureColumns,
    getInfrastructureOrgUnitsColumns,
    getPolicyColumns,
    getTicketingColumns,
    getVersionControlColumns,
    getVersionControlRepositoryColumns,
    getVulnerabilityColumns,
    getWizColumns,
} from './columns-helpers';
import {
    getAgentFilters,
    getCaCFilters,
    getCustomFilters,
    getEDRFilters,
    getIdentityFilters,
    getInDrataFilters,
    getInfrastructureFilters,
    getPolicyFilters,
    getTicketingFilters,
    getVersionControlFilters,
    getVulnerabilityFilters,
    getWizFilters,
} from './filters-helpers';

type ColumnMapKeyType =
    | 'INFRASTRUCTURE_ACORN'
    | 'INFRASTRUCTURE'
    | 'INFRASTRUCTURE_ORG_UNITS'
    | 'OBSERVABILITY_CPSM'
    | 'OBSERVABILITY_VULNERABILITY'
    | 'OBSERVABILITY'
    | 'POLICY'
    | 'AGENT_MALWARE_DETECTION'
    | 'AGENT'
    | 'CUSTOM'
    | 'IDENTITY'
    | 'TICKETING'
    | 'VERSION_CONTROL'
    | 'VERSION_CONTROL_REPOSITORY'
    | 'IN_DRATA';
interface DatatableStructureType {
    columns: unknown[];
    filters: unknown[];
}

export const getDatatableStructure = ({
    category,
    source,
    testId,
    filtersValues,
    additionalProperties,
}: {
    category: MonitorsV2FindingsFiltersListResponseDto['category'];
    source: MonitorsV2FindingsFiltersListResponseDto['source'];
    testId: MonitorsV2FindingsFiltersListResponseDto['testId'];
    filtersValues: Record<string, unknown[]>;
    additionalProperties: { id: unknown }[];
}): DatatableStructureType => {
    // Create a mapping of category/source/testId combinations to their respective handlers
    const structureMap: Record<ColumnMapKeyType, () => DatatableStructureType> =
        {
            INFRASTRUCTURE_ACORN: () => ({
                columns: getCaCColumns(),
                filters: getCaCFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            INFRASTRUCTURE: () => ({
                columns: getInfrastructureColumns(),
                filters: getInfrastructureFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            INFRASTRUCTURE_ORG_UNITS: () => ({
                columns: getInfrastructureOrgUnitsColumns(),
                filters: getInfrastructureFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            OBSERVABILITY_CPSM: () => ({
                columns: getWizColumns(),
                filters: getWizFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            OBSERVABILITY_VULNERABILITY: () => ({
                columns: getVulnerabilityColumns(),
                filters: getVulnerabilityFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            OBSERVABILITY: () => ({
                columns: getInfrastructureColumns(),
                filters: getInfrastructureFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            POLICY: () => ({
                columns: getPolicyColumns(),
                filters: getPolicyFilters(),
            }),
            AGENT_MALWARE_DETECTION: () => ({
                columns: getEDRColumns(
                    additionalProperties.map((prop) => prop.id as string),
                ),
                filters: getEDRFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            AGENT: () => ({
                columns: getInDrataColumns(),
                filters: getAgentFilters(),
            }),
            CUSTOM: () => ({
                columns: getCustomColumns(),
                filters: getCustomFilters(),
            }),
            IDENTITY: () => ({
                columns: getIdentityColumns(),
                filters: getIdentityFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            TICKETING: () => ({
                columns: getTicketingColumns(),
                filters: getTicketingFilters(
                    filtersValues as Record<string, string[]>,
                ),
            }),
            VERSION_CONTROL: () => ({
                columns: getVersionControlColumns(),
                filters: getVersionControlFilters(),
            }),
            VERSION_CONTROL_REPOSITORY: () => ({
                columns: getVersionControlRepositoryColumns(),
                filters: getVersionControlFilters(),
            }),
            IN_DRATA: () => ({
                columns: getInDrataColumns(),
                filters: getInDrataFilters(),
            }),
        };

    let key = category as ColumnMapKeyType;

    if (category === 'INFRASTRUCTURE') {
        if (source === 'ACORN') {
            key = 'INFRASTRUCTURE_ACORN';
        } else if (
            (filtersValues.connection as Provider[] | undefined)?.some(
                (connection: Provider) =>
                    ['AWS_ORG_UNITS', 'AZURE_ORG_UNITS'].includes(connection),
            )
        ) {
            key = 'INFRASTRUCTURE_ORG_UNITS';
        } else if (source === 'CUSTOM') {
            key = 'CUSTOM';
        } else {
            key = 'INFRASTRUCTURE';
        }
    } else if (category === 'OBSERVABILITY') {
        if (CPSM_TEST_IDS.includes(testId as (typeof CPSM_TEST_IDS)[number])) {
            key = 'OBSERVABILITY_CPSM';
        } else if (
            VULNERABILITY_MONITORING_TEST_IDS.includes(
                testId as (typeof VULNERABILITY_MONITORING_TEST_IDS)[number],
            )
        ) {
            key = 'OBSERVABILITY_VULNERABILITY';
        }
    } else if (
        category === 'AGENT' &&
        testId === MALWARE_DETECTION_SOFTWARE_INSTALLED
    ) {
        key = 'AGENT_MALWARE_DETECTION';
    } else if (
        category === 'VERSION_CONTROL' &&
        VERSION_CONTROL_REPOSITORY_TEST_IDS.includes(
            testId as (typeof VERSION_CONTROL_REPOSITORY_TEST_IDS)[number],
        )
    ) {
        key = 'VERSION_CONTROL_REPOSITORY';
    }

    // Return the structure for the key or default to INFRASTRUCTURE
    const getColumnsAndFilters =
        (structureMap[key] as (() => DatatableStructureType) | undefined) ??
        structureMap.INFRASTRUCTURE;

    return getColumnsAndFilters();
};

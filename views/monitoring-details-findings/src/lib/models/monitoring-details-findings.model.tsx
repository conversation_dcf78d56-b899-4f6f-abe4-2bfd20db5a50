import { isEmpty } from 'lodash-es';
import {
    type FindingForBulkExclusion,
    openMonitoringBulkExclusionModal,
} from '@components/monitoring-bulk-exclusion-modal';
import { sharedMonitoringFindingsReportController } from '@controllers/monitoring';
import {
    activeMonitoringDetailsController,
    type CodebaseFindingForExclusion,
    sharedCodebaseFindingExclusionMutationController,
    sharedFindingExclusionMutationController,
    sharedFindingsController,
    sharedFindingsFiltersController,
} from '@controllers/monitoring-details';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import type { FindingItemResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import {
    action,
    makeAutoObservable,
    runInAction,
    toJS,
    when,
} from '@globals/mobx';
import {
    CPSM_TEST_IDS,
    MALWARE_DETECTION_SOFTWARE_INSTALLED,
    MONITOR_CHECK_TYPE_ENUM,
    VULNERABILITY_MONITORING_TEST_IDS,
} from '@views/monitoring';
import { sharedMonitoringDetailsFindingsPanelModel } from '@views/monitoring-details-findings-panel';
import { getDatatableStructure } from '../helpers/structure-helpers';

const isSpecificTestId = (testId: number) => {
    return [
        ...(CPSM_TEST_IDS as readonly number[]),
        ...(VULNERABILITY_MONITORING_TEST_IDS as readonly number[]),
        MALWARE_DETECTION_SOFTWARE_INSTALLED,
    ].includes(testId);
};

class MonitoringDetailsFindingsModel {
    selectedFindings: FindingItemResponseDto[] = [];
    isAllRowsSelected = false;
    datatableRef: React.RefObject<DatatableRef> | null = null;

    constructor() {
        makeAutoObservable(this);
    }

    CATEGORIES_ALLOWED_DOWNLOAD_CSV = [
        MONITOR_CHECK_TYPE_ENUM.OBSERVABILITY,
        MONITOR_CHECK_TYPE_ENUM.INFRASTRUCTURE,
    ];

    isAllowedCategory = (category: string) => {
        return this.CATEGORIES_ALLOWED_DOWNLOAD_CSV.includes(category);
    };

    get allowBulkActions() {
        const { monitorDetailsData } = activeMonitoringDetailsController;

        if (!monitorDetailsData) {
            return false;
        }

        return (
            this.isCodebaseFindings ||
            this.isAllowedCategory(monitorDetailsData.category) ||
            isSpecificTestId(monitorDetailsData.testId) ||
            this.canManageExclusions
        );
    }

    get getMonitoringDetailsFindingsBulkActions(): BulkAction[] {
        if (!this.allowBulkActions) {
            return [];
        }

        const actions: BulkAction[] = [];

        if (this.canManageExclusions) {
            actions.push({
                actionType: 'button',
                id: 'bulk-actions-findings-exclude',
                typeProps: {
                    label: t`Exclude`,
                    level: 'tertiary',
                    onClick: action(() => {
                        this.handleBulkExclude();
                    }),
                },
            });
        }

        actions.push({
            actionType: 'button',
            id: 'bulk-actions-findings-download-csv',
            typeProps: {
                label: t`Download CSV`,
                level: 'tertiary',
                onClick: this.handleCsvDownload,
            },
        });

        return actions;
    }

    handleCsvDownload = () => {
        const { monitorDetailsData } = activeMonitoringDetailsController;

        if (!monitorDetailsData) {
            return;
        }

        if (
            monitorDetailsData.testId === MALWARE_DETECTION_SOFTWARE_INSTALLED
        ) {
            sharedMonitoringFindingsReportController.downloadEdrFailedTestReport(
                monitorDetailsData.testId,
            );

            return;
        }

        if (
            monitorDetailsData.category ===
                MONITOR_CHECK_TYPE_ENUM.OBSERVABILITY &&
            CPSM_TEST_IDS.includes(
                monitorDetailsData.testId as (typeof CPSM_TEST_IDS)[number],
            )
        ) {
            sharedMonitoringFindingsReportController.downloadCspmFailedTestReport(
                monitorDetailsData.testId,
            );

            return;
        }

        if (this.isAllowedCategory(monitorDetailsData.category)) {
            sharedMonitoringFindingsReportController.downloadFailedFindings({
                testId: monitorDetailsData.testId,
                testName: monitorDetailsData.testName,
                checkType: monitorDetailsData.category,
            });
        }
    };

    get datatableStructure(): { columns: unknown[]; filters: unknown[] } {
        const { findingsFilters, isLoadingFindingsFilters } =
            sharedFindingsFiltersController;

        if (isLoadingFindingsFilters || !findingsFilters) {
            return {
                columns: [],
                filters: [],
            };
        }

        const {
            category,
            source,
            testId,
            additionalProperties,
            filters: filterValues,
        } = findingsFilters;

        return getDatatableStructure({
            category,
            source,
            testId,
            filtersValues: Object.fromEntries(
                Object.entries(filterValues).map(([key, value]) => [
                    key,
                    Array.isArray(value) ? value : [value],
                ]),
            ),
            additionalProperties: additionalProperties.map((a) => ({
                id: a.id,
            })),
        });
    }

    get canManageExclusions(): boolean {
        return (
            sharedFindingsFiltersController.findingsFilters
                ?.canManageExclusions ?? false
        );
    }

    get isCodebaseFindings(): boolean {
        return (
            sharedFindingsFiltersController.findingsFilters?.source === 'ACORN'
        );
    }

    handleBulkExclude = (): void => {
        if (isEmpty(this.selectedFindings)) {
            return;
        }

        if (this.isCodebaseFindings) {
            // Handle codebase findings exclusion
            const codebaseFindingsForExclusion: CodebaseFindingForExclusion[] =
                this.selectedFindings.map((finding) => {
                    return {
                        findingId: finding.findingId,
                        testId: finding.testId,
                        repositoryName: finding.repository || '',
                    };
                });

            openMonitoringBulkExclusionModal(
                codebaseFindingsForExclusion.length,
                (reason: string) => {
                    runInAction(() => {
                        sharedCodebaseFindingExclusionMutationController.createBulkExclusion(
                            codebaseFindingsForExclusion,
                            reason,
                            this.onBulkExcludeSuccess,
                        );
                    });
                },
            );
        } else {
            // Handle production findings exclusion
            const findingsForExclusion: FindingForBulkExclusion[] =
                this.selectedFindings.map((finding) => ({
                    findingId: finding.findingId,
                    testId: finding.testId,
                    connectionId: finding.connectionId,
                    metadata: finding,
                }));

            openMonitoringBulkExclusionModal(
                findingsForExclusion.length,
                (reason: string) => {
                    runInAction(() => {
                        sharedFindingExclusionMutationController.createBulkExclusion(
                            findingsForExclusion,
                            reason,
                            this.onBulkExcludeSuccess,
                        );
                    });
                },
            );
        }
    };

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        const allFindings = sharedFindingsController.findingsList;

        if (isAllRowsSelected) {
            this.selectedFindings = toJS(allFindings);
        } else {
            const selectedFindingIds = Object.keys(selectedRows);

            this.selectedFindings = selectedFindingIds
                .map((findingId) => {
                    const finding = allFindings.find(
                        (f) => f.findingId === findingId,
                    );

                    return finding ? toJS(finding) : undefined;
                })
                .filter(
                    (finding): finding is FindingItemResponseDto =>
                        finding !== undefined,
                );
        }
        this.isAllRowsSelected = isAllRowsSelected;
    };

    onBulkExcludeSuccess = (): void => {
        // Capture the currently selected finding in the panel before clearing selections
        const currentPanelFindingId =
            sharedMonitoringDetailsFindingsPanelModel.selectedFindingItem
                ?.findingId;
        const indexBeforeExclusion =
            sharedMonitoringDetailsFindingsPanelModel.selectedFindingIndex;

        runInAction(() => {
            this.datatableRef?.current?.resetRowSelection();
            this.selectedFindings = [];
            this.isAllRowsSelected = false;
        });

        if (currentPanelFindingId) {
            when(
                () => {
                    return !sharedFindingsController.findingsList.some(
                        (item) => item.findingId === currentPanelFindingId,
                    );
                },
                () => {
                    sharedMonitoringDetailsFindingsPanelModel.switchToNextItemAfterExclusion(
                        indexBeforeExclusion,
                    );
                },
            );
        }
    };
}

export const sharedMonitoringDetailsFindingsModel =
    new MonitoringDetailsFindingsModel();

import type { RefObject } from 'react';
import {
    type PolicyDetailsFormData,
    sharedPolicyBuilderDetailsController,
} from '@controllers/policy-builder-details';
import { observer, runInAction } from '@globals/mobx';
import { sharedPolicyBuilderDetailsFormController } from '@models/policies';
import { Form, type FormValues } from '@ui/forms';

interface Props {
    formRef: RefObject<HTMLFormElement>;
}

export const PoliciesBuilderOverviewDetailsEditComponent = observer(
    ({ formRef }: Props): React.JSX.Element => {
        return (
            <Form
                hasExternalSubmitButton
                data-id="policy-builder-details-edit-form"
                formId="policy-builder-details-edit"
                ref={formRef}
                schema={
                    sharedPolicyBuilderDetailsFormController.editDetailsSchema
                }
                onSubmit={(values: FormValues) => {
                    runInAction(() => {
                        sharedPolicyBuilderDetailsController.savePolicyDetails(
                            values as unknown as PolicyDetailsFormData,
                        );
                    });
                }}
            />
        );
    },
);

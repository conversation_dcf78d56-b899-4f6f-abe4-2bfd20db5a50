import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { AssessmentProgressCard } from './components/assessment-progress-card/assessment-progress-card';
import { ManagementDataTable } from './components/management-data-table/management-data-table';
import { RegisterPostureCard } from './components/register-posture-card/register-posture-card';

export const RiskRegisterManagementView = observer((): React.JSX.Element => {
    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="RiskRegisterManagementView"
            data-id="9BAiy9bG"
        >
            <Grid gapX="2xl" columns="auto 1fr">
                <AssessmentProgressCard />

                <RegisterPostureCard />
            </Grid>

            <ManagementDataTable />
        </Stack>
    );
});

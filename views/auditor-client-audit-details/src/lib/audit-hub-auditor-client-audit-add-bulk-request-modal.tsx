import { sharedAuditHubAuditController } from '@controllers/audit-hub';
import { modalController } from '@controllers/modal';
import { snackbarController } from '@controllers/snackbar';
import { Button } from '@cosmos/components/button';
import {
    type CosmosFileObject,
    SUPPORTED_FORMATS,
} from '@cosmos/components/file-upload';
import { FileUploadField } from '@cosmos/components/file-upload-field';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

const MODAL_ID = 'add-bulk-requests-modal';
/**
 * Closes the request changes modal.
 *
 * @returns Void.
 */
const handleClose = (): void => {
    sharedAuditHubAuditController.resetBulkUploadState();
    modalController.closeModal(MODAL_ID);
};

export const AuditHubAuditorClientAuditAddBulkRequestModal = observer(
    (): React.JSX.Element => {
        const handleFileUpdate = async (files: CosmosFileObject[]) => {
            await sharedAuditHubAuditController.handleFileUpdate(files);
        };

        const handleDownloadTemplate = () => {
            sharedAuditHubAuditController.downloadTemplate();
        };

        const handleSubmit = async () => {
            const success =
                await sharedAuditHubAuditController.handleBulkSubmit();

            if (success) {
                handleClose();
            }
        };

        return (
            <>
                <Modal.Header
                    title={t`Add requests in bulk`}
                    closeButtonAriaLabel={t`Add requests in bulk`}
                    onClose={handleClose}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Text type="body" size="200">
                            <Trans>
                                Download and fill out the template, then upload
                                once you&apos;re done. Note: Values in the
                                &quot;Mapped DCF control&quot; column of the
                                template must be valid DCF controls.
                            </Trans>
                        </Text>

                        <Button
                            label={t`Download template`}
                            level="secondary"
                            size="md"
                            startIconName="Download"
                            data-id="download-template-button"
                            a11yLoadingLabel={t`Downloading template...`}
                            isLoading={
                                sharedAuditHubAuditController.isDownloadingTemplate
                            }
                            onClick={handleDownloadTemplate}
                        />

                        <Stack direction="column" gap="sm">
                            <Text type="body" size="200">
                                <Trans>Upload request list</Trans>
                            </Text>

                            <FileUploadField
                                oneFileOnly
                                required
                                acceptedFormats={[SUPPORTED_FORMATS.csv]}
                                isMulti={false}
                                selectButtonText={t`Upload files`}
                                innerLabel={t`Or drop files here`}
                                removeButtonText={t`Remove file`}
                                data-id="bulk-request-file-upload"
                                formId="bulk-request-modal"
                                name="csvFile"
                                label=""
                                key={
                                    sharedAuditHubAuditController.fileUploadKey
                                }
                                initialFiles={sharedAuditHubAuditController.uploadedFiles.map(
                                    (file) => ({ file, errors: [] }),
                                )}
                                feedback={
                                    sharedAuditHubAuditController.fileValidationError
                                        ? {
                                              type: 'error',
                                              message:
                                                  sharedAuditHubAuditController.fileValidationError,
                                          }
                                        : undefined
                                }
                                errorCodeMessages={{
                                    'file-invalid-type': t`Not a valid file type.`,
                                    'file-too-large': t`File size is too large.`,
                                    'file-too-small': t`File size is too small.`,
                                    'too-many-files': t`Contains too many files.`,
                                }}
                                onUpdate={handleFileUpdate}
                            />

                            {sharedAuditHubAuditController.isValidatingCsv && (
                                <Text type="body" size="100">
                                    <Trans>Validating CSV file...</Trans>
                                </Text>
                            )}

                            {sharedAuditHubAuditController.isCreatingCustomerRequests && (
                                <Text type="body" size="100">
                                    <Trans>Creating customer requests...</Trans>
                                </Text>
                            )}
                        </Stack>
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: handleClose,
                        },
                        {
                            label: t`Save`,
                            level: 'primary',
                            onClick: () => {
                                handleSubmit().catch(() => {
                                    snackbarController.addSnackbar({
                                        id: 'bulk-create-error',
                                        props: {
                                            title: t`Failed to create customer requests`,
                                            description: t`An error occurred while creating the requests. Please try again.`,
                                            severity: 'critical',
                                            closeButtonAriaLabel: t`Close`,
                                        },
                                    });
                                });
                            },
                        },
                    ]}
                />
            </>
        );
    },
);

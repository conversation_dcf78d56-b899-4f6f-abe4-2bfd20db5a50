import { buildVendorBulkFormSchema } from '@components/vendors-current-bulk-add-vendors';
import { sharedVendorsBulkAddUpdateController } from '@controllers/vendors';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Grid } from '@cosmos/components/grid';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { Form } from '@ui/forms';

const VENDORS_CURRENT_BULK_ADD_VENDOR_FORM_ID =
    'vendors-current-bulk-add-vendors-form-id';

export const VendorsCurrentBulkAddVendorsUploadComponent = observer(
    ({
        formRef,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
    }): React.JSX.Element => {
        const {
            loadVendorsBulkAddUpdateTemplate,
            validateVendorsBulkUpdate,
            isLoading,
        } = sharedVendorsBulkAddUpdateController;

        return (
            <Grid
                gap="xl"
                flow="row"
                data-testid="VendorsCurrentBulkAddVendorsUploadComponent"
                data-id="kiNL9MOm"
            >
                <Grid
                    data-id="vendors-current-bulk-add-vendors-step-0"
                    gap="xl"
                    flow="row"
                >
                    <Box>
                        <Text type="subheadline" size="400">
                            <Trans>
                                Use this template to add new vendors and make
                                changes to your current vendors in bulk.
                            </Trans>
                        </Text>
                    </Box>
                    <Stack gap="md">
                        <Box minWidth="2xl">
                            <Metadata type="number" label="1" />
                        </Box>

                        <Text type="title" size="200">
                            <Trans>
                                Download the sample data template to import your
                                vendors. Please view the{' '}
                                <AppLink
                                    isExternal
                                    href="https://help.drata.com/en/articles/4755823-vendor-directory-profiles#h_0daa908b64"
                                    label={t`help article`}
                                />{' '}
                                and follow the rules for the template.
                            </Trans>
                        </Text>
                    </Stack>
                    <Box pl="3xl">
                        <Button
                            label={t`Download template`}
                            level="secondary"
                            size="md"
                            isLoading={isLoading}
                            a11yLoadingLabel={t`Downloading template...`}
                            onClick={loadVendorsBulkAddUpdateTemplate}
                        />
                    </Box>
                    <Stack gap="md" direction="column">
                        <Stack gap="md">
                            <Box minWidth="2xl">
                                <Metadata type="number" label="2" />
                            </Box>

                            <Text type="title" size="200">
                                <Trans>
                                    Upload the completed CSV template.
                                </Trans>
                            </Text>
                        </Stack>
                        <Box pl="3xl">
                            <Form
                                hasExternalSubmitButton
                                ref={formRef}
                                formId={VENDORS_CURRENT_BULK_ADD_VENDOR_FORM_ID}
                                data-id="vendors-current-bulk-add-vendors-form"
                                schema={buildVendorBulkFormSchema()}
                                onSubmit={validateVendorsBulkUpdate}
                            />
                        </Box>
                    </Stack>
                </Grid>
            </Grid>
        );
    },
);

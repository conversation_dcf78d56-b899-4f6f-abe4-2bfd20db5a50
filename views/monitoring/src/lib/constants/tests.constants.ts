export const MALWARE_DETECTION_SOFTWARE_INSTALLED = 64;

const FORMAL_CODE_REVIEW_PROCESS = 8;
const USER_WITH_EXCESSIVE_ADMIN_PRIVILEGES = 208;
const EXTERNAL_EXPOSURE = 209;
const NOT_ENCRYPTED_IN_TRANSIT = 210;
const CRITICAL_VULNERABILITY_ADDRESSED = 212;
const HIGH_VULNERABILITY_ADDRESSED = 213;
const CRITICAL_VULNERABILITY_ADDRESSED_TENABLE = 235;
const HIGH_VULNERABILITY_ADDRESSED_TENABLE = 236;
const CRITICAL_VULNERABILITY_ADDRESSED_SNYK = 237;
const HIGH_VULNERABILITY_ADDRESSED_SNYK = 238;
const CRITICAL_VULNERABILITY_ADDRESSED_SEMGREP = 239;
const HIGH_VULNERABILITY_ADDRESSED_SEMGREP = 240;
const CRITICAL_VULNERABILITY_ADDRESSED_QUALYS = 241;
const HIGH_VULNERABILITY_ADDRESSED_QUALYS = 242;
const CRITICAL_VULNERABILITY_ADDRESSED_CROWDSTRIKE_VMS = 282;
const HIGH_VULNERABILITY_ADDRESSED_CROWDSTRIKE_VMS = 283;
const CRITICAL_VULNERABILITY_ADDRESSED_SENTINELONE_VMS = 284;
const HIGH_VULNERABILITY_ADDRESSED_SENTINELONE_VMS = 285;
const CRITICAL_VULNERABILITY_ADDRESSED_MS_DEFENDER_VMS = 286;
const HIGH_VULNERABILITY_ADDRESSED_MS_DEFENDER_VMS = 287;
const CRITICAL_VULNERABILITY_ADDRESSED_RAPID7_VMS = 288;
const HIGH_VULNERABILITY_ADDRESSED_RAPID7_VMS = 289;
const CRITICAL_VULNERABILITY_ADDRESSED_ARNICA = 313;
const HIGH_VULNERABILITY_ADDRESSED_ARNICA = 314;
const CRITICAL_VULNERABILITY_ADDRESSED_AIKIDO = 315;
const HIGH_VULNERABILITY_ADDRESSED_AIKIDO = 316;
const CRITICAL_VULNERABILITY_ADDRESSED_WIZ_VMS = 317;
const HIGH_VULNERABILITY_ADDRESSED_WIZ_VMS = 318;
const CRITICAL_VULNERABILITY_ADDRESSED_WIZ_CODE = 319;
const HIGH_VULNERABILITY_ADDRESSED_WIZ_CODE = 320;

export const CPSM_TEST_IDS = [
    USER_WITH_EXCESSIVE_ADMIN_PRIVILEGES,
    EXTERNAL_EXPOSURE,
    NOT_ENCRYPTED_IN_TRANSIT,
] as const;

export const VULNERABILITY_MONITORING_TEST_IDS = [
    CRITICAL_VULNERABILITY_ADDRESSED,
    HIGH_VULNERABILITY_ADDRESSED,
    CRITICAL_VULNERABILITY_ADDRESSED_TENABLE,
    HIGH_VULNERABILITY_ADDRESSED_TENABLE,
    CRITICAL_VULNERABILITY_ADDRESSED_SNYK,
    HIGH_VULNERABILITY_ADDRESSED_SNYK,
    CRITICAL_VULNERABILITY_ADDRESSED_SEMGREP,
    HIGH_VULNERABILITY_ADDRESSED_SEMGREP,
    CRITICAL_VULNERABILITY_ADDRESSED_QUALYS,
    HIGH_VULNERABILITY_ADDRESSED_QUALYS,
    CRITICAL_VULNERABILITY_ADDRESSED_SENTINELONE_VMS,
    HIGH_VULNERABILITY_ADDRESSED_SENTINELONE_VMS,
    CRITICAL_VULNERABILITY_ADDRESSED_CROWDSTRIKE_VMS,
    HIGH_VULNERABILITY_ADDRESSED_CROWDSTRIKE_VMS,
    CRITICAL_VULNERABILITY_ADDRESSED_MS_DEFENDER_VMS,
    HIGH_VULNERABILITY_ADDRESSED_MS_DEFENDER_VMS,
    CRITICAL_VULNERABILITY_ADDRESSED_RAPID7_VMS,
    HIGH_VULNERABILITY_ADDRESSED_RAPID7_VMS,
    CRITICAL_VULNERABILITY_ADDRESSED_ARNICA,
    HIGH_VULNERABILITY_ADDRESSED_ARNICA,
    CRITICAL_VULNERABILITY_ADDRESSED_AIKIDO,
    HIGH_VULNERABILITY_ADDRESSED_AIKIDO,
    CRITICAL_VULNERABILITY_ADDRESSED_WIZ_VMS,
    HIGH_VULNERABILITY_ADDRESSED_WIZ_VMS,
    CRITICAL_VULNERABILITY_ADDRESSED_WIZ_CODE,
    HIGH_VULNERABILITY_ADDRESSED_WIZ_CODE,
] as const;

export const VERSION_CONTROL_REPOSITORY_TEST_IDS = [
    FORMAL_CODE_REVIEW_PROCESS,
] as const;

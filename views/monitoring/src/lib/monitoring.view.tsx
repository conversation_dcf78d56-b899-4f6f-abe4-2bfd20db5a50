import { AppDatatable } from '@components/app-datatable';
import {
    sharedMonitoringController,
    sharedMonitoringStatsController,
} from '@controllers/monitoring';
import { Box } from '@cosmos/components/box';
import { Card } from '@cosmos/components/card';
import { Grid } from '@cosmos/components/grid';
import { Loader } from '@cosmos/components/loader';
import { StatBlock } from '@cosmos-lab/components/stat-block';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { MonitoringBulkActionsModel } from './models/monitoring-bulk-actions.model';
import { MONITORING_COLUMNS, MONITORING_FILTERS } from './monitoring.constants';

export const MonitoringView = observer((): React.JSX.Element => {
    const navigate = useNavigate();

    const { bulkActions, handleRowSelection } =
        new MonitoringBulkActionsModel();

    const {
        monitoringListData,
        monitoringListTotal,
        monitoringListLoad,
        isLoading,
    } = sharedMonitoringController;

    const {
        monitoringPassingPercents,
        monitoringStatsFailedTests,
        monitoringStatsPassedTests,
        isLoadingStats,
    } = sharedMonitoringStatsController;

    return (
        <>
            {isLoadingStats ? (
                <Card
                    title={t`Tests passed`}
                    body={<Loader isSpinnerOnly label={t`Loading...`} />}
                />
            ) : (
                <Grid columns="3" gap="4x" pb="4x">
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Failed tests`}
                            statValue={monitoringStatsFailedTests}
                            statIcon="NotReady"
                            statIconColor="critical"
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Passed tests`}
                            statValue={monitoringStatsPassedTests}
                            statIcon="CheckCircle"
                            statIconColor="success"
                        />
                    </Box>
                    <Box
                        borderColor="neutralBorderInitial"
                        borderWidth="borderWidth1"
                        borderRadius="borderRadius2x"
                        p="4x"
                    >
                        <StatBlock
                            title={t`Draft tests`}
                            statValue={monitoringPassingPercents}
                            statIcon="InProgress"
                        />
                    </Box>
                </Grid>
            )}

            <AppDatatable
                isFullPageTable
                isRowSelectionEnabled
                isLoading={isLoading}
                tableId="datatable-monitoring"
                data-id="datatable-monitoring"
                data={monitoringListData}
                columns={MONITORING_COLUMNS}
                total={monitoringListTotal}
                filterProps={MONITORING_FILTERS}
                bulkActionDropdownItems={bulkActions}
                getRowId={(row) => String(row.testId)}
                emptyStateProps={{
                    title: t`No monitoring tests found`,
                    description: t`No monitoring tests found`,
                }}
                tableSearchProps={{
                    hideSearch: false,
                    placeholder: t`Search`,
                }}
                filterViewModeProps={{
                    props: {
                        initialSelectedOption: 'pinned',
                        togglePinnedLabel: t`Pin filters to page`,
                        toggleUnpinnedLabel: t`Move filters to dropdown`,
                        selectedOption: 'pinned',
                    },
                    viewMode: 'toggleable',
                }}
                tableActions={[
                    {
                        actionType: 'button',
                        id: 'create-button',
                        typeProps: {
                            label: t`Create test`,
                            level: 'secondary',
                            href: '/compliance/monitoring/builder',
                        },
                    },
                ]}
                onFetchData={monitoringListLoad}
                onRowSelection={handleRowSelection}
                onRowClick={({ row }) => {
                    navigate(`${row.testId}/overview`);
                }}
            />
        </>
    );
});

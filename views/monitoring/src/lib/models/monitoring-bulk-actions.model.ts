import { isError } from 'lodash-es';
import { sharedMonitoringController } from '@controllers/monitoring';
import { snackbarController } from '@controllers/snackbar';
import type {
    BulkAction,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { t } from '@globals/i18n/macro';
import { logger } from '@globals/logger';
import { makeAutoObservable } from '@globals/mobx';

export class MonitoringBulkActionsModel {
    selectedTestIndices: string[] = [];
    isAllRowsSelected = false;

    constructor() {
        makeAutoObservable(this);
    }

    bulkActionDropdownItems = {};

    get bulkActions(): BulkAction[] {
        return [
            {
                actionType: 'button',
                id: 'bulk-test-now',
                typeProps: {
                    label: t`Test now`,
                    level: 'tertiary',
                    onClick: () => {
                        this.handleTestNow().catch((error) => {
                            logger.error(
                                `Failed to execute bulk test now: ${isError(error) ? error.message : String(error)}`,
                            );
                            snackbarController.addSnackbar({
                                id: 'bulk-test-now-error',
                                props: {
                                    title: t`Failed to start tests. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        });
                    },
                },
            },
            {
                actionType: 'button',
                id: 'bulk-enable',
                typeProps: {
                    label: t`Enable`,
                    level: 'tertiary',
                    onClick: () => {
                        this.handleBulkEnable().catch((error) => {
                            logger.error(
                                `Failed to enable tests: ${isError(error) ? error.message : String(error)}`,
                            );
                            snackbarController.addSnackbar({
                                id: 'bulk-enable-error',
                                props: {
                                    title: t`Failed to enable tests. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        });
                    },
                },
            },
            {
                actionType: 'button',
                id: 'bulk-disable',
                typeProps: {
                    label: t`Disable`,
                    level: 'tertiary',
                    onClick: () => {
                        this.handleBulkDisable().catch((error) => {
                            logger.error(
                                `Failed to disable tests: ${isError(error) ? error.message : String(error)}`,
                            );
                            snackbarController.addSnackbar({
                                id: 'bulk-disable-error',
                                props: {
                                    title: t`Failed to disable tests. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        });
                    },
                },
            },
            {
                actionType: 'button',
                id: 'bulk-publish',
                typeProps: {
                    label: t`Publish`,
                    level: 'tertiary',
                    onClick: () => {
                        this.handleBulkPublish().catch((error) => {
                            logger.error(
                                `Failed to publish tests: ${isError(error) ? error.message : String(error)}`,
                            );
                            snackbarController.addSnackbar({
                                id: 'bulk-publish-error',
                                props: {
                                    title: t`Failed to publish tests. Please try again.`,
                                    severity: 'critical',
                                    closeButtonAriaLabel: t`Close`,
                                },
                            });
                        });
                    },
                },
            },
            {
                actionType: 'dropdown',
                id: 'bulk-actions-dropdown',
                typeProps: {
                    items: [
                        {
                            id: 'download',
                            label: t`Download CSV`,
                            type: 'item',
                            value: 'download',
                            startIconName: 'Download',
                            onSelect: () => {
                                this.handleDownloadCSV().catch((error) => {
                                    logger.error(
                                        `Failed to download CSV: ${isError(error) ? error.message : String(error)}`,
                                    );
                                    snackbarController.addSnackbar({
                                        id: 'download-csv-error',
                                        props: {
                                            title: t`Failed to download CSV. Please try again.`,
                                            severity: 'critical',
                                            closeButtonAriaLabel: t`Close`,
                                        },
                                    });
                                });
                            },
                        },
                    ],
                    label: t`More`,
                    level: 'tertiary',
                },
            },
        ];
    }

    get selectedTestIds(): number[] {
        return this.selectedTestIndices
            .filter(Boolean)
            .map((id) => parseInt(id));
    }

    /**
     * Gets the appropriate test IDs based on selection state.
     */
    getTestIdsForBulkAction = async (): Promise<number[]> => {
        if (this.isAllRowsSelected) {
            return sharedMonitoringController.getTestIds();
        }

        return this.selectedTestIds;
    };

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        this.selectedTestIndices = Object.keys(selectedRows);
        this.isAllRowsSelected = isAllRowsSelected;
    };

    handleDownloadCSV = async (): Promise<void> => {
        const testIds = await this.getTestIdsForBulkAction();

        sharedMonitoringController.downloadTests(testIds);
    };

    handleTestNow = async (): Promise<void> => {
        const testIds = await this.getTestIdsForBulkAction();

        sharedMonitoringController.bulkTestNow(testIds);
    };

    handleBulkEnable = async (): Promise<void> => {
        const testIds = await this.getTestIdsForBulkAction();

        sharedMonitoringController.bulkEnableTests(testIds);
    };

    handleBulkDisable = async (): Promise<void> => {
        const testIds = await this.getTestIdsForBulkAction();

        sharedMonitoringController.bulkDisableTests(testIds);
    };

    handleBulkPublish = async (): Promise<void> => {
        const testIds = await this.getTestIdsForBulkAction();

        sharedMonitoringController.bulkPublishTests(testIds);
    };
}

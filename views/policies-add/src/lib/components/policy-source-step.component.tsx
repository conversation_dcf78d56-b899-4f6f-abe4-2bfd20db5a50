import { sharedCreatePolicyController } from '@controllers/policies';
import { Stack } from '@cosmos/components/stack';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
import { Form } from '@ui/forms';
import type { PolicySourceFormData } from '../types';

const REPLACE_DEFAULT_POLICIES_HELP_LINK =
    'https://help.drata.com/en/articles/6219781-create-and-replace-a-policy-with-your-custom-policy';

export const PolicySourceStep = observer(
    ({
        formRef,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
    }): React.JSX.Element => {
        const { formModel } = sharedCreatePolicyController;

        const handleSubmit = action((values: unknown) => {
            sharedCreatePolicyController.updateStepData(
                'source',
                values as PolicySourceFormData,
            );
        });

        return (
            <Stack
                direction="column"
                gap="2xl"
                data-testid="PolicySourceStep"
                data-id="gvqSzqOA"
            >
                <Form
                    hasExternalSubmitButton
                    formId="policy-source-form"
                    ref={formRef}
                    data-id="policy-source-form"
                    schema={formModel.policySourceSchema}
                    onSubmit={handleSubmit}
                />

                <AppLink isExternal href={REPLACE_DEFAULT_POLICIES_HELP_LINK}>
                    {t`Learn about creating policies`}
                </AppLink>
            </Stack>
        );
    },
);

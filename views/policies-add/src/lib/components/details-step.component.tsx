import { sharedCreatePolicyController } from '@controllers/policies';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import type { DetailsFormData } from '../types';

export const DetailsStep = observer(
    ({
        formRef,
    }: {
        formRef: React.ForwardedRef<HTMLFormElement>;
    }): React.JSX.Element => {
        const { formModel } = sharedCreatePolicyController;

        const handleSubmit = action((values: unknown) => {
            const typedValues = values as DetailsFormData;
            const cleanedValues = {
                ...typedValues,
                name: typedValues.name.trim(),
                description: typedValues.description.trim(),
                disclaimer: typedValues.disclaimer?.trim(),
            };

            sharedCreatePolicyController.updateStepData(
                'details',
                cleanedValues,
            );
        });

        return (
            <Stack
                direction="column"
                gap="2xl"
                data-testid="DetailsStep"
                data-id="LniIy7Oa"
            >
                <Text type="subheadline" size="400" as="p">
                    <Trans>Add basic details</Trans>
                </Text>

                <Form
                    hasExternalSubmitButton
                    formId="details-form"
                    ref={formRef}
                    data-id="details-form"
                    schema={formModel.detailsSchema}
                    onSubmit={handleSubmit}
                />
            </Stack>
        );
    },
);

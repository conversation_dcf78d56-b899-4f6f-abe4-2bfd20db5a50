import { noop } from 'lodash-es';
import { ControlPanel } from '@components/controls';
import { sharedControlDetailsOrchestratorController } from '@controllers/controls';
import { sharedMonitoringDetailsControlsController } from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import { Banner } from '@cosmos/components/banner';
import { Datatable } from '@cosmos/components/datatable';
import { Stack } from '@cosmos/components/stack';
import type { ControlMonitorResponseDto } from '@globals/api-sdk/types';
import { action, observer } from '@globals/mobx';
import { sharedMonitoringDetailsControlsTableModel } from './models/monitoring-details-controls-table.model';

const handleOpenControlPanel = action(
    ({ row: control }: { row: ControlMonitorResponseDto }) => {
        sharedControlDetailsOrchestratorController.load(control.id);
        panelController.openPanel({
            id: 'monitoring-details-control-panel',
            content: () => (
                <ControlPanel
                    controlSource="MONITORING_DETAILS"
                    data-id="monitoring-details-control-panel"
                />
            ),
        });
    },
);

interface MonitoringDetailsControlsViewProps {
    code?: boolean;
    testId: number;
    'data-testid'?: string;
    'data-id'?: string;
}

export const MonitoringDetailsControlsView = observer(
    ({ code = false, testId }: MonitoringDetailsControlsViewProps) => {
        const {
            monitoringDetailsControlsData,
            monitoringDetailsControlsTotal,
            isLoading,
            loadMonitoringDetailsControls,
        } = sharedMonitoringDetailsControlsController;

        const { controlsColumns } = sharedMonitoringDetailsControlsTableModel;

        return (
            <Stack
                gap="4x"
                pt="8x"
                direction="column"
                data-testid="MonitoringDetailsControlsView"
                data-id="MonitoringDetailsControlsView"
            >
                {code && (
                    <Banner
                        title="Codebase tests do not impact control readiness."
                        severity="primary"
                        body="Codebase tests monitor your infrastructure-as-code and therefore do contribute to control readiness."
                    />
                )}
                <Datatable
                    isLoading={isLoading}
                    tableId="MonitoringDetailsControlsView"
                    data-id="MonitoringDetailsControlsView"
                    data={monitoringDetailsControlsData}
                    total={monitoringDetailsControlsTotal}
                    columns={controlsColumns}
                    data-testid="MonitoringDetailsControlsView"
                    tableActions={[
                        {
                            actionType: 'button',
                            id: 'map-controls-button',
                            typeProps: {
                                label: 'Map control',
                                level: 'secondary',
                            },
                        },
                    ]}
                    tableSearchProps={{
                        hideSearch: true,
                    }}
                    emptyStateProps={{
                        title: 'Monitoring Details Controls View ',
                        description: 'description',
                    }}
                    filterViewModeProps={{
                        props: {
                            selectedOption: 'pinned',
                            initialSelectedOption: 'pinned',
                            togglePinnedLabel: 'Pin filters to page',
                            toggleUnpinnedLabel: 'Move filters to dropdown',
                        },
                        viewMode: 'toggleable',
                    }}
                    onRowSelection={noop}
                    onRowClick={handleOpenControlPanel}
                    onFetchData={(params) => {
                        loadMonitoringDetailsControls(testId, params);
                    }}
                />
            </Stack>
        );
    },
);

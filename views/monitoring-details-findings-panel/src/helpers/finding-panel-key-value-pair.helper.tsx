import { filter } from 'lodash-es';
import type {
    KeyValuePairProps,
    Value,
} from '@cosmos/components/key-value-pair';
import { DateTime } from '@cosmos-lab/components/date-time';
import type { MonitorV2FindingResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { type BaseProvider, providers } from '@globals/providers';

const getInfrastructureMetadataKeyValuePairs = ({
    finding,
    testLifecycle,
    createdDate,
    cacFindingSeverity,
}: {
    finding: MonitorV2FindingResponseDto;
    testLifecycle: string;
    createdDate: string;
    cacFindingSeverity?: Value;
}): KeyValuePairProps[] => {
    const { testSource, clientType, connectionAlias } = finding;

    // Cast is required here (for now) until we resolve the multiverse issue of front-end-only and api-only client types
    const providerInfo = providers[clientType as keyof typeof providers] as
        | BaseProvider
        | undefined;
    const basePairs: (KeyValuePairProps | undefined)[] = [
        {
            label: t`Test lifecycle`,
            value: testLifecycle,
            type: 'TEXT',
        },
        {
            label: t`Date created`,
            value: <DateTime date={createdDate} format="field" />,
            type: 'REACT_NODE',
        },
        providerInfo && {
            label: t`Connection`,
            value: {
                username: connectionAlias ?? providerInfo.name,
                avatarProps: {
                    imgSrc: providerInfo.logo,
                    imgAlt: providerInfo.name,
                    fallbackText: providerInfo.name,
                },
            },
            type: 'USER',
        },
    ];

    if (testSource === 'ACORN' && cacFindingSeverity) {
        basePairs.unshift({
            id: 'finding-details-panel-test-result',
            label: t`Severity`,
            value: cacFindingSeverity,
            type: 'BADGE',
        });
    }

    return filter(basePairs, (bp) => bp !== undefined);
};

export const getMetadataKeyValuePairs = ({
    finding,
    testLifecycle,
    createdDate,
    cacFindingSeverity,
}: {
    finding: MonitorV2FindingResponseDto;
    testLifecycle: string;
    createdDate: string;
    cacFindingSeverity?: Value;
}): KeyValuePairProps[] => {
    const { checkType, clientType, connectionAlias } = finding;

    // Cast is required here (for now) until we resolve the multiverse issue of front-end-only and api-only client types
    const providerInfo = providers[clientType as keyof typeof providers] as
        | BaseProvider
        | undefined;

    switch (checkType) {
        case 'INFRASTRUCTURE': {
            return getInfrastructureMetadataKeyValuePairs({
                finding,
                testLifecycle,
                createdDate,
                cacFindingSeverity,
            });
        }
        case 'TICKETING':
        case 'IN_DRATA':
        case 'POLICY':
        default: {
            const pairs: (KeyValuePairProps | undefined)[] = [
                {
                    label: t`Test lifecycle`,
                    value: testLifecycle,
                    type: 'TEXT',
                },
                {
                    label: t`Date created`,
                    value: <DateTime date={createdDate} format="field" />,
                    type: 'REACT_NODE',
                },
                providerInfo && {
                    label: t`Connection`,
                    value: {
                        username: connectionAlias ?? providerInfo.name,
                        avatarProps: {
                            imgSrc: providerInfo.logo,
                            imgAlt: providerInfo.name,
                            fallbackText: providerInfo.name,
                        },
                    },
                    type: 'USER',
                },
            ];

            return filter(pairs, (pair) => pair !== undefined);
        }
    }
};

import { monitorsV2ControllerGetFindingByIdOptions } from '@globals/api-sdk/queries';
import type {
    MonitorsV2ControllerGetFindingByIdData,
    MonitorV2FindingResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, ObservedQuery, when } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';

export class MonitoringDetailsFindingsPanelController {
    constructor() {
        makeAutoObservable(this);
    }

    findingByIdQuery = new ObservedQuery(
        monitorsV2ControllerGetFindingByIdOptions,
    );

    get finding(): MonitorV2FindingResponseDto | null {
        return this.findingByIdQuery.data ?? null;
    }

    get isLoading(): boolean {
        return this.findingByIdQuery.isLoading;
    }

    get isFetching(): boolean {
        return this.findingByIdQuery.isFetching;
    }

    get hasError(): boolean {
        return Boolean(this.findingByIdQuery.error);
    }

    get error(): Error | null {
        return this.findingByIdQuery.error;
    }

    loadFinding = (params: {
        testId: number;
        connectionId: number;
        findingId: string;
        drataDevice?: string;
        serialNumber?: string;
    }): void => {
        when(
            () => Boolean(sharedWorkspacesController.currentWorkspace),
            () => {
                const workspace = sharedWorkspacesController.currentWorkspace;

                if (!workspace) {
                    return;
                }

                const workspaceId = workspace.id;

                const query: Record<string, string> = {};

                if (params.drataDevice) {
                    query.drataDevice = params.drataDevice;
                }
                if (params.serialNumber) {
                    query.serialNumber = params.serialNumber;
                }

                this.findingByIdQuery.load({
                    path: {
                        workspaceId,
                        testId: params.testId,
                        connectionId: params.connectionId,
                        findingId: params.findingId,
                    },
                    query: query as MonitorsV2ControllerGetFindingByIdData['query'], // Type assertion needed due to API type constraints
                });
            },
        );
    };
}

export const sharedMonitoringDetailsFindingsPanelController =
    new MonitoringDetailsFindingsPanelController();

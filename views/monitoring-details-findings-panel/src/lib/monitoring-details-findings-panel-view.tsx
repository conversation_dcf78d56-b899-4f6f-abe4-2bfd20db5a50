import { isEmpty } from 'lodash-es';
import { useEffect } from 'react';
import { panelController } from '@controllers/panel';
import { Accordion } from '@cosmos/components/accordion';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Loader } from '@cosmos/components/loader';
import {
    PanelBody,
    PanelControls,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { CodeViewer } from '@cosmos-lab/components/code-viewer';
import { Divider } from '@cosmos-lab/components/divider';
import { PanelSection } from '@cosmos-lab/components/panel-section';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { codeToJSONString } from '@helpers/download-file';
import { MonitoringDetailsFindingPanelActionCard } from './monitoring-details-finding-panel-action-card';
import { sharedMonitoringDetailsFindingsPanelModel } from './monitoring-details-findings-panel-model';

export const MonitoringDetailsFindingsPanelView = observer(
    (): React.JSX.Element | undefined => {
        const {
            findingName,
            hasActionCard,
            finding,
            validMetadataKeyValuePairs,
            validDetailsKeyValuePairs,
            findingsList,
            selectedFindingIndex,
            paginatePanelContent,
            selectedFindingItem,
            isLoading,
        } = sharedMonitoringDetailsFindingsPanelModel;

        useEffect(() => {
            if (!selectedFindingItem) {
                return;
            }

            runInAction(() => {
                const { testId, connectionId, id, drataDevice, serialNumber } =
                    selectedFindingItem;

                sharedMonitoringDetailsFindingsPanelModel.loadFinding({
                    testId,
                    connectionId,
                    findingId: id,
                    drataDevice,
                    serialNumber,
                });
            });
        }, [selectedFindingItem]);

        if (isLoading) {
            return (
                <Stack
                    align="center"
                    justify="center"
                    height="100%"
                    width="100%"
                >
                    <Loader size="lg" label={t`Loading...`} />
                </Stack>
            );
        }

        if (!finding) {
            return;
        }

        return (
            <>
                <PanelControls
                    closeButtonLabel={t`Close`}
                    data-id="monitoring-finding-panel-controls"
                    pagination={{
                        currentItem: selectedFindingIndex + 1,
                        onNextPageClick: () => {
                            runInAction(() => {
                                paginatePanelContent(1);
                            });
                        },
                        onPrevPageClick: () => {
                            runInAction(() => {
                                paginatePanelContent(-1);
                            });
                        },
                        totalItems: findingsList.length,
                    }}
                    onClose={() => {
                        runInAction(() => {
                            panelController.closePanel();
                        });
                    }}
                />
                <PanelHeader
                    data-id="monitoring-finding-panel-header"
                    title={findingName}
                />
                <PanelBody data-id="monitoring-finding-panel-body">
                    <Stack direction="column" gap="8x">
                        {hasActionCard && (
                            <MonitoringDetailsFindingPanelActionCard />
                        )}
                        <Text>{finding.testDescription}</Text>
                        <Stack gap="8x" direction="row">
                            {validMetadataKeyValuePairs.map((props) => (
                                <KeyValuePair
                                    key={props.label}
                                    {...props}
                                    data-id="UMxA3JJw"
                                />
                            ))}
                        </Stack>
                        <Divider />
                        {!isEmpty(validDetailsKeyValuePairs.flat()) && (
                            <PanelSection
                                showBorderBottom
                                title={t`Finding details`}
                                body={validDetailsKeyValuePairs
                                    .filter((details) => !isEmpty(details))
                                    .map((detailSection, i) => (
                                        <Stack
                                            key={`finding-panel-detail-${detailSection[i]?.label}`}
                                            data-id="W81lKOS0"
                                            gap="6x"
                                            direction="column"
                                        >
                                            {detailSection.map((kvp) => (
                                                <KeyValuePair
                                                    key={`${finding.checkType}-${kvp.label}`}
                                                    label={kvp.label}
                                                    value={kvp.value}
                                                    type={kvp.type}
                                                    data-id="fiL5-meY"
                                                />
                                            ))}
                                        </Stack>
                                    ))}
                            />
                        )}
                        <Accordion
                            title={t`Raw JSON data`}
                            body={
                                <CodeViewer
                                    language="json"
                                    value={codeToJSONString(
                                        finding.connectionMonitorResult,
                                    )}
                                />
                            }
                        />
                    </Stack>
                </PanelBody>
            </>
        );
    },
);

import { isEmpty } from 'lodash-es';
import { openMonitoringExclusionModal } from '@components/monitoring-exclusion-modal';
import {
    sharedCodebaseFindingExclusionMutationController,
    sharedFindingExclusionMutationController,
    sharedFindingsController,
    sharedFindingsFiltersController,
} from '@controllers/monitoring-details';
import { panelController } from '@controllers/panel';
import type { Action } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import type {
    FindingItemResponseDto,
    MonitorV2FindingResponseDto,
} from '@globals/api-sdk/types';
import { makeAutoObservable, runInAction, when } from '@globals/mobx';
import { getCardActions } from '../helpers/finding-panel-action-card.helper';
import { getDetailsKeyValuePairs } from '../helpers/finding-panel-details-key-value-pair.helper';
import { getMetadataKeyValuePairs } from '../helpers/finding-panel-key-value-pair.helper';
import { getTestLifecycleLabel } from '../helpers/finding-panel-test-lifecycle.helper';
import { sharedMonitoringDetailsFindingsPanelController } from './monitoring-details-findings-panel-controller';

export class MonitoringDetailsFindingsPanelModel {
    selectedFindingItem: FindingItemResponseDto | undefined;

    constructor() {
        makeAutoObservable(this);
    }

    get findingsList(): FindingItemResponseDto[] {
        return sharedFindingsController.findingsList;
    }

    get selectedFindingIndex(): number {
        return this.findingsList.findIndex(
            (findingItem) =>
                findingItem.findingId === this.selectedFindingItem?.findingId,
        );
    }

    paginatePanelContent = (index: number): void => {
        const newIndex = this.selectedFindingIndex + index;

        if (newIndex < 0 || newIndex >= this.findingsList.length) {
            return;
        }

        const newFindingItem = this.findingsList[newIndex];

        this.selectedFindingItem = newFindingItem;

        // Load the new finding details
        this.loadFinding({
            testId: newFindingItem.testId,
            connectionId: newFindingItem.connectionId,
            findingId: newFindingItem.id,
            drataDevice: newFindingItem.drataDevice,
            serialNumber: newFindingItem.serialNumber,
        });
    };

    switchToNextItemAfterExclusion = (indexBeforeExclusion: number): void => {
        const currentIndex = indexBeforeExclusion;

        if (isEmpty(this.findingsList)) {
            this.selectedFindingItem = undefined;
            panelController.closePanel();

            return;
        }

        let nextIndex = currentIndex;

        if (nextIndex >= this.findingsList.length) {
            nextIndex = this.findingsList.length - 1;
        }

        if (nextIndex >= 0 && nextIndex < this.findingsList.length) {
            const nextFindingItem = this.findingsList[nextIndex];

            this.selectedFindingItem = nextFindingItem;

            // Load the new finding details
            this.loadFinding({
                testId: nextFindingItem.testId,
                connectionId: nextFindingItem.connectionId,
                findingId: nextFindingItem.id,
                drataDevice: nextFindingItem.drataDevice,
                serialNumber: nextFindingItem.serialNumber,
            });
        } else {
            this.selectedFindingItem = undefined;
            panelController.closePanel();
        }
    };

    get finding(): MonitorV2FindingResponseDto | null {
        return sharedMonitoringDetailsFindingsPanelController.finding;
    }

    get isLoading(): boolean {
        return (
            sharedMonitoringDetailsFindingsPanelController.isLoading ||
            sharedMonitoringDetailsFindingsPanelController.isFetching
        );
    }

    get hasError(): boolean {
        return sharedMonitoringDetailsFindingsPanelController.hasError;
    }

    get error(): Error | null {
        return sharedMonitoringDetailsFindingsPanelController.error;
    }

    get findingName(): string {
        return this.finding?.resourceName ?? '';
    }

    get findingId(): string {
        return this.finding?.findingId ?? '';
    }

    get testId(): number {
        return this.finding?.testId ?? 0;
    }

    get testDescription(): string {
        return this.finding?.testDescription ?? '';
    }

    get connectionId(): number {
        return this.finding?.connectionId ?? 0;
    }

    get connectionAlias(): string {
        return this.finding?.connectionAlias ?? '';
    }

    get createdAt(): string {
        return this.finding?.createdAt ?? '';
    }

    get resourceName(): string {
        return this.finding?.resourceName ?? '';
    }

    get remedy(): string {
        return this.finding?.remedy ?? '';
    }

    get clientType(): string {
        return this.finding?.clientType ?? '';
    }

    get checkType(): string {
        return this.finding?.checkType ?? '';
    }

    get testSource(): MonitorV2FindingResponseDto['testSource'] | undefined {
        return this.finding?.testSource;
    }

    get metadata(): Record<string, unknown> {
        return this.finding?.metadata ?? {};
    }

    get connectionMonitorResult(): Record<string, unknown> {
        return this.finding?.connectionMonitorResult ?? {};
    }

    get hasActionCard(): boolean {
        return this.testSource !== 'CUSTOM';
    }

    get testLifecycle(): string {
        return getTestLifecycleLabel(this.testSource);
    }

    /**
     * May return key value pair props with empty values. Use `validMetadataKeyValuePairs` to only get fully hydrated props instead.
     */
    private get metadataKeyValuePairs(): KeyValuePairProps[] {
        if (!this.finding) {
            return [];
        }

        const { metadata, createdAt } = this.finding;
        const { raw } = metadata;
        const { severity } = (raw as { severity: string } | undefined) ?? {};

        return getMetadataKeyValuePairs({
            finding: this.finding,
            testLifecycle: this.testLifecycle,
            createdDate: createdAt,
            cacFindingSeverity: severity,
        }).filter((kvp) => {
            if (Array.isArray(kvp.value)) {
                return !isEmpty(kvp.value);
            }

            return Boolean(kvp.value);
        });
    }

    get validMetadataKeyValuePairs(): KeyValuePairProps[] {
        return this.metadataKeyValuePairs.filter((kvp) => {
            if (Array.isArray(kvp.value)) {
                return !isEmpty(kvp.value);
            }

            return Boolean(kvp.value);
        });
    }

    /**
     * May return key value pair props with empty values. Use `validDetailsKeyValuePairs` to only get fully hydrated props instead.
     */
    private get detailsKeyValuePairs(): KeyValuePairProps[][] {
        const { finding, metadata } = this;

        if (!finding) {
            return [];
        }

        if (finding.checkType === 'AGENT') {
            const rawAgentMetadata: {
                devices?: unknown[];
                drataDevice?: unknown;
            } = metadata.raw ?? {};

            const deviceArray = rawAgentMetadata.devices ?? [
                rawAgentMetadata.drataDevice,
            ];
            const remadeFindings: MonitorV2FindingResponseDto[] =
                deviceArray.map((device: unknown) => {
                    return {
                        ...finding,
                        metadata: {
                            ...finding.metadata,
                            raw: device,
                        },
                    };
                });

            return remadeFindings.map(
                (remadeFinding: MonitorV2FindingResponseDto) =>
                    getDetailsKeyValuePairs({ finding: remadeFinding }),
            );
        }

        return [getDetailsKeyValuePairs({ finding })];
    }

    get validDetailsKeyValuePairs(): KeyValuePairProps[][] {
        return this.detailsKeyValuePairs.map((details) =>
            details.filter((kvp) => {
                if (Array.isArray(kvp.value)) {
                    return !isEmpty(kvp.value);
                }

                return Boolean(kvp.value);
            }),
        );
    }

    loadFinding = (params: {
        testId: number;
        connectionId: number;
        findingId: string;
        drataDevice?: string;
        serialNumber?: string;
    }): void => {
        sharedMonitoringDetailsFindingsPanelController.loadFinding(params);
    };

    /**
     * Action Card Methods.
     */
    get canManageExclusions(): boolean {
        return (
            sharedFindingsFiltersController.findingsFilters
                ?.canManageExclusions ?? false
        );
    }

    get shouldShowCodeBlock(): boolean {
        return this.testSource === 'ACORN';
    }

    /**
     * Creates the exclusion callback for monitoring findings.
     */
    createMonitoringExclusionCallback = (
        currentFinding: MonitorV2FindingResponseDto,
    ) => {
        return (reason: string): void => {
            runInAction(() => {
                sharedFindingExclusionMutationController.createExclusion(
                    currentFinding,
                    reason,
                );
            });

            when(
                () =>
                    !sharedFindingExclusionMutationController
                        .createExclusionMutation.isPending,
                () => {
                    if (
                        !sharedFindingExclusionMutationController
                            .createExclusionMutation.response
                    ) {
                        return;
                    }

                    const currentFindingId =
                        this.selectedFindingItem?.findingId;
                    const indexBeforeExclusion = this.selectedFindingIndex;

                    when(
                        () => {
                            return !this.findingsList.some(
                                (item) => item.findingId === currentFindingId,
                            );
                        },
                        () => {
                            this.switchToNextItemAfterExclusion(
                                indexBeforeExclusion,
                            );
                        },
                    );
                },
            );
        };
    };

    /**
     * Creates the exclusion callback for codebase findings.
     */
    createCodebaseExclusionCallback = (
        currentFinding: MonitorV2FindingResponseDto,
    ) => {
        return (reason: string): void => {
            const { raw } = currentFinding.metadata;
            const repositoryName =
                (raw as { repositoryName?: string }).repositoryName || '';

            const codebaseFinding = {
                findingId: currentFinding.findingId,
                testId: currentFinding.testId,
                repositoryName,
            };

            runInAction(() => {
                sharedCodebaseFindingExclusionMutationController.createExclusion(
                    codebaseFinding,
                    reason,
                );
            });

            when(
                () =>
                    !sharedCodebaseFindingExclusionMutationController
                        .createExclusionMutation.isPending,
                () => {
                    if (
                        !sharedCodebaseFindingExclusionMutationController
                            .createExclusionMutation.response
                    ) {
                        return;
                    }

                    const currentFindingId =
                        this.selectedFindingItem?.findingId;
                    const indexBeforeExclusion = this.selectedFindingIndex;

                    when(
                        () => {
                            return !this.findingsList.some(
                                (item) => item.findingId === currentFindingId,
                            );
                        },
                        () => {
                            this.switchToNextItemAfterExclusion(
                                indexBeforeExclusion,
                            );
                        },
                    );
                },
            );
        };
    };

    excludeFindingHandler = (): void => {
        if (!this.finding) {
            return;
        }

        const currentFinding = this.finding;
        const isCodebaseFinding = this.testSource === 'ACORN';

        const exclusionCallback = isCodebaseFinding
            ? this.createCodebaseExclusionCallback(currentFinding)
            : this.createMonitoringExclusionCallback(currentFinding);

        openMonitoringExclusionModal(currentFinding, exclusionCallback);
    };

    get actionCardActions(): Action[] {
        if (!this.finding) {
            return [];
        }

        return getCardActions({
            finding: this.finding,
            excludeFindingHandler: this.excludeFindingHandler,
            canManageExclusions: this.canManageExclusions,
        });
    }
}

export const sharedMonitoringDetailsFindingsPanelModel =
    new MonitoringDetailsFindingsPanelModel();

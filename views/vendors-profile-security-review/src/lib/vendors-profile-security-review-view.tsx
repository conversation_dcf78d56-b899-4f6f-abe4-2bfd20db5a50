import { type default as React, useEffect } from 'react';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import { Card } from '@cosmos/components/card';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';
import { useNavigate, useParams } from '@remix-run/react';
import { VendorsSecurityReviewFilesComponent } from './components/vendors-security-review-files-component';
import {
    SECURITY_REVIEW_FILES_CELLS,
    SECURITY_REVIEW_QUESTIONNAIRES_CELLS,
    VENDORS_PROFILE_SECURITY_REVIEW_ADD_FILES_ACTIONS,
    VENDORS_PROFILE_SECURITY_REVIEW_ADD_QUESTIONNAIRE_ACTIONS,
} from './constants/vendors-profile-security-review.constants';

export const VendorsProfileSecurityReviewView = observer(
    (): React.JSX.Element => {
        const { workspaceId, vendorId, securityReviewId } = useParams();
        const navigate = useNavigate();

        const { files = [], questionnaires = [] } =
            sharedVendorsSecurityReviewDocumentsController;

        const { securityReviewDetails, isLoading: isSecurityReviewLoading } =
            sharedVendorsSecurityReviewDetailsController;

        const { isProspectiveVendor } = sharedVendorsDetailsController;

        useEffect(() => {
            // If security review is not completed or still loading, do nothing
            if (
                securityReviewDetails?.status !== 'COMPLETED' ||
                isSecurityReviewLoading
            ) {
                return;
            }

            // Redirect if security review is completed
            const vendorType = isProspectiveVendor ? 'prospective' : 'current';

            navigate(
                `/workspaces/${workspaceId}/vendors/${vendorType}/${vendorId}/security-reviews/${securityReviewId}/completed`,
            );
        }, [
            securityReviewDetails?.status,
            isSecurityReviewLoading,
            navigate,
            workspaceId,
            vendorId,
            securityReviewId,
            isProspectiveVendor,
        ]);

        return (
            <Stack
                gap="xl"
                direction="column"
                data-testid="VendorsProfileSecurityReviewView"
                data-id="WwQyGcrv"
            >
                <Card
                    data-id="vendors-profile-security-review-add-files-card"
                    title="Files"
                    actions={VENDORS_PROFILE_SECURITY_REVIEW_ADD_FILES_ACTIONS}
                    body={
                        <VendorsSecurityReviewFilesComponent
                            data-testid="VendorsSecurityReviewFilesComponent"
                            data-id="WwQyGcrv"
                            data={files}
                            columns={SECURITY_REVIEW_FILES_CELLS}
                        />
                    }
                />
                <Card
                    data-id="vendors-profile-security-review-add-questionnaires-card"
                    title="Questionnaires"
                    body={
                        <VendorsSecurityReviewFilesComponent
                            data-testid="VendorsSecurityReviewQuestionnairesComponent"
                            data-id="WwQyGcrv"
                            data={questionnaires}
                            columns={SECURITY_REVIEW_QUESTIONNAIRES_CELLS}
                        />
                    }
                    actions={
                        VENDORS_PROFILE_SECURITY_REVIEW_ADD_QUESTIONNAIRE_ACTIONS
                    }
                />
            </Stack>
        );
    },
);

import { RiskCategoryCell } from '@components/risk-register';
import type { DatatableProps } from '@cosmos/components/datatable';
import type { RiskLibraryResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { ActionButtonCell } from '../cells/action-button-cell';
import { ReadinessCell } from '../cells/readiness-cell';
import { RiskDescriptionCell } from '../cells/risk-description-cell';
import { RiskNameCell } from '../cells/risk-name-cell';

export const getRiskColumns =
    (): DatatableProps<RiskLibraryResponseDto>['columns'] => {
        return [
            {
                id: 'risk-insights-action-button',
                isActionColumn: true,
                cell: ActionButtonCell,
                meta: {
                    shouldIgnoreRowClick: true,
                },
                maxSize: 70,
            },
            {
                accessorKey: 'title',
                header: t`Name`,
                id: 'name',
                enableSorting: true,
                cell: RiskNameCell,
            },
            {
                accessorKey: 'description',
                header: t`Description`,
                id: 'description',
                enableSorting: false,
                cell: RiskDescriptionCell,
            },
            {
                accessorKey: 'controls',
                header: t`Controls`,
                id: 'readiness',
                enableSorting: false,
                cell: ReadinessCell,
                size: 150,
            },
            {
                accessorKey: 'categories',
                header: t`Categories`,
                id: 'categories',
                enableSorting: false,
                cell: RiskCategoryCell,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
        ];
    };

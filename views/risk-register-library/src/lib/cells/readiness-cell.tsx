import { Metadata } from '@cosmos/components/metadata';
import type { RiskLibraryResponseDto } from '@globals/api-sdk/types';

export const ReadinessCell = ({
    row: { original },
}: {
    row: { original: RiskLibraryResponseDto };
}): React.ReactNode => {
    const { controls } = original;

    return (
        <Metadata
            label={controls.length.toString()}
            colorScheme="neutral"
            type="number"
            data-testid="ReadinessCell"
            data-id="Dbrur44A"
        />
    );
};

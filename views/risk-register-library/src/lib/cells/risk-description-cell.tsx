import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { Truncation } from '@cosmos-lab/components/truncation';
import type { RiskLibraryResponseDto } from '@globals/api-sdk/types';

const MAX_DESCRIPTION_LENGTH = 150;

export const RiskDescriptionCell = ({
    row: { original },
}: {
    row: { original: RiskLibraryResponseDto };
}): React.ReactNode => {
    const { description } = original;

    if (description.length <= MAX_DESCRIPTION_LENGTH) {
        return (
            <Text data-testid="RiskDescriptionCell" data-id="ONFzM8sD">
                {description}
            </Text>
        );
    }

    return (
        <Tooltip
            text={description}
            data-testid="RiskDescriptionCell"
            data-id="ONFzM8sD"
        >
            <Text>
                <Truncation maxLength={MAX_DESCRIPTION_LENGTH}>
                    {description}
                </Truncation>
            </Text>
        </Tooltip>
    );
};

import { isNil } from 'lodash-es';
import { openEvidenceOwnerModal } from '@components/evidence-library';
import { Button } from '@cosmos/components/button';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import type { EvidenceUnionResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { getFullName, getInitials } from '@helpers/formatters';

export const OwnerCell = observer(
    ({
        row: { original },
    }: {
        row: { original: EvidenceUnionResponseDto };
    }): React.JSX.Element => {
        const { owner, evidenceId } = original;
        const { hasWriteEvidenceLibraryPermission } = sharedFeatureAccessModel;

        if (isNil(owner)) {
            if (!hasWriteEvidenceLibraryPermission) {
                return <EmptyValue label="—" />;
            }

            return (
                <Button
                    size="sm"
                    type="button"
                    level="secondary"
                    colorScheme="neutral"
                    label={t`Assign owner`}
                    startIconName="UserCircleSingle"
                    onClick={() => {
                        openEvidenceOwnerModal({
                            evidenceIds: [evidenceId],
                            objectType: 'controls',
                        });
                    }}
                />
            );
        }

        const { firstName, lastName, avatarUrl, email } = owner;
        const fullName = getFullName(firstName, lastName);
        const initials = getInitials(fullName);

        return (
            <AvatarIdentity
                key={fullName}
                fallbackText={initials}
                primaryLabel={fullName}
                imgSrc={avatarUrl ?? ''}
                secondaryLabel={email}
                data-testid="OwnerCell"
                data-id="zdtgQSST"
            />
        );
    },
);

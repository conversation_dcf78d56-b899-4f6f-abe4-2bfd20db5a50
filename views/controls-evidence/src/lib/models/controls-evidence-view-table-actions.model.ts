import { isEmpty } from 'lodash-es';
import { openMapEvidenceModal } from '@components/controls';
import {
    sharedControlDetailsController,
    sharedControlEvidenceController,
    sharedControlsDownloadController,
} from '@controllers/controls';
import type { DatatableProps, TableAction } from '@cosmos/components/datatable';
import type { EvidenceUnionResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import {
    ActionCell,
    ControlsCell,
    OwnerCell,
    SourceCell,
    StatusCell,
} from '../components';
import { openAddMiscellaneousEvidenceControlModal } from '../helpers/add-evidence-control-modal.helper';

export class ControlsEvidenceViewTableModel {
    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Private action handlers.
     */
    handleDownloadEvidence = (): void => {
        const { controlId } = sharedControlEvidenceController;
        const { downloadControlsEvidence } = sharedControlsDownloadController;

        if (controlId) {
            runInAction(() => {
                downloadControlsEvidence(controlId);
            });
        }
    };

    handleMapEvidenceLibrary = (): void => {
        const { controlId } = sharedControlDetailsController;

        if (!controlId) {
            return;
        }

        openMapEvidenceModal({ controlId });
    };

    handleMapMiscellaneousEvidence = (): void => {
        openAddMiscellaneousEvidenceControlModal();
    };

    /**
     * Table columns configuration.
     */
    get tableColumns(): DatatableProps<EvidenceUnionResponseDto>['columns'] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        return [
            ...(hasWriteControlPermission
                ? [
                      {
                          id: 'actions',
                          cell: ActionCell,
                          accessorKey: 'evidenceId',
                          header: '',
                          enableSorting: false,
                          enableHiding: true,
                          meta: {
                              shouldIgnoreRowClick: true,
                          },
                          minSize: COLUMN_SIZES.XS,
                          maxSize: COLUMN_SIZES.XS,
                      },
                  ]
                : []),
            {
                accessorKey: 'name',
                header: t`Name`,
                id: 'name',
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'status',
                header: t`Status`,
                id: 'status',
                enableSorting: true,
                cell: StatusCell,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                header: t`Source`,
                id: 'source',
                accessorKey: 'source',
                enableSorting: true,
                cell: SourceCell,
                minSize: COLUMN_SIZES.MEDIUM,
            },
            {
                accessorKey: 'controls',
                header: t`Controls`,
                id: 'controls',
                enableSorting: false,
                cell: ControlsCell,
                minSize: COLUMN_SIZES.LARGE,
            },
            {
                accessorKey: 'evidenceId',
                header: t`Owner`,
                id: 'owner',
                enableSorting: false,
                cell: OwnerCell,
                minSize: COLUMN_SIZES.LARGE,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
        ];
    }

    /**
     * Table actions configuration.
     */
    get downloadAction(): TableAction {
        const { isDownloadControlsEvidenceLoading } =
            sharedControlsDownloadController;

        return {
            actionType: 'button',
            id: 'download-evidence-button',
            typeProps: {
                startIconName: 'Download',
                label: t`Download`,
                level: 'tertiary',
                colorScheme: 'neutral',
                isLoading: isDownloadControlsEvidenceLoading,
                onClick: this.handleDownloadEvidence,
            },
        };
    }

    get mapEvidenceAction(): TableAction {
        return {
            actionType: 'dropdown',
            id: 'map-evidence-dropdown',
            typeProps: {
                label: t`Map evidence`,
                level: 'secondary',
                items: [
                    {
                        id: 'map-evidence-library-item',
                        label: t`Evidence Library (recommended)`,
                        type: 'item',
                        value: 'mapEvidenceLibrary',
                        onSelect: this.handleMapEvidenceLibrary,
                    },
                    {
                        id: 'map-miscellaneous-evidence-item',
                        label: t`Miscellaneous evidence`,
                        type: 'item',
                        value: 'mapMiscellaneousEvidence',
                        onSelect: this.handleMapMiscellaneousEvidence,
                    },
                ],
            },
        };
    }

    get tableActions(): TableAction[] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;
        const { controlEvidence } = sharedControlEvidenceController;

        if (!hasWriteControlPermission) {
            return [];
        }

        if (isEmpty(controlEvidence)) {
            return [this.mapEvidenceAction];
        }

        return [this.downloadAction, this.mapEvidenceAction];
    }
}

export const sharedControlsEvidenceViewTableModel =
    new ControlsEvidenceViewTableModel();

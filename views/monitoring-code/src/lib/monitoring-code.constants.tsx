import {
    MonitoringCodeTableCellStatusComponent,
    MonitoringTableCellFindingsComponent,
    MonitoringTableCellResultComponent,
    MonitoringTableCellTestNameComponent,
} from '@components/monitoring';
import { sharedMonitoringCodeFiltersController } from '@controllers/monitoring';
import type { DatatableProps, FilterProps } from '@cosmos/components/datatable';
import type { MonitorTestInstanceResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { computed, runInAction } from '@globals/mobx';
import { MonitoringCodeTableCellActionsComponent } from './components/monitoring-code-table-cell-actions.component';

export const getMonitoringCodeColumns =
    (): DatatableProps<MonitorTestInstanceResponseDto>['columns'] => [
        {
            header: '',
            id: 'action',
            isActionColumn: true,
            accessorKey: 'testId',
            enableSorting: false,
            meta: { shouldIgnoreRowClick: true },
            cell: MonitoringCodeTableCellActionsComponent,
        },
        {
            accessorKey: 'testName',
            header: t`Name`,
            id: 'name',
            enableSorting: false,
            minSize: 300,
            cell: MonitoringTableCellTestNameComponent,
        },
        {
            accessorKey: 'connections',
            header: t`Result`,
            id: 'checkResultStatus',
            enableSorting: true,
            cell: MonitoringTableCellResultComponent,
        },
        {
            accessorKey: 'findingsCount',
            header: t`Findings`,
            id: 'findingsCount',
            enableSorting: true,
            cell: MonitoringTableCellFindingsComponent,
        },
        {
            accessorKey: 'connections',
            header: t`Status`,
            id: 'checkStatus',
            enableSorting: true,
            meta: {
                shouldIgnoreRowClick: true,
            },
            cell: MonitoringCodeTableCellStatusComponent,
        },
    ];
const CHECK_STATUS_ENUM = {
    ENABLED: 'ENABLED',
    DISABLED: 'DISABLED',
    UNUSED: 'UNUSED',
    TESTING: 'TESTING',
};

export const getMonitoringCodeFilters = computed((): FilterProps => {
    return {
        clearAllButtonLabel: t`Reset`,
        triggerLabel: t`Filters`,
        filters: [
            {
                filterType: 'checkbox',
                id: 'isNew',
                label: '',
                options: [
                    {
                        label: t`New`,
                        value: 'NEW',
                    },
                ],
            },
            {
                filterType: 'checkbox',
                id: 'allowedCheckResultStatuses',
                label: t`Result`,
                options: [
                    {
                        label: t`Passing`,
                        value: 'PASSED',
                    },
                    {
                        label: t`Failing`,
                        value: 'FAILED',
                    },
                    {
                        label: t`Error`,
                        value: 'ERROR',
                    },
                ],
            },
            {
                filterType: 'checkbox',
                id: 'allowedStatuses',
                label: t`Status`,
                options: [
                    {
                        label: t`Enabled`,
                        value: CHECK_STATUS_ENUM.ENABLED,
                    },
                    {
                        label: t`Disabled`,
                        value: CHECK_STATUS_ENUM.DISABLED,
                    },
                    {
                        label: t`Unused`,
                        value: CHECK_STATUS_ENUM.UNUSED,
                    },
                    {
                        label: t`Testing`,
                        value: CHECK_STATUS_ENUM.TESTING,
                    },
                ],
            },
            {
                filterType: 'checkbox',
                id: 'hasExclusions',
                label: t`Exclusions`,
                options: [
                    {
                        label: t`Has Exclusions`,
                        value: 'true',
                    },
                ],
            },
            {
                filterType: 'combobox',
                id: 'allowedControls',
                isMultiSelect: true,
                placeholder: t`Select all that apply`,
                label: t`Control`,
                options: sharedMonitoringCodeFiltersController.controlOptions,
                onFetchOptions: ({ search }) => {
                    runInAction(() => {
                        sharedMonitoringCodeFiltersController.searchControlOptions(
                            {
                                search,
                            },
                        );
                    });
                },
                isLoading: sharedMonitoringCodeFiltersController.isLoading,
            },
            {
                filterType: 'combobox',
                id: 'allowedFrameworks',
                isMultiSelect: true,
                placeholder: t`Select all that apply`,
                label: t`Framework`,
                options: sharedMonitoringCodeFiltersController.frameworkOptions,
                onFetchOptions: ({ search }) => {
                    runInAction(() => {
                        sharedMonitoringCodeFiltersController.searchFrameworkOptions(
                            { search },
                        );
                    });
                },
                isLoading: sharedMonitoringCodeFiltersController.isLoading,
            },
            {
                filterType: 'checkbox',
                id: 'allowedTicketStatuses',
                label: t`Tickets`,
                options: [
                    {
                        label: t`In Progress`,
                        value: 'IN_PROGRESS',
                    },
                    {
                        label: t`Done`,
                        value: 'ARCHIVED',
                    },
                ],
            },
        ],
    };
});

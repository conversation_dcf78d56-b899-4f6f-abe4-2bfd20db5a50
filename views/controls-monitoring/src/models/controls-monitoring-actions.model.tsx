import { handleOpenCompareToDefaultsModal } from '@components/controls';
import { openMapAutomatedTestModal } from '@components/map-tests-modal';
import { sharedControlDetailsController } from '@controllers/controls';
import { sharedMonitorsController } from '@controllers/monitors';
import { Button } from '@cosmos/components/button';
import type { DatatableProps, TableAction } from '@cosmos/components/datatable';
import type { ControlTestResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { sharedWorkspacesController } from '@globals/workspaces';
import { sharedCompareToDefaultsMonitoringModalModel } from '@models/controls';
import { FORM_ID, MAP_TESTS_MODAL_ID } from '../constants/modal.constant';
import { canManageControlTests } from '../helpers/can-manage-control-tests.helpers';
import { sharedMonitoringMapTestsModel } from './monitoring-map-tests.model';

export class ControlsMonitoringActionsModel {
    get emptyStateProps(): DatatableProps<ControlTestResponseDto>['emptyStateProps'] {
        const { currentWorkspace } = sharedWorkspacesController;
        const { hasFilters } = sharedMonitorsController;

        if (!currentWorkspace) {
            return undefined;
        }

        if (hasFilters) {
            return {
                title: t`No monitors found`,
                description: t`Try adjusting your filters or search terms. If monitors exist for this control, they’ll show up here.`,
                illustrationName: 'Warning',
            };
        }

        return {
            title: t`Continuously monitor the effectiveness of your control`,
            description: t`See a history of how tests mapped to this control have performed over time.`,
            illustrationName: 'MonitoringTest',
            leftAction: canManageControlTests() && (
                <Button
                    label={t`Map tests`}
                    colorScheme="primary"
                    size="md"
                    onClick={() => {
                        const { controlId } = sharedMonitorsController;

                        openMapAutomatedTestModal(
                            FORM_ID,
                            MAP_TESTS_MODAL_ID,
                            sharedMonitoringMapTestsModel.saveMappedTests,
                            controlId,
                        );
                    }}
                />
            ),
            rightAction: canManageControlTests() && (
                <Button
                    label={t`Compare to defaults`}
                    colorScheme="neutral"
                    level="secondary"
                    size="md"
                    onClick={() => {
                        handleOpenCompareToDefaultsModal(
                            sharedCompareToDefaultsMonitoringModalModel,
                        );
                    }}
                />
            ),
        };
    }

    get tableActions(): TableAction[] {
        const { controlId, controlDetails } = sharedControlDetailsController;

        if (!controlId || !controlDetails || !canManageControlTests()) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'compare-button',
                typeProps: {
                    level: 'tertiary',
                    label: t`Compare to defaults`,
                    onClick: () => {
                        handleOpenCompareToDefaultsModal(
                            sharedCompareToDefaultsMonitoringModalModel,
                        );
                    },
                },
            },
            {
                actionType: 'button',
                id: 'map-button',
                typeProps: {
                    level: 'primary',
                    label: t`Map tests`,
                    onClick: () => {
                        openMapAutomatedTestModal(
                            FORM_ID,
                            MAP_TESTS_MODAL_ID,
                            sharedMonitoringMapTestsModel.saveMappedTests,
                            controlId,
                        );
                    },
                },
            },
        ];
    }
}

export const sharedControlsMonitoringActionsModel =
    new ControlsMonitoringActionsModel();

import { isEmpty } from 'lodash-es';
import { sharedControlsMonitoringHistoryController } from '@controllers/controls';
import {
    activeTicketsMetadataController,
    activeTrackCardController,
    sharedMonitorFindingsController,
    sharedMonitoringDetailsControlsController,
} from '@controllers/monitoring-details';
import { sharedMonitorsController } from '@controllers/monitors';
import { sharedWorkspaceMonitorsController } from '@controllers/workspace-monitors';
import type { ControlTestResponseDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';

class MonitoringDetailsPanelModel {
    constructor() {
        makeAutoObservable(this);
    }

    loadPanelInfo = (testId: number): void => {
        sharedWorkspaceMonitorsController.loadWorkspaceMonitorTestOverview(
            testId,
        );
        activeTrackCardController.loadTrackData(testId);
        sharedMonitorFindingsController.loadFindingsResponse(testId);
        activeTicketsMetadataController.loadTicketsMetadata(testId);
        sharedControlsMonitoringHistoryController.loadMonitoringHistory(testId);
        sharedMonitoringDetailsControlsController.loadMonitoringDetailsControls(
            testId,
        );
    };

    get monitoringsDetails(): ControlTestResponseDto[] {
        const { monitors } = sharedMonitorsController;

        return monitors;
    }

    get currentMonitoringIndex(): number {
        return this.monitoringsDetails.findIndex(
            (monitoring) =>
                monitoring.testId ===
                sharedWorkspaceMonitorsController.workspaceMonitorTestOverview
                    ?.testId,
        );
    }

    get description(): string {
        const { workspaceMonitorTestOverview: monitoringDetails } =
            sharedWorkspaceMonitorsController;

        if (!monitoringDetails) {
            return '';
        }

        return isEmpty(monitoringDetails.monitorInstances) &&
            monitoringDetails.monitorInstances[0]
            ? ''
            : monitoringDetails.monitorInstances[0]
                  .evidenceCollectionDescription;
    }

    onNextPageClick = () => {
        if (
            this.currentMonitoringIndex ===
            this.monitoringsDetails.length - 1
        ) {
            return;
        }
        const nextMonitoring =
            this.monitoringsDetails[this.currentMonitoringIndex + 1];

        this.loadPanelInfo(nextMonitoring.testId);
    };

    onPrevPageClick = () => {
        if (this.currentMonitoringIndex === 0) {
            return;
        }
        const prevMonitoring =
            this.monitoringsDetails[this.currentMonitoringIndex - 1];

        this.loadPanelInfo(prevMonitoring.testId);
    };
}

export const sharedMonitoringDetailsPanelModel =
    new MonitoringDetailsPanelModel();

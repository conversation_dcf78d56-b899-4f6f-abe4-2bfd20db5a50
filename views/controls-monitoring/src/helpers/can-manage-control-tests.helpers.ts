import { sharedFeatureAccessModel } from '@globals/feature-access';

export const canManageControlTests = (): boolean => {
    const {
        isMapControlsTestsEnabled,
        hasWriteControlPermission,
        hasControlTemplatePermission,
    } = sharedFeatureAccessModel;

    return (
        isMapControlsTestsEnabled &&
        (hasWriteControlPermission || hasControlTemplatePermission)
    );
};

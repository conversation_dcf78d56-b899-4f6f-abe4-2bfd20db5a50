import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form } from '@ui/forms';
import { sharedEvidenceLibraryAddEvidenceFormModel } from '../models/evidence-library-add-evidence-form.model';

interface EvidenceDetailsStepProps {
    formId: string;
    formRef: React.RefObject<HTMLFormElement>;
}

export const EvidenceDetailsStep = observer(
    ({ formId, formRef }: EvidenceDetailsStepProps): React.JSX.Element => {
        const {
            storeEvidenceDetailsStepValues,
            name,
            description,
            implementationGuidance,
        } = sharedEvidenceLibraryAddEvidenceFormModel;

        return (
            <Stack direction="column" gap="2xl" width="100%" data-id="5jPvVZ20">
                <Text size="400" type="subheadline">
                    <Trans>Add your evidence basic information</Trans>
                </Text>
                <Form
                    hasExternalSubmitButton
                    data-testid="EvidenceDetailsStep"
                    data-id="MhQi7kZe"
                    formId={formId}
                    ref={formRef}
                    schema={{
                        name: {
                            type: 'text',
                            label: t`Name`,
                            initialValue: name,
                        },
                        description: {
                            type: 'textarea',
                            label: t`Description`,
                            isOptional: true,
                            initialValue: description,
                        },
                        implementationGuidance: {
                            type: 'textarea',
                            label: t`Implementation guidance`,
                            isOptional: true,
                            initialValue: implementationGuidance,
                        },
                    }}
                    onSubmit={storeEvidenceDetailsStepValues}
                />
            </Stack>
        );
    },
);

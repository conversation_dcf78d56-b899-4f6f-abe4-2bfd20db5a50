import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import {
    getVendorsSecurityReviewsTableColumns,
    openAddSOCReviewModal,
    openUploadReviewReportModal,
    useVendorsSecurityReviewsTableActions,
    VENDORS_SECURITY_REVIEWS_DEFAULT_PAGINATION_OPTIONS,
    VENDORS_SECURITY_REVIEWS_DEFAULT_SORTING,
} from '@components/vendors-security-reviews';
import {
    sharedVendorsCurrentSecurityReviewsController,
    sharedVendorsDetailsController,
} from '@controllers/vendors';
import { EmptyState } from '@cosmos/components/empty-state';
import { Grid } from '@cosmos/components/grid';
import {
    SchemaDropdown,
    type SchemaDropdownItems,
} from '@cosmos/components/schema-dropdown';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';

export const VendorsProfileSecurityReviewsView = observer(
    (): React.JSX.Element => {
        const { paginatedSecurityReviews, loadPaginatedSecurityReviews } =
            sharedVendorsCurrentSecurityReviewsController;
        const { vendorDetails } = sharedVendorsDetailsController;
        const navigate = useNavigate();
        const { createNewSecurityReview } =
            sharedVendorsCurrentSecurityReviewsController;

        const isProspective = vendorDetails?.status === 'PROSPECTIVE';
        const securityReviewsData = paginatedSecurityReviews.data?.data ?? [];
        const hasExistingReview = !isEmpty(securityReviewsData);

        // For prospective vendors: hide table actions if there's already a review (max 1 allowed)
        const shouldShowTableActions = !isProspective || !hasExistingReview;
        const tableActionsFromHook = useVendorsSecurityReviewsTableActions();
        const tableActions = shouldShowTableActions
            ? tableActionsFromHook
            : undefined;
        const defaultPaginationOptions = isProspective
            ? undefined
            : VENDORS_SECURITY_REVIEWS_DEFAULT_PAGINATION_OPTIONS;

        return (
            <Grid
                gap="6x"
                data-testid="VendorsProfileSecurityReviewsView"
                data-id="rWHNQhZb"
            >
                {!hasExistingReview && !paginatedSecurityReviews.isLoading ? (
                    <EmptyState
                        data-id="empty-state-vendors-profile-security-reviews"
                        illustrationName="AddAi"
                        title={t`No security reviews found`}
                        leftAction={
                            <SchemaDropdown
                                label={t`Create review`}
                                level="primary"
                                endIconName="ChevronDown"
                                items={
                                    [
                                        {
                                            id: 'add-security-review',
                                            label: t`Security review`,
                                            type: 'item',
                                            description: t`Send questionnaires, add observations, and track your review decision.`,
                                            onSelect: () => {
                                                createNewSecurityReview(
                                                    navigate,
                                                    'IN_PROGRESS',
                                                    'SECURITY',
                                                );
                                            },
                                        },
                                        {
                                            id: 'add-soc-report-review',
                                            label: t`SOC report review`,
                                            type: 'item',
                                            description: t`Please finalize or delete your in-progress review to start a new SOC report review.`,
                                            onSelect: () => {
                                                openAddSOCReviewModal();
                                            },
                                        },
                                        {
                                            id: 'add-upload-review-report',
                                            label: t`Upload review report`,
                                            type: 'item',
                                            description: t`Upload a completed compliance review report.`,
                                            onSelect: () => {
                                                openUploadReviewReportModal();
                                            },
                                        },
                                    ] as SchemaDropdownItems
                                }
                            />
                        }
                    />
                ) : (
                    <AppDatatable
                        columns={getVendorsSecurityReviewsTableColumns()}
                        isSortable={!isProspective}
                        data={securityReviewsData}
                        data-id="datatable-vendors-profile-security-reviews"
                        isLoading={paginatedSecurityReviews.isLoading}
                        tableId="datatable-vendors-profile-security-reviews"
                        total={paginatedSecurityReviews.data?.total ?? 0}
                        hidePagination={isProspective}
                        tableActions={tableActions}
                        defaultPaginationOptions={defaultPaginationOptions}
                        initialSorting={
                            VENDORS_SECURITY_REVIEWS_DEFAULT_SORTING
                        }
                        tableSearchProps={{
                            hideSearch: true,
                        }}
                        onFetchData={loadPaginatedSecurityReviews}
                    />
                )}
            </Grid>
        );
    },
);

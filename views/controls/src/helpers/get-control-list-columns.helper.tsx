import type { DatatableProps } from '@cosmos/components/datatable';
import type { ControlListResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { ControlCodeCell } from '../cells/control-code-cell';
import { ControlFrameworksCell } from '../cells/control-frameworks-cell';
import { ControlMitigationCell } from '../cells/control-mitigation-cell';
import { ControlNameCell } from '../cells/control-name-cell';
import { ControlOwnerCell } from '../cells/control-owner-cell';
import { CONTROL_COLUMN_SIZES } from '../constants/control-column-sizes.constants';

export const getControlsListColumns = action(
    (): DatatableProps<ControlListResponseDto>['columns'] => {
        const { isControlMitigationCellEnabled } = sharedFeatureAccessModel;

        return [
            ...(isControlMitigationCellEnabled
                ? [
                      {
                          accessorKey: 'mitigation',
                          cell: ControlMitigationCell,
                          enableSorting: false,
                          header: '',
                          isActionColumn: true,
                          id: 'mitigation',
                          meta: {
                              shouldIgnoreRowClick: true,
                          },
                          minSize: 100,
                          maxSize: 100,
                      },
                  ]
                : []),
            {
                accessorKey: 'code',
                cell: ControlCodeCell,
                enableSorting: true,
                header: t`Control code`,
                id: 'CODE',
                minSize: CONTROL_COLUMN_SIZES.MEDIUM,
                maxSize: CONTROL_COLUMN_SIZES.MEDIUM,
            },
            {
                accessorKey: 'name',
                cell: ControlNameCell,
                enableSorting: true,
                header: t`Name`,
                id: 'NAME',
                minSize: 400,
            },
            {
                accessorKey: 'hasOwner',
                cell: ControlOwnerCell,
                enableSorting: false,
                header: t`Owners`,
                id: 'owners',
                minSize: 200,
                maxSize: 200,
                meta: {
                    shouldIgnoreRowClick: true,
                },
            },
            {
                accessorKey: 'frameworkTags',
                cell: ControlFrameworksCell,
                enableSorting: false,
                header: t`Frameworks`,
                id: 'frameworks',
                minSize: 300,
            },
        ];
    },
);

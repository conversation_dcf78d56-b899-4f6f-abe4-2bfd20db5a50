import { openAccessReviewUpdatePersonnelModal } from '@components/access-review-empty-state';
import { Button } from '@cosmos/components/button';
import type {
    FilterViewModeProps,
    TableSettingsTriggerProps,
} from '@cosmos/components/datatable';
import type { EmptyStateProps } from '@cosmos/components/empty-state';
import type { SchemaDropdownProps } from '@cosmos/components/schema-dropdown';
import type { UserAccessReviewApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export interface AccessReviewPersonnelAdapterResult {
    tableData: UserAccessReviewApplicationResponseDto[];
    totalCount: number;
    tableSettingsTriggerProps: TableSettingsTriggerProps;
    tableSearchProps: { hideSearch: boolean; placeholder: string };
    emptyStateProps: EmptyStateProps;
    filterViewModeProps: FilterViewModeProps;
}

export const accessReviewPersonnelAdapter = (data: {
    accessReviewApplicationUsersList: UserAccessReviewApplicationResponseDto[];
    accessReviewApplicationUsersTotal: number;
    isManuallyAddedApplication: boolean;
    name: string;
    isSummaryView: boolean;
    handleSummaryViewChange: (value: boolean) => void;
    hasActiveFilters?: boolean;
    hasActiveSearch?: boolean;
    hasLimitedAccess: boolean;
}): AccessReviewPersonnelAdapterResult => {
    const {
        accessReviewApplicationUsersList,
        accessReviewApplicationUsersTotal,
        isManuallyAddedApplication,
        name,
        isSummaryView,
        handleSummaryViewChange,
        hasActiveFilters = false,
        hasActiveSearch = false,
        hasLimitedAccess,
    } = data;

    /**
     * Table settings items for view mode switching.
     */
    const settingsItems = [
        {
            id: 'summary',
            label: t`Summary`,
            colorScheme: isSummaryView ? 'primary' : 'neutral',
            onSelect: () => {
                handleSummaryViewChange(true);
            },
        },
        {
            id: 'detailed',
            label: t`Detailed`,
            colorScheme: isSummaryView ? 'neutral' : 'primary',
            onSelect: () => {
                handleSummaryViewChange(false);
            },
        },
    ];

    /**
     * Table settings trigger props for the settings dropdown.
     */
    const tableSettingsTriggerProps: TableSettingsTriggerProps = {
        actionType: 'dropdown',
        id: 'table-settings-trigger',
        typeProps: {
            isIconOnly: true,
            label: t`Settings`,
            level: 'tertiary',
            colorScheme: 'neutral',
            startIconName: 'Settings',
            items: settingsItems as SchemaDropdownProps['items'],
        },
    };

    /**
     * Table search props for the search input.
     */
    const tableSearchProps = {
        hideSearch: false,
        placeholder: t`Search personnel by name or job title`,
    };

    /**
     * Filter view mode props for toggling between pinned and dropdown filters.
     */
    const filterViewModeProps: FilterViewModeProps = {
        props: {
            selectedOption: 'pinned',
            initialSelectedOption: 'pinned',
            togglePinnedLabel: t`Pin filters to page`,
            toggleUnpinnedLabel: t`Move filters to dropdown`,
        },
        viewMode: 'toggleable',
    };

    /**
     * Empty state configuration.
     */
    const manuallyAddedEmptyState: EmptyStateProps = {
        illustrationName: 'AddCircle',
        imageSize: 'md',
        title: t`You've manually added "${name}" for access review`,
        description: t`Upload a list of personnel to get it ready for an access review.`,
        rightAction: hasLimitedAccess ? null : (
            <Button
                data-testid="upload-personnel-button-empty-state-data-table-manually-added"
                label={t`Upload personnel`}
                level="primary"
                onClick={openAccessReviewUpdatePersonnelModal}
            />
        ),
    };

    const filteredEmptyState: EmptyStateProps = {
        title: t`No results found`,
        description: t`Edit your filters or search and try again.`,
    };

    const shouldShowManualEmptyState =
        isManuallyAddedApplication && !hasActiveFilters && !hasActiveSearch;

    const emptyStateProps: EmptyStateProps = shouldShowManualEmptyState
        ? manuallyAddedEmptyState
        : filteredEmptyState;

    // Transform the data to include proper typing

    return {
        tableData: accessReviewApplicationUsersList,
        totalCount: accessReviewApplicationUsersTotal,
        tableSettingsTriggerProps,
        tableSearchProps,
        emptyStateProps,
        filterViewModeProps,
    };
};

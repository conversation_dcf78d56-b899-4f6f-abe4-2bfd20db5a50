import { sharedCreateControlController } from '@controllers/controls';
import { Box } from '@cosmos/components/box';
import { Loader } from '@cosmos/components/loader';
import { Stack } from '@cosmos/components/stack';
import { Wizard, type WizardProps } from '@cosmos-lab/components/wizard';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { sharedCreateControlWorkflowModel } from '@models/controls';
import { sharedCustomFieldsValuesModel } from '@models/custom-fields';
import { useLocation, useNavigate } from '@remix-run/react';
import { useFormSubmit } from '@ui/forms';
import { CreateControlInfoWizardCreateStep } from './components/control-info/create-control-info-wizard-create-step';
import { CreateControlInfoWizardMapAutomatedTestStep } from './components/map-automated-tests/create-control-map-automated-test';
import { CreateControlInfoWizardMapEvidenceStep } from './components/map-evidence/create-control-map-evidence';
import { CreateControlInfoWizardMapPoliciesStep } from './components/map-policies/create-control-map-policies';
import { CreateControlInfoWizardMapRequirementStep } from './components/map-requirements/create-control-info-wizard-map-requirement';

export const CreateControlInfoView = observer((): React.JSX.Element => {
    const controlInfoForm = useFormSubmit();
    const navigate = useNavigate();
    const location = useLocation();

    const steps: WizardProps['steps'] = [
        {
            component: () => (
                <CreateControlInfoWizardCreateStep
                    formRef={controlInfoForm.formRef}
                    data-id="oQY_Gr38"
                />
            ),
            stepTitle: t`Control info`,
            isStepSkippable: false,
            onStepChange: async () => {
                const mainFormValid = await controlInfoForm.triggerSubmit();
                const customFieldsValid =
                    sharedCustomFieldsValuesModel.validateControlInfoCustomFields();

                return mainFormValid && customFieldsValid;
            },
        },
        {
            component: CreateControlInfoWizardMapRequirementStep,
            stepTitle: t`Map requirements`,
            isStepSkippable: false,
            backButtonLabelOverride: t`Back`,
        },
    ];

    if (sharedFeatureAccessModel.isMapControlsTestsEnabled) {
        steps.push({
            component: CreateControlInfoWizardMapAutomatedTestStep,
            stepTitle: t`Map automated tests`,
            isStepSkippable: false,
            backButtonLabelOverride: t`Back`,
        });
    }

    steps.push(
        {
            component: CreateControlInfoWizardMapEvidenceStep,
            stepTitle: t`Map evidence`,
            isStepSkippable: false,
            backButtonLabelOverride: t`Back`,
        },
        {
            component: CreateControlInfoWizardMapPoliciesStep,
            stepTitle: t`Map policies`,
            isStepSkippable: false,
            forwardButtonLabelOverride: t`Finish`,
            backButtonLabelOverride: t`Back`,
        },
    );

    const wizardProps: WizardProps = {
        steps,
        onCancel: () => {
            sharedCreateControlWorkflowModel.handleCancel(navigate, location);
        },
        onComplete: () => {
            sharedCreateControlWorkflowModel.handleComplete(
                navigate,
                location,
                sharedCreateControlController.createControl,
                sharedCreateControlController.createControlMutation,
            );

            sharedCreateControlController.handleNavigationAfterCreation(
                navigate,
                location,
            );
        },
    };

    if (sharedFeatureAccessModel.isLoading) {
        return (
            <Stack
                direction="column"
                gap="4x"
                width="100%"
                justify="center"
                align="center"
                minHeight="400px"
                data-testid="CreateControlInfoView-loading"
                data-id="loading-state"
            >
                <Loader
                    isSpinnerOnly
                    label={t`Loading...`}
                    size="lg"
                    colorScheme="primary"
                />
            </Stack>
        );
    }

    return (
        <Box pb="3xl" data-testid="CreateControlInfoView" data-id="vpo-4LfT">
            <Wizard
                steps={wizardProps.steps}
                data-testid="CreateControlInfoView"
                data-id="vpo-4LfT"
                globalIsLoading={
                    sharedCreateControlWorkflowModel.isWorkflowBusy
                }
                onCancel={wizardProps.onCancel}
                onComplete={wizardProps.onComplete}
            />
        </Box>
    );
});

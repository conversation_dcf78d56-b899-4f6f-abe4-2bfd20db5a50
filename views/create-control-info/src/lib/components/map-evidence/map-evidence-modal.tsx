import { isEmpty, isNull, size } from 'lodash-es';
import { useCallback, useMemo, useState } from 'react';
import {
    sharedControlEvidenceController,
    sharedControlsExternalEvidenceMutationController,
} from '@controllers/controls';
import { sharedEvidenceLibraryInfiniteListControllerWithDependencies } from '@controllers/evidence-library';
import { modalController } from '@controllers/modal';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { ComboboxField } from '@cosmos/components/combobox-field';
import type { ListBoxItemData } from '@cosmos/components/list-box';
import { Metadata } from '@cosmos/components/metadata';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import type { EvidenceResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { formatDate } from '@helpers/date-time';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';
import {
    MAP_EVIDENCE_MODAL_ID,
    sharedMapEvidenceModel,
} from '@models/controls';
import { useParams } from '@remix-run/react';
import { getEvidenceStatusMetadataNoIcon } from '../../helpers/get-evidence-status-metadata-no-icon.helper';

const FORM_ID = 'map-evidence-form';

interface EvidenceItem extends ListBoxItemData {
    creationDate: string;
    evidenceData: EvidenceResponseDto;
}

const MapEvidenceModal = observer(
    ({
        onConfirm,
        objectType,
    }: {
        onConfirm?: (ids: number[]) => void;
        objectType?: 'controls-evidence';
    }): React.JSX.Element => {
        const { controlId } = useParams();
        const { currentWorkspaceId } = sharedWorkspacesController;
        const { selectedEvidence } = sharedMapEvidenceModel;
        const { controlEvidence } = sharedControlEvidenceController;
        const { evidenceList, isLoading, hasNextPage } =
            sharedEvidenceLibraryInfiniteListControllerWithDependencies;
        const { isMappingEvidenceLoading } =
            sharedControlsExternalEvidenceMutationController;

        const selectedEvidenceFromController = selectedEvidence;

        const currentSelectedEvidence = useMemo(() => {
            return selectedEvidenceFromController.map(
                (evidence): EvidenceItem => {
                    const tagMetadata =
                        getEvidenceStatusMetadataNoIcon(evidence);

                    return {
                        id: `evidence-${evidence.id}`,
                        value: String(evidence.id),
                        label: evidence.name || 'Unnamed Evidence',
                        description: `Creation date: ${formatDate('field', evidence.versions[0]?.filedAt ?? evidence.createdAt)}`,
                        endSlot: (
                            <Metadata
                                type="status"
                                label={tagMetadata.label}
                                colorScheme={tagMetadata.colorScheme}
                            />
                        ),
                        creationDate: `Creation date: ${formatDate('field', evidence.versions[0]?.filedAt ?? evidence.createdAt)}`,
                        evidenceData: evidence,
                    };
                },
            );
        }, [selectedEvidenceFromController]);

        const [localSelectedEvidence, setLocalSelectedEvidence] = useState<
            ListBoxItemData[] | ListBoxItemData
        >(currentSelectedEvidence);
        const [validationError, setValidationError] = useState<string | null>(
            null,
        );

        const getEvidenceSelectionDescription = useCallback(() => {
            const count = size(localSelectedEvidence);

            return count === 1
                ? t`1 Evidence selected`
                : t`${count} Evidences selected`;
        }, [localSelectedEvidence]);

        const evidenceOptions = useMemo(() => {
            let filteredEvidenceList = evidenceList;

            // Only filter out mapped evidence when dealing with controls-evidence
            if (objectType === 'controls-evidence') {
                // Get IDs of evidence already mapped to this control
                const mappedEvidenceIds = new Set(
                    controlEvidence.map((evidence) => evidence.evidenceId),
                );

                filteredEvidenceList = evidenceList.filter(
                    (evidence) => !mappedEvidenceIds.has(evidence.id),
                );
            }

            return filteredEvidenceList.map((evidence): EvidenceItem => {
                const tagMetadata = getEvidenceStatusMetadataNoIcon(evidence);

                return {
                    id: `evidence-${evidence.id}`,
                    value: String(evidence.id),
                    label: evidence.name || 'Unnamed Evidence',
                    description: `Creation date: ${formatDate('field', evidence.versions[0].filedAt ?? evidence.createdAt)}`,
                    endSlot: (
                        <Metadata
                            type="status"
                            label={tagMetadata.label}
                            colorScheme={tagMetadata.colorScheme}
                        />
                    ),
                    creationDate: `Creation date: ${formatDate('field', evidence.versions[0].filedAt ?? evidence.createdAt)}`,
                    evidenceData: evidence,
                };
            });
        }, [evidenceList, controlEvidence, objectType]);

        const handleClose = useCallback(() => {
            modalController.closeModal(MAP_EVIDENCE_MODAL_ID);
        }, []);

        const handleSave = useCallback(() => {
            sharedEvidenceLibraryInfiniteListControllerWithDependencies.clearAllEvidence();

            if (
                !(
                    Array.isArray(localSelectedEvidence) &&
                    isEmpty(localSelectedEvidence)
                )
            ) {
                const evidenceToAdd = Array.isArray(localSelectedEvidence)
                    ? localSelectedEvidence.map(
                          (item) => (item as EvidenceItem).evidenceData,
                      )
                    : [(localSelectedEvidence as EvidenceItem).evidenceData];

                sharedEvidenceLibraryInfiniteListControllerWithDependencies.addSelectedEvidence(
                    evidenceToAdd,
                );
            }

            handleClose();
        }, [localSelectedEvidence, handleClose]);

        const handleConfirmClick = useCallback(() => {
            if (onConfirm) {
                const evidenceArray = Array.isArray(localSelectedEvidence)
                    ? localSelectedEvidence
                    : [localSelectedEvidence];

                const hasNeedsArtifactStatus = evidenceArray.some((item) => {
                    const evidenceItem = item as EvidenceItem;
                    const statusMetadata = getEvidenceStatusMetadataNoIcon(
                        evidenceItem.evidenceData,
                    );

                    return (
                        statusMetadata.status === 'NEEDS_SOURCE' ||
                        statusMetadata.status === 'EXPIRED'
                    );
                });

                if (hasNeedsArtifactStatus) {
                    openConfirmationModal({
                        title: t`This evidence will impact control readiness`,
                        body: t`You are adding evidence without an artifact to this control. This control will not be ready until an artifact is attached.`,
                        confirmText: t`Confirm`,
                        cancelText: t`Close`,
                        type: 'danger',
                        onConfirm: () => {
                            onConfirm(
                                (
                                    localSelectedEvidence as ListBoxItemData[]
                                ).map((item) => Number(item.value)),
                            );
                        },
                        onCancel: () => {
                            closeConfirmationModal();
                        },
                    });
                } else {
                    onConfirm(
                        (localSelectedEvidence as ListBoxItemData[]).map(
                            (item) => Number(item.value),
                        ),
                    );
                }
            } else {
                handleSave();
            }
        }, [onConfirm, localSelectedEvidence, handleSave]);

        const handleGetSearchEmptyState = () => {
            return (
                <Text
                    type="body"
                    size="200"
                    colorScheme="neutral"
                    align="center"
                    data-id="emptyEvidenceSearch"
                    data-testid="handleGetSearchEmptyState"
                >
                    <Trans>
                        No evidence found matching your search criteria.
                    </Trans>
                </Text>
            );
        };

        const handleOnChange = useCallback(
            (selected: ListBoxItemData[] | ListBoxItemData) => {
                setLocalSelectedEvidence(selected);
                if (
                    validationError &&
                    Array.isArray(selected) &&
                    !isEmpty(selected)
                ) {
                    setValidationError(null);
                }
            },
            [validationError],
        );

        const handleFetchOptions = useCallback(
            ({
                search,
                increasePage,
            }: {
                search?: string;
                increasePage?: boolean;
            }) => {
                sharedEvidenceLibraryInfiniteListControllerWithDependencies.fetchOptions(
                    {
                        search,
                        increasePage,
                    },
                );
            },
            [],
        );

        return (
            <>
                <Modal.Header
                    title={t`Map evidence`}
                    closeButtonAriaLabel="Close map evidence modal"
                    description={getEvidenceSelectionDescription()}
                    onClose={handleClose}
                />
                <Modal.Body>
                    <Stack direction="column" gap="lg">
                        <Box>
                            <ComboboxField
                                isMultiSelect
                                label={t`Search evidence`}
                                formId={FORM_ID}
                                getSearchEmptyState={handleGetSearchEmptyState}
                                loaderLabel={t`Loading...`}
                                name="evidence"
                                removeAllSelectedItemsLabel={t`Clear all`}
                                placeholder={t`Search by evidence name...`}
                                data-id="map-evidence-combobox"
                                defaultSelectedOptions={currentSelectedEvidence}
                                options={evidenceOptions}
                                isLoading={isLoading}
                                hasMore={hasNextPage}
                                getRemoveIndividualSelectedItemClickLabel={({
                                    itemLabel,
                                }) => t`Remove ${itemLabel}`}
                                onChange={handleOnChange}
                                onFetchOptions={handleFetchOptions}
                            />
                        </Box>
                        {objectType === 'controls-evidence' && (
                            <Stack align="start">
                                <Button
                                    width="auto"
                                    label={t`Create evidence`}
                                    level="secondary"
                                    size="sm"
                                    href={`/workspaces/${currentWorkspaceId}/compliance/controls/${controlId}/evidence/create`}
                                />
                            </Stack>
                        )}
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'secondary',
                            onClick: handleClose,
                        },
                        {
                            label: t`Confirm`,
                            level: 'primary',
                            isLoading:
                                (!isNull(onConfirm) &&
                                    isMappingEvidenceLoading) ||
                                isLoading,
                            onClick: handleConfirmClick,
                        },
                    ]}
                />
            </>
        );
    },
);

export const openMapEvidenceModal = ({
    onConfirm,
    objectType,
}: {
    onConfirm?: (ids: number[]) => void;
    objectType?: 'controls-evidence';
} = {}): void => {
    action(() => {
        sharedEvidenceLibraryInfiniteListControllerWithDependencies.load();
    })();

    modalController.openModal({
        id: MAP_EVIDENCE_MODAL_ID,
        content: () => (
            <MapEvidenceModal
                data-id="OiCVGTvi"
                objectType={objectType}
                onConfirm={onConfirm}
            />
        ),
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
    });
};

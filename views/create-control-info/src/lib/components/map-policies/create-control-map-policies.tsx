import { isEmpty, noop } from 'lodash-es';
import type { ObjectItem } from '@components/object-selector';
import { openPolicySelector } from '@components/policies';
import { sharedPoliciesLibraryInfiniteListController } from '@controllers/policies';
import { Box } from '@cosmos/components/box';
import { Button } from '@cosmos/components/button';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Truncation } from '@cosmos-lab/components/truncation';
import type { PoliciesSummarizedDataWithOwnerDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedMapPoliciesModel } from '@models/controls';
import { AppButton } from '@ui/app-button';

export const CreateControlInfoWizardMapPoliciesStep = observer(
    (): React.JSX.Element => {
        return (
            <Stack
                gap="6x"
                direction="column"
                data-testid="CreateControlInfoWizardMapPoliciesStep"
                data-id="MapPoliciesStep"
            >
                <Stack direction="column">
                    <Box>
                        <Text type="subheadline" size="400" as="p">
                            <Trans>Map policies</Trans>
                        </Text>
                    </Box>
                    <Stack justify="between" pt="4x" pb="4x">
                        <Stack align="center" gap="2x">
                            <Text type="title" size="100">
                                <Trans>Mapped policies</Trans>
                            </Text>
                            <Metadata
                                label={sharedMapPoliciesModel.selectedPolicies.length.toString()}
                                type="number"
                            />
                        </Stack>
                        <Stack gap="3x">
                            {!isEmpty(
                                sharedMapPoliciesModel.selectedPolicies,
                            ) && (
                                <Button
                                    colorScheme="danger"
                                    label={t`Remove all`}
                                    level="tertiary"
                                    size="sm"
                                    onClick={() => {
                                        sharedPoliciesLibraryInfiniteListController.clearAllPolicies();
                                    }}
                                />
                            )}
                            <Button
                                label={t`Map policies`}
                                level="secondary"
                                size="sm"
                                onClick={action(() => {
                                    const defaultSelectedItems =
                                        sharedMapPoliciesModel.selectedPoliciesAsObjectItems;

                                    openPolicySelector({
                                        config: {
                                            selectionMode: 'multi',
                                            modal: {
                                                id: 'map-policies-to-control',
                                                title: t`Map Policies`,
                                                size: 'lg',
                                                confirmButtonLabel: t`Confirm`,
                                                cancelButtonLabel: t`Close`,
                                                showSelectedCount: true,
                                                disableClickOutsideToClose: true,
                                            },
                                            search: {
                                                placeholder: t`Search by policy name...`,
                                                label: t`Search policies`,
                                                loaderLabel: t`Loading policies...`,
                                                emptyStateMessage: t`No policies found matching your search criteria.`,
                                                clearAllLabel: t`Clear all`,
                                            },
                                            filters: {
                                                // Don't exclude selected policies since we want to show them as selected
                                                excludeIds: [],
                                            },
                                            // Pass the converted policies as default selected items
                                            defaultSelectedItems,
                                        },
                                        callbacks: {
                                            onSelected: (
                                                selectedItems:
                                                    | ObjectItem<PoliciesSummarizedDataWithOwnerDto>[]
                                                    | ObjectItem<PoliciesSummarizedDataWithOwnerDto>,
                                            ) => {
                                                // Convert ObjectItem back to PolicyItem format expected by saveSelectedPolicies
                                                const items = Array.isArray(
                                                    selectedItems,
                                                )
                                                    ? selectedItems
                                                    : [selectedItems];

                                                const policyItems = items.map(
                                                    (item) => ({
                                                        ...item,
                                                        policyData:
                                                            item.objectData, // Convert objectData to policyData
                                                    }),
                                                );

                                                // Use saveSelectedPolicies to replace the entire selection
                                                sharedPoliciesLibraryInfiniteListController.saveSelectedPolicies(
                                                    policyItems,
                                                );
                                            },
                                            onCancel: noop,
                                        },
                                    });
                                })}
                            />
                        </Stack>
                    </Stack>
                    {isEmpty(sharedMapPoliciesModel.selectedPolicies) ? (
                        <Box p="4x" data-id="empty-policies-message">
                            <Text type="body" size="200" colorScheme="neutral">
                                <Trans>
                                    No policies mapped yet. Use the button above
                                    to add them.
                                </Trans>
                            </Text>
                        </Box>
                    ) : (
                        <Stack direction="column" gap="2x">
                            {sharedMapPoliciesModel.selectedPolicies.map(
                                (policy) => {
                                    const policyId = policy.id;
                                    const isPublished =
                                        sharedMapPoliciesModel.isPolicyPublished(
                                            policy,
                                        );

                                    return (
                                        <Box
                                            key={policyId}
                                            borderColor="neutralBorderFaded"
                                            borderWidth="borderWidthSm"
                                            borderRadius="borderRadiusLg"
                                            p="lg"
                                            data-id={`policy-item-${policyId}`}
                                        >
                                            <Stack
                                                direction="row"
                                                justify="between"
                                                align="center"
                                            >
                                                <Box flexGrow="1">
                                                    <Text
                                                        type="title"
                                                        size="200"
                                                        align="left"
                                                    >
                                                        <Truncation
                                                            lineClamp={2}
                                                            mode="end"
                                                        >
                                                            {policy.name ||
                                                                t`Unnamed Policy`}
                                                        </Truncation>
                                                    </Text>
                                                </Box>
                                                <Stack
                                                    direction="row"
                                                    gap="2x"
                                                    flexShrink="0"
                                                >
                                                    {isPublished && (
                                                        <Box flexShrink="0">
                                                            <AppButton
                                                                isIconOnly
                                                                target="_blank"
                                                                data-id={`policy-link-${policyId}`}
                                                                label={t`External Link`}
                                                                size="sm"
                                                                colorScheme="primary"
                                                                level="tertiary"
                                                                startIconName="LinkOut"
                                                                href={sharedPoliciesLibraryInfiniteListController.getPolicyUrl(
                                                                    policyId,
                                                                )}
                                                            />
                                                        </Box>
                                                    )}
                                                    <Box flexShrink="0">
                                                        <Button
                                                            label={t`Unmap`}
                                                            level="tertiary"
                                                            size="sm"
                                                            colorScheme="danger"
                                                            onClick={() => {
                                                                sharedPoliciesLibraryInfiniteListController.removePolicy(
                                                                    policyId,
                                                                );
                                                            }}
                                                        />
                                                    </Box>
                                                </Stack>
                                            </Stack>
                                        </Box>
                                    );
                                },
                            )}
                        </Stack>
                    )}
                </Stack>
            </Stack>
        );
    },
);

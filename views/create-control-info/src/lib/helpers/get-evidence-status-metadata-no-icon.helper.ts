import { find, isObject } from 'lodash-es';
import type { ColorScheme } from '@cosmos/components/metadata';
import type { EvidenceResponseDto } from '@globals/api-sdk/types';
import {
    type EvidenceStatusType,
    getEvidenceStatus,
    getEvidenceStatusMetadata,
} from '@helpers/evidence';

export interface TagMetadata {
    label: string;
    colorScheme: ColorScheme;
    status: EvidenceStatusType | null;
}

/**
 * Get evidence status metadata without icon for a given evidence.
 *
 * @param evidence - The evidence to get status metadata for.
 * @returns The metadata object with label and colorScheme, but no icon.
 */
export const getEvidenceStatusMetadataNoIcon = (
    evidence: EvidenceResponseDto,
): TagMetadata => {
    const currentVersion = find(evidence.versions, ['current', true]);

    const status = currentVersion
        ? getEvidenceStatus(currentVersion, evidence.renewalSchema.renewalDate)
        : null;

    const statusMetadata = getEvidenceStatusMetadata(status);

    return isObject(statusMetadata) &&
        'label' in statusMetadata &&
        'colorScheme' in statusMetadata &&
        statusMetadata.colorScheme !== undefined
        ? {
              label: statusMetadata.label,
              colorScheme: statusMetadata.colorScheme,
              status,
          }
        : {
              label: '-',
              colorScheme: 'neutral' as ColorScheme,
              status: null,
          };
};

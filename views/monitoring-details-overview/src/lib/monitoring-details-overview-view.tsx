import {
    ConnectionsUpdatedCard,
    DetailsCard,
    HistoricalResultsCard,
    LastResultsCard,
    NextStepsCard,
    TestLogicCard,
} from '@components/monitoring-details-overview';
import { sharedMonitoringTestDetailsController } from '@controllers/monitoring-test-details';
import { Grid } from '@cosmos/components/grid';
import { Stack } from '@cosmos/components/stack';
import { observer } from '@globals/mobx';

export const MonitoringDetailsOverviewView = observer((): React.JSX.Element => {
    const { testDetails, isCustomTest } = sharedMonitoringTestDetailsController;

    return (
        <Stack
            gap="8x"
            data-id="mEYSmngD"
            direction="column"
            py="8x"
            data-testid="MonitoringDetailsOverviewView"
        >
            <Grid
                gap="8x"
                columns="2"
                rows="repeat(1, 100%)"
                data-testid="MonitoringDetailsOverviewView"
                data-id="8VwQjOYg"
            >
                <DetailsCard />
                {testDetails?.draft || <NextStepsCard />}
            </Grid>

            <LastResultsCard />

            <Grid
                gap="8x"
                columns="2"
                rows="repeat(1, 100%)"
                data-testid="MonitoringDetailsOverviewView"
                data-id="8VwQjOYg"
            >
                <ConnectionsUpdatedCard />
                <HistoricalResultsCard />
            </Grid>

            {isCustomTest && <TestLogicCard />}
        </Stack>
    );
});

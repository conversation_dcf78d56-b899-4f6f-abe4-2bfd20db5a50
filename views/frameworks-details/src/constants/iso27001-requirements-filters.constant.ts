import type { FilterProps } from '@cosmos/components/datatable';
import { RequirementIndexCategory } from '@drata/enums';
import { t } from '@globals/i18n/macro';
import { createRequirementFilters } from './base-requirements-filters.constant';

export function getISO27001RequirementsFilters(): FilterProps {
    return createRequirementFilters([
        {
            filterType: 'select',
            id: 'category',
            label: t`Category`,
            options: [
                {
                    groupHeader: t`ISMS`,
                    items: [
                        {
                            id: 'iso27001-context-of-the-organization',
                            label: t`Context of the Organization`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO27001_CONTEXT_OF_THE_ORGANIZATION
                            ],
                        },
                        {
                            id: 'iso27001-leadership',
                            label: t`Leadership`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO27001_LEADERSHIP
                            ],
                        },
                        {
                            id: 'iso27001-planning',
                            label: t`Planning`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO27001_PLANNING
                            ],
                        },
                        {
                            id: 'iso27001-support',
                            label: t`Support`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO27001_SUPPORT
                            ],
                        },
                        {
                            id: 'iso27001-operation',
                            label: t`Operation`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO27001_OPERATION
                            ],
                        },
                        {
                            id: 'iso27001-performance-evaluation',
                            label: t`Performance Evaluation`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO27001_PERFORMANCE_EVALUATION
                            ],
                        },
                        {
                            id: 'iso27001-improvement',
                            label: t`Improvement`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO27001_IMPROVEMENT
                            ],
                        },
                    ],
                },
                {
                    groupHeader: t`Annex A`,
                    items: [
                        {
                            id: 'iso-information-security-policies',
                            label: t`Information Security Policies`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_INFORMATION_SECURITY_POLICIES
                            ],
                        },
                        {
                            id: 'iso-organization-of-information-security',
                            label: t`Organization of Information Security`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_ORGANIZATION_OF_INFORMATION_SECURITY
                            ],
                        },
                        {
                            id: 'iso-human-resources-security',
                            label: t`Human Resources Security`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_HUMAN_RESOURCES_SECURITY
                            ],
                        },
                        {
                            id: 'iso-asset-management',
                            label: t`Asset Management`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO_ASSET_MANAGEMENT
                            ],
                        },
                        {
                            id: 'iso-access-control',
                            label: t`Access Control`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO_ACCESS_CONTROL
                            ],
                        },
                        {
                            id: 'iso-cryptography',
                            label: t`Cryptography`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO_CRYPTOGRAPHY
                            ],
                        },
                        {
                            id: 'iso-physical-and-environmental-security',
                            label: t`Physical and Environmental Security`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_PHYSICAL_AND_ENVIRONMENTAL_SECURITY
                            ],
                        },
                        {
                            id: 'iso-operations-security',
                            label: t`Operations Security`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO_OPERATIONS_SECURITY
                            ],
                        },
                        {
                            id: 'iso-communications-security',
                            label: t`Communications Security`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_COMMUNICATIONS_SECURITY
                            ],
                        },
                        {
                            id: 'iso-system-acquisition-development-and-maintenance',
                            label: t`System Acquisition, Development and Maintenance`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_SYSTEM_ACQUISITION_DEVELOPMENT_AND_MAINTENANCE
                            ],
                        },
                        {
                            id: 'iso-supplier-relationships',
                            label: t`Supplier Relationships`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_SUPPLIER_RELATIONSHIPS
                            ],
                        },
                        {
                            id: 'iso-information-security-incident-management',
                            label: t`Information Security Incident Management`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO_INFORMATION_SECURITY_INCIDENT_MANAGEMENT
                            ],
                        },
                        {
                            id: 'iso27001-information-security-aspects-of-business-continuity-management',
                            label: t`Information Security Aspects of Business Continuity Management`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory
                                    .ISO27001_INFORMATION_SECURITY_ASPECTS_OF_BUSINESS_CONTINUITY_MANAGEMENT
                            ],
                        },
                        {
                            id: 'iso-compliance',
                            label: t`Compliance`,
                            value: RequirementIndexCategory[
                                RequirementIndexCategory.ISO_COMPLIANCE
                            ],
                        },
                    ],
                },
            ],
        },
    ]);
}

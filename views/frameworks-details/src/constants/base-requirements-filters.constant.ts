import type { FilterProps } from '@cosmos/components/datatable';
import type { Filter } from '@cosmos/components/filter-field';
import type { RadioFieldGroupProps } from '@cosmos/components/radio-field-group';
import { t } from '@globals/i18n/macro';

/**
 * Base requirements filters with abstractions for creating radio and select filters.
 *
 * Provides helper functions to easily create radio filters, select filters, and grouped select options.
 * Use createRadioFilter for simple radio button groups, createSelectFilter for dropdown selections,
 * and createGroupedSelectOptions to organize select options into categories.
 */

/**
 * Helper function to create the base readiness filter.
 *
 * @returns Base readiness filter that is shared across all framework requirement filters.
 */
export function createBaseReadinessFilter(): Filter[] {
    return [
        {
            filterType: 'radio',
            id: 'isInScope',
            label: t`Scope`,
            value: 'true', // Default to "In Scope"
            options: [
                {
                    label: t`In Scope`,
                    value: 'true',
                },
                {
                    label: t`Out of Scope`,
                    value: 'false',
                },
            ],
        },
        {
            filterType: 'radio',
            id: 'isReady',
            label: t`Readiness`,
            options: [
                {
                    label: t`Ready`,
                    value: 'true',
                },
                {
                    label: t`Not Ready`,
                    value: 'false',
                },
            ],
        },
    ];
}

/**
 * Helper function to create a category filter with the standard structure.
 *
 * @param options - Array of category options for the specific framework.
 * @returns Category filter object with standard filterType, id, and label.
 */
export function createCategoryFilter(
    options: RadioFieldGroupProps['options'],
    filterLabel = t`Category`,
): Filter {
    return {
        filterType: 'radio',
        id: 'category',
        label: filterLabel,
        options,
    };
}

/**
 * Helper function to create a topic filter with the standard structure.
 *
 * @param options - Array of topic options for the specific framework.
 * @returns Topic filter object with standard filterType, id, and label.
 */
export function createTopicFilter(
    options: RadioFieldGroupProps['options'],
    filterLabel = t`Topic`,
): Filter {
    return {
        filterType: 'radio',
        id: 'topic',
        label: filterLabel,
        options,
    };
}

/**
 * Helper function to create requirement filters with base properties and additional filters.
 *
 * @param additionalFilters - Array of additional filters to include after the base readiness filter.
 * @returns Complete FilterProps object with base properties and filters.
 */
export function createRequirementFilters(
    additionalFilters: Filter[] = [],
): FilterProps {
    return {
        clearAllButtonLabel: t`Reset`,
        triggerLabel: t`Filters`,
        filters: [...createBaseReadinessFilter(), ...additionalFilters],
    };
}

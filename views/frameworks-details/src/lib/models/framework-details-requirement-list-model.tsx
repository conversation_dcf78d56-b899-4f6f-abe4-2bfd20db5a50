import { isEmpty, isNil, isString } from 'lodash-es';
import { sharedAuditHubAuditorClientAuditController } from '@controllers/audit-hub-auditor-client-audit';
import {
    FrameworkControlsFactoryController,
    FrameworkRequirementsFactoryController,
    sharedCustomFrameworkRequirementsController,
    sharedDownloadCustomControlsToRequirementsController,
    sharedDownloadCustomRequirementsToControlsController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import { sharedRequirementsController } from '@controllers/requirements';
import type {
    FilterProps,
    Row,
    TableAction,
} from '@cosmos/components/datatable';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import { FrameworkSlug } from '@drata/enums';
import type {
    GrcControllerGetCustomRequirementMapData,
    RequirementListResponseDto,
} from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable, when } from '@globals/mobx';
import {
    getCCMRequirementsFilters,
    getCCPARequirementsFilters,
    getCIS8RequirementsFilters,
    getCMMCRequirementsFilters,
    getCOBITRequirementsFilters,
    getCyberEssentials32RequirementsFilters,
    getCyberEssentialsRequirementsFilters,
    getDORARequirementsFilters,
    getDrataEssentialsRequirementsFilters,
    getFEDRAMP20XRequirementsFilters,
    getFEDRAMPRequirementsFilters,
    getFFIECRequirementsFilters,
    getGDPRRequirementsFilters,
    getHIPAARequirementsFilters,
    getISO27001RequirementsFilters,
    getISO27701RequirementsFilters,
    getISO270012022RequirementsFilters,
    getISO270172015RequirementsFilters,
    getISO270182019RequirementsFilters,
    getISO420012023RequirementsFilters,
    getMSSSPARequirementsFilters,
    getNIS2RequirementsFilters,
    getNIST80053RequirementsFilters,
    getNIST800171R3RequirementsFilters,
    getNIST800171RequirementsFilters,
    getNISTAIRequirementsFilters,
    getNISTCSF2RequirementsFilters,
    getNISTCSFRequirementsFilters,
    getPCI4RequirementsFilters,
    getPCIRequirementsFilters,
    getSOC2RequirementsFilters,
    getSOXITGCRequirementsFilters,
} from '@views/frameworks-details';
import { frameworkDownloadConfig } from '../helpers/framework-download-config.helper';

class FrameworkDetailsRequirementListModel {
    isRequirementsDownloadInProgress = false;
    isControlsDownloadInProgress = false;

    constructor() {
        makeAutoObservable(this);
    }

    /**
     * Gets the current category filter value for dynamic filter generation.
     * This is used to determine which subcategory options to show.
     */
    get currentCategoryFilter(): string | undefined {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!frameworkDetails || frameworkDetails.tag === 'CUSTOM') {
            return undefined;
        }

        return sharedRequirementsController.category as string | undefined;
    }

    /**
     * Gets the current category filter value for dynamic filter generation.
     * This is used to determine which subcategory options to show.
     */
    get currentTopicFilter(): string | undefined {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!frameworkDetails || frameworkDetails.tag === 'CUSTOM') {
            return undefined;
        }

        return sharedRequirementsController.topic as string | undefined;
    }

    get customFrameworkRequirementsFilters(): FilterProps | undefined {
        const { frameworkDetails } = sharedFrameworkDetailsController;
        const { customCategories, isLoadingCategories } =
            sharedCustomFrameworkRequirementsController;

        if (!frameworkDetails || frameworkDetails.tag !== 'CUSTOM') {
            return undefined;
        }

        // Load custom categories if not already loaded
        if (!customCategories && !isLoadingCategories) {
            sharedCustomFrameworkRequirementsController.loadCustomCategories(
                frameworkDetails.id,
            );
        }

        const categoryOptions = [
            ...(customCategories?.data.map((category) => ({
                label: category.label,
                value: category.label,
            })) ?? []),
            {
                label: t`No Category`,
                value: 'NO_CATEGORY',
            },
        ];

        return {
            clearAllButtonLabel: t`Reset`,
            filters: [
                {
                    filterType: 'radio',
                    id: 'isInScope',
                    label: t`Scope`,
                    value: 'true', // Default to "In Scope"
                    options: [
                        {
                            label: t`In Scope`,
                            value: 'true',
                        },
                        {
                            label: t`Out of Scope`,
                            value: 'false',
                        },
                    ],
                },
                {
                    filterType: 'radio',
                    id: 'isReady',
                    label: t`Readiness`,
                    options: [
                        {
                            label: t`Ready`,
                            value: 'true',
                        },
                        {
                            label: t`Not Ready`,
                            value: 'false',
                        },
                    ],
                },
                {
                    filterType: 'radio' as const,
                    id: 'customCategory',
                    label: t`Category`,
                    options: categoryOptions,
                },
            ],
            triggerLabel: t`Filters`,
        };
    }

    get requirementListFilters(): FilterProps | undefined {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!frameworkDetails) {
            return undefined;
        }

        if (frameworkDetails.tag === 'CUSTOM') {
            return this.customFrameworkRequirementsFilters;
        }

        // eslint-disable-next-line sonarjs/max-switch-cases -- There's a lot of frameworks.
        switch (frameworkDetails.slug as FrameworkSlug) {
            case FrameworkSlug.CCM: {
                return getCCMRequirementsFilters();
            }
            case FrameworkSlug.SOC_2: {
                return getSOC2RequirementsFilters(this.currentTopicFilter);
            }
            case FrameworkSlug.ISO27001: {
                return getISO27001RequirementsFilters();
            }
            case FrameworkSlug.NIST80053: {
                return getNIST80053RequirementsFilters(
                    this.currentCategoryFilter,
                );
            }
            case FrameworkSlug.CCPA: {
                return getCCPARequirementsFilters();
            }
            case FrameworkSlug.GDPR: {
                return getGDPRRequirementsFilters();
            }
            case FrameworkSlug.HIPAA: {
                return getHIPAARequirementsFilters(this.currentCategoryFilter);
            }
            case FrameworkSlug.PCI: {
                return getPCIRequirementsFilters();
            }
            case FrameworkSlug.MSSSPA: {
                return getMSSSPARequirementsFilters();
            }
            case FrameworkSlug.NIST800171: {
                return getNIST800171RequirementsFilters(
                    this.currentCategoryFilter,
                );
            }
            case FrameworkSlug.CMMC: {
                return getCMMCRequirementsFilters(this.currentCategoryFilter);
            }
            case FrameworkSlug.FFIEC: {
                return getFFIECRequirementsFilters();
            }
            case FrameworkSlug.COBIT: {
                return getCOBITRequirementsFilters();
            }
            case FrameworkSlug.SOX_ITGC: {
                return getSOXITGCRequirementsFilters();
            }
            case FrameworkSlug.NISTAI: {
                return getNISTAIRequirementsFilters();
            }
            case FrameworkSlug.NISTCSF: {
                return getNISTCSFRequirementsFilters();
            }
            case FrameworkSlug.NISTCSF2: {
                return getNISTCSF2RequirementsFilters();
            }
            case FrameworkSlug.ISO27701: {
                return getISO27701RequirementsFilters();
            }
            case FrameworkSlug.CYBER_ESSENTIALS: {
                return getCyberEssentialsRequirementsFilters();
            }
            case FrameworkSlug.FEDRAMP: {
                return getFEDRAMPRequirementsFilters(
                    this.currentCategoryFilter,
                );
            }
            case FrameworkSlug.PCI4: {
                return getPCI4RequirementsFilters();
            }
            case FrameworkSlug.DORA: {
                return getDORARequirementsFilters(this.currentCategoryFilter);
            }
            case FrameworkSlug.NIST800171R3: {
                return getNIST800171R3RequirementsFilters(
                    this.currentCategoryFilter,
                );
            }
            case FrameworkSlug.CIS8: {
                return getCIS8RequirementsFilters();
            }
            case FrameworkSlug.ISO270012022: {
                return getISO270012022RequirementsFilters();
            }
            case FrameworkSlug.NIS2: {
                return getNIS2RequirementsFilters();
            }
            case FrameworkSlug.ISO270172015: {
                return getISO270172015RequirementsFilters();
            }
            case FrameworkSlug.ISO270182019: {
                return getISO270182019RequirementsFilters(
                    this.currentCategoryFilter,
                );
            }
            case FrameworkSlug.DRATA_ESSENTIALS: {
                return getDrataEssentialsRequirementsFilters();
            }
            case FrameworkSlug.ISO420012023: {
                return getISO420012023RequirementsFilters();
            }
            case FrameworkSlug.CYBER_ESSENTIALS_32: {
                return getCyberEssentials32RequirementsFilters();
            }
            case FrameworkSlug.FEDRAMP20X: {
                return getFEDRAMP20XRequirementsFilters();
            }
            case FrameworkSlug.NONE:
            default: {
                return undefined;
            }
        }
    }

    get isDownloadAllowed(): boolean {
        const { auditorDetails } = sharedAuditHubAuditorClientAuditController;
        const { isDownloadControlEnabled } = sharedFeatureAccessModel;

        return Boolean(
            isDownloadControlEnabled &&
                (isNil(auditorDetails?.client) ||
                    auditorDetails.allowDownloads),
        );
    }

    get isCustomFramework(): boolean {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        return frameworkDetails?.tag === 'CUSTOM';
    }

    get isAnyDownloadInProgress(): boolean {
        // Track custom framework download states
        const isCustomDownloadInProgress =
            sharedDownloadCustomControlsToRequirementsController.isLoading ||
            sharedDownloadCustomRequirementsToControlsController.isLoading;

        // Track non-custom framework download state
        return (
            isCustomDownloadInProgress ||
            this.isRequirementsDownloadInProgress ||
            this.isControlsDownloadInProgress
        );
    }

    downloadCustomControlsToRequirements = (
        frameworkSlug: FrameworkSlug,
    ): void => {
        sharedDownloadCustomControlsToRequirementsController.loadCustomRequirementsControls(
            frameworkSlug,
        );
    };

    downloadCustomRequirementsToControls = (
        frameworkSlug: FrameworkSlug,
    ): void => {
        sharedDownloadCustomRequirementsToControlsController.loadCustomRequirementsControls(
            frameworkSlug,
        );
    };

    downloadCustomRequirementsToControlsFiltered = (
        frameworkSlug: FrameworkSlug,
    ): void => {
        const { customCategory, isReady, isInScope } =
            sharedCustomFrameworkRequirementsController;

        type QueryParams = GrcControllerGetCustomRequirementMapData['query'];
        const queryParams: QueryParams = {
            customCategory:
                customCategory && isString(customCategory)
                    ? customCategory
                    : undefined,
            isReady: isReady as boolean | null | undefined,
            isInScope: isInScope as boolean | null | undefined,
        };

        sharedDownloadCustomRequirementsToControlsController.loadCustomRequirementsControls(
            frameworkSlug,
            queryParams,
        );
    };

    downloadCustomControlsToRequirementsFiltered = (
        frameworkSlug: FrameworkSlug,
    ): void => {
        const { customCategory, isReady, isInScope } =
            sharedCustomFrameworkRequirementsController;

        type QueryParams = GrcControllerGetCustomRequirementMapData['query'];
        const queryParams: QueryParams = {
            customCategory:
                customCategory && isString(customCategory)
                    ? customCategory
                    : undefined,
            isReady: isReady as boolean | null | undefined,
            isInScope: isInScope as boolean | null | undefined,
        };

        sharedDownloadCustomControlsToRequirementsController.loadCustomRequirementsControls(
            frameworkSlug,
            queryParams,
        );
    };

    downloadRequirementsToControls = (frameworkSlug: FrameworkSlug): void => {
        const frameworkRequirementsController =
            new FrameworkRequirementsFactoryController().createController(
                frameworkSlug,
            );

        // Set loading state for non-custom frameworks
        this.isRequirementsDownloadInProgress = true;

        frameworkRequirementsController.loadData();

        // Reset loading state when download completes
        when(
            () => !frameworkRequirementsController.isLoading,
            () => {
                this.isRequirementsDownloadInProgress = false;
            },
        );
    };

    downloadControlsToRequirements = (frameworkSlug: FrameworkSlug): void => {
        const frameworkControlsController =
            new FrameworkControlsFactoryController().createController(
                frameworkSlug,
            );

        // Set loading state for non-custom frameworks
        this.isControlsDownloadInProgress = true;

        frameworkControlsController.loadData();

        // Reset loading state when download completes
        when(
            () => !frameworkControlsController.isLoading,
            () => {
                this.isControlsDownloadInProgress = false;
            },
        );
    };

    downloadRequirementsToControlsFiltered = (
        frameworkSlug: FrameworkSlug,
    ): void => {
        const { category, isReady, isInScope } = sharedRequirementsController;
        const frameworkRequirementsController =
            new FrameworkRequirementsFactoryController().createController(
                frameworkSlug,
            );

        // Extract the query type from the framework requirements method
        type QueryParams = NonNullable<
            Parameters<typeof frameworkRequirementsController.loadData>[0]
        >;

        const queryParams: QueryParams = {
            category:
                category && isString(category)
                    ? ([category] as QueryParams['category'])
                    : undefined,
            isReady: isReady as boolean | null | undefined,
            isInScope: isInScope as boolean | null | undefined,
        };

        // Set loading state for non-custom frameworks
        this.isRequirementsDownloadInProgress = true;

        frameworkRequirementsController.loadData(queryParams);

        // Reset loading state when download completes
        when(
            () => !frameworkRequirementsController.isLoading,
            () => {
                this.isRequirementsDownloadInProgress = false;
            },
        );
    };

    downloadControlsToRequirementsFiltered = (
        frameworkSlug: FrameworkSlug,
    ): void => {
        const { category, isReady } = sharedRequirementsController;
        const frameworkControlsController =
            new FrameworkControlsFactoryController().createController(
                frameworkSlug,
            );

        // Set loading state for non-custom frameworks
        this.isControlsDownloadInProgress = true;

        frameworkControlsController.loadData({
            category,
            isReady,
        });

        // Reset loading state when download completes
        when(
            () => !frameworkControlsController.isLoading,
            () => {
                this.isControlsDownloadInProgress = false;
            },
        );
    };

    get tableActions(): TableAction[] {
        const { frameworkDetails } = sharedFrameworkDetailsController;

        if (!frameworkDetails) {
            return [];
        }

        const { titleDownload, titleDownloadFilterView } =
            frameworkDownloadConfig(frameworkDetails.slug as FrameworkSlug);

        const frameworkSlug = frameworkDetails.slug as FrameworkSlug;
        const isDownloadInProgress = this.isAnyDownloadInProgress;

        const dropdownItems: SchemaDropdownItems = [
            {
                id: 'download-requirements-group',
                label: titleDownload,
                type: 'group',
                items: [
                    {
                        id: 'download-requirements',
                        label: t`Requirements to controls`,
                        startIconName: 'File',
                        isReadOnly: isDownloadInProgress,
                        readOnlyTooltip: isDownloadInProgress
                            ? t`Please wait for current download to complete`
                            : undefined,
                        onSelect: action(() => {
                            if (this.isCustomFramework) {
                                this.downloadCustomRequirementsToControls(
                                    frameworkSlug,
                                );
                            } else {
                                this.downloadRequirementsToControls(
                                    frameworkSlug,
                                );
                            }
                        }),
                    },
                    {
                        id: 'download-controls',
                        label: t`Controls to requirements`,
                        startIconName: 'File',
                        isReadOnly: isDownloadInProgress,
                        readOnlyTooltip: isDownloadInProgress
                            ? t`Please wait for current download to complete`
                            : undefined,
                        onSelect: action(() => {
                            if (this.isCustomFramework) {
                                this.downloadCustomControlsToRequirements(
                                    frameworkSlug,
                                );
                            } else {
                                this.downloadControlsToRequirements(
                                    frameworkSlug,
                                );
                            }
                        }),
                    },
                ],
            },
            {
                id: 'download-filtered-view-group',
                label: titleDownloadFilterView,
                type: 'group',
                items: [
                    {
                        id: 'download-requirements-filtered',
                        label: t`Requirements to controls`,
                        startIconName: 'File',
                        isReadOnly: isDownloadInProgress,
                        readOnlyTooltip: isDownloadInProgress
                            ? t`Please wait for current download to complete`
                            : undefined,
                        onSelect: action(() => {
                            if (this.isCustomFramework) {
                                this.downloadCustomRequirementsToControlsFiltered(
                                    frameworkSlug,
                                );
                            } else {
                                this.downloadRequirementsToControlsFiltered(
                                    frameworkSlug,
                                );
                            }
                        }),
                    },
                    {
                        id: 'download-controls-filtered',
                        label: t`Controls to requirements`,
                        startIconName: 'File',
                        isReadOnly: isDownloadInProgress,
                        readOnlyTooltip: isDownloadInProgress
                            ? t`Please wait for current download to complete`
                            : undefined,
                        onSelect: action(() => {
                            if (this.isCustomFramework) {
                                this.downloadCustomControlsToRequirementsFiltered(
                                    frameworkSlug,
                                );
                            } else {
                                this.downloadControlsToRequirementsFiltered(
                                    frameworkSlug,
                                );
                            }
                        }),
                    },
                ],
            },
        ];

        return isEmpty(dropdownItems)
            ? []
            : [
                  {
                      actionType: 'dropdown',
                      id: 'download-dropdown',
                      typeProps: {
                          startIconName: 'Download',
                          level: 'tertiary',
                          colorScheme: 'neutral',
                          label: t`Download`,
                          items: dropdownItems,
                      },
                  },
              ];
    }

    get isRowSelectionEnabled(): (
        row: Row<RequirementListResponseDto>,
    ) => boolean {
        return () => true;
    }
}

export const sharedFrameworkDetailsRequirementListModel =
    new FrameworkDetailsRequirementListModel();

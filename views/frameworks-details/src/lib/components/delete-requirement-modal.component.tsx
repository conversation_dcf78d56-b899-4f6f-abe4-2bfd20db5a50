import { sharedCustomFrameworkRequirementsController } from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { action, observer, when } from '@globals/mobx';
import { DELETE_REQUIREMENT_MODAL_ID } from '../../constants/modal-ids.constants';

interface DeleteRequirementModalProps {
    'data-id'?: string;
    onConfirm: () => void;
}

const handleClose = () => {
    modalController.closeModal(DELETE_REQUIREMENT_MODAL_ID);
};

export const DeleteRequirementModal = observer(
    ({
        'data-id': dataId = 'DeleteRequirementModal',
        onConfirm,
    }: DeleteRequirementModalProps): React.JSX.Element => {
        const handleConfirm = action(() => {
            onConfirm();
            when(
                () => !sharedCustomFrameworkRequirementsController.isDeleting,
                () => {
                    handleClose();
                },
            );
        });

        return (
            <>
                <Modal.Header
                    title={t`Delete Framework Requirements?`}
                    closeButtonAriaLabel={t`Close modal`}
                    onClose={handleClose}
                />
                <Modal.Body>
                    <Text type="body" data-id={`${dataId}-warning-text`}>
                        {t`This action cannot be undone.`}
                    </Text>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: handleClose,
                        },
                        {
                            label: t`Delete requirement`,
                            level: 'primary',
                            colorScheme: 'danger',
                            isLoading:
                                sharedCustomFrameworkRequirementsController.isDeleting,
                            onClick: handleConfirm,
                        },
                    ]}
                />
            </>
        );
    },
);

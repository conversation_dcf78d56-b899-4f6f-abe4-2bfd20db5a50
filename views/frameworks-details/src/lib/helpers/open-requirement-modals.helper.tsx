import { OutOfScopeModal } from '@components/out-of-scope-modal';
import {
    sharedCustomFrameworkRequirementsController,
    sharedFrameworkDetailsController,
} from '@controllers/frameworks';
import { modalController } from '@controllers/modal';
import { sharedRequirementsController } from '@controllers/requirements';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedRequirementsModel } from '@models/requirements';
import {
    CHANGE_REQUIREMENT_CATEGORY_MODAL_ID,
    DELETE_REQUIREMENT_MODAL_ID,
    MARK_REQUIREMENT_OUT_OF_SCOPE_MODAL_ID,
} from '../../constants/modal-ids.constants';
import { ChangeRequirementCategoryModal } from '../components/change-requirement-category-modal.component';
import { DeleteRequirementModal } from '../components/delete-requirement-modal.component';

export const openMarkRequirementOutOfScopeModal = action(
    (requirementIds: number[]): void => {
        const handleCloseModal = () => {
            modalController.closeModal(MARK_REQUIREMENT_OUT_OF_SCOPE_MODAL_ID);
        };

        const handleOutOfScope = (reason: string) => {
            const { frameworkDetails } = sharedFrameworkDetailsController;
            const isCustomFramework = frameworkDetails?.tag === 'CUSTOM';

            if (isCustomFramework) {
                // Use custom framework controller for custom frameworks
                sharedCustomFrameworkRequirementsController.updateRequirementsScope(
                    {
                        requirementIds,
                        isInScope: false,
                        rationale: reason,
                        onSuccess:
                            sharedRequirementsModel.resetCustomFrameworkRowSelection,
                    },
                );
            } else {
                // Use requirements controller for non-custom frameworks
                sharedRequirementsController.updateRequirementsScope({
                    requirementIds,
                    isInScope: false,
                    rationale: reason,
                    onSuccess: sharedRequirementsModel.resetRowSelection,
                });
            }

            handleCloseModal();
        };

        modalController.openModal({
            id: MARK_REQUIREMENT_OUT_OF_SCOPE_MODAL_ID,
            content: () => (
                <OutOfScopeModal
                    data-id="disable-requirement-modal"
                    title={t`Mark requirements out of scope?`}
                    onConfirm={handleOutOfScope}
                    onCancel={handleCloseModal}
                />
            ),
            size: 'md',
            centered: true,
            disableClickOutsideToClose: false,
        });
    },
);

export const openChangeRequirementCategoryModal = action(
    (requirementIds: number[], frameworkId: number): void => {
        if (!frameworkId) {
            return;
        }

        sharedCustomFrameworkRequirementsController.loadCustomCategories(
            frameworkId,
        );

        modalController.openModal({
            id: CHANGE_REQUIREMENT_CATEGORY_MODAL_ID,
            content: () => (
                <ChangeRequirementCategoryModal
                    data-id="ChangeRequirementCategoryModal"
                    onConfirm={(selectedCategory?: string | null) => {
                        // Convert null to undefined for the API call
                        const categoryValue =
                            selectedCategory === null
                                ? undefined
                                : selectedCategory;

                        sharedCustomFrameworkRequirementsController.updateCustomRequirementCategories(
                            {
                                frameworkId,
                                requirementIds,
                                category: categoryValue,
                                onSuccess:
                                    sharedRequirementsModel.resetCustomFrameworkRowSelection,
                            },
                        );
                    }}
                />
            ),
            size: 'sm',
            centered: true,
            disableClickOutsideToClose: false,
        });
    },
);

export const openDeleteRequirementModal = action(
    (requirementIds: number[]): void => {
        modalController.openModal({
            id: DELETE_REQUIREMENT_MODAL_ID,
            content: () => (
                <DeleteRequirementModal
                    data-id="DeleteRequirementModal"
                    onConfirm={action(() => {
                        sharedCustomFrameworkRequirementsController.deleteCustomRequirements(
                            {
                                requirementIds,
                                onSuccess:
                                    sharedRequirementsModel.resetCustomFrameworkRowSelection,
                            },
                        );
                    })}
                />
            ),
            size: 'sm',
            centered: true,
            disableClickOutsideToClose: false,
        });
    },
);

import { modalController } from '@controllers/modal';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { action } from '@globals/mobx';
import { VendorsMarkVendorAsActiveSuccessView } from '../lib/vendors-mark-vendor-as-active-success-view';

export const openMarkVendorAsActiveSuccessModal = action(
    (redirectPath?: string): void => {
        const handleClose = () => {
            modalController.closeModal('mark-vendor-as-active-success-modal');
        };

        modalController.openModal({
            id: 'mark-vendor-as-active-success-modal',
            content: () => (
                <VendorsMarkVendorAsActiveSuccessView
                    data-id="FrFvztJ0"
                    redirectPath={redirectPath}
                    onClose={handleClose}
                />
            ),
            centered: true,
            size: 'md',
            onClose: () => {
                if (redirectPath) {
                    sharedProgrammaticNavigationController.navigateTo(
                        redirectPath,
                    );
                }
            },
        });
    },
);

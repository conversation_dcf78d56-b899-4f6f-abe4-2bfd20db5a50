import { modalController } from '@controllers/modal';
import { sharedVendorsTypeformQuestionnairesController } from '@controllers/vendors';
import {
    DEFAULT_PAGE_SIZE,
    type FetchDataResponseParams,
} from '@cosmos/components/datatable';
import { action } from '@globals/mobx';
import { MARK_VENDOR_AS_ACTIVE_MODAL_ID } from '../lib/constants/mark-vendor-as-active-modals.constant';
import { VendorsMarkVendorAsActiveView } from '../lib/vendors-mark-vendor-as-active-view';

export const openMarkVendorAsActiveModal = action(
    (redirectPath?: string): void => {
        sharedVendorsTypeformQuestionnairesController.loadQuestionnaires({
            pagination: { page: 1, pageSize: DEFAULT_PAGE_SIZE },
            globalFilter: { search: '', filters: {} },
        } as FetchDataResponseParams);

        modalController.openModal({
            id: MARK_VENDOR_AS_ACTIVE_MODAL_ID,
            content: () => (
                <VendorsMarkVendorAsActiveView
                    data-id="87RwD6dm"
                    redirectPath={redirectPath}
                />
            ),
            centered: true,
            disableClickOutsideToClose: true,
            size: 'lg',
        });
    },
);

export const closeMarkVendorAsActiveModal = (): void => {
    modalController.closeModal(MARK_VENDOR_AS_ACTIVE_MODAL_ID);
};

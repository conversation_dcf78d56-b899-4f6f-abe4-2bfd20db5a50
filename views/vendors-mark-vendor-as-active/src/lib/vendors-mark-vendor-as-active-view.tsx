import { styled } from 'styled-components';
import {
    VENDOR_MODAL_FORM_ID,
    VENDOR_MODAL_RISK_OPTIONS,
} from '@components/vendor-questionnaires';
import { VendorsManageRecurringReviewsForm } from '@components/vendors-security-reviews';
import { modalController } from '@controllers/modal';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import { Modal } from '@cosmos/components/modal';
import { SelectField } from '@cosmos/components/select-field';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { Divider } from '@cosmos-lab/components/divider';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { MARK_VENDOR_AS_ACTIVE_MODAL_ID } from './constants/mark-vendor-as-active-modals.constant';
import { useVendorForm } from './useVendorForm';

const StyledSelectWrapper = styled.div`
    width: 100%;
`;

interface VendorsMarkVendorAsActiveViewProps {
    redirectPath?: string;
}

export const VendorsMarkVendorAsActiveView = observer(
    ({
        redirectPath,
    }: VendorsMarkVendorAsActiveViewProps): React.JSX.Element => {
        const { data: vendor } =
            sharedVendorsDetailsController.vendorDetailsQuery;
        const {
            formData,
            reminderToggle,
            scheduleToggle,
            selectedQuestionnaire,
            setReminderToggle,
            setScheduleToggle,
            handleRecurringReviewsChange,
            handleQuestionnaireChange,
            handleContactEmailChange,
            handleRiskChange,
            handleSubmit,
            isUpdating,
        } = useVendorForm(redirectPath);
        const vendorName = vendor?.name || t`vendor`;

        return (
            <>
                <Modal.Header
                    closeButtonAriaLabel={t`Close mark vendor as active`}
                    title={t`Mark ${vendorName} as active`}
                    onClose={() => {
                        modalController.closeModal(
                            MARK_VENDOR_AS_ACTIVE_MODAL_ID,
                        );
                    }}
                />
                <Modal.Body>
                    <Stack
                        direction="column"
                        gap="xl"
                        data-testid="VendorsMarkVendorAsActiveView"
                        data-id="BqLxcPVN"
                    >
                        <Text type="title" size="300">
                            <Trans>Risk level</Trans>
                        </Text>

                        <StyledSelectWrapper>
                            <SelectField
                                label={t`Overall risk level`}
                                formId={VENDOR_MODAL_FORM_ID}
                                loaderLabel={t`Loading`}
                                name="overallRiskLevel"
                                options={VENDOR_MODAL_RISK_OPTIONS}
                                value={VENDOR_MODAL_RISK_OPTIONS.find(
                                    (opt) => opt.value === formData.risk,
                                )}
                                onChange={(value) => {
                                    handleRiskChange(value.value ?? '');
                                }}
                            />
                        </StyledSelectWrapper>

                        {/* TODO: Observations and Risk title - https://drata.atlassian.net/browse/ENG-67936 */}
                        {/* TODO: Use Form - https://drata.atlassian.net/browse/ENG-68009 */}

                        <Divider />

                        <Text type="title" size="300">
                            <Trans>Recurring reviews</Trans>
                        </Text>

                        <VendorsManageRecurringReviewsForm
                            reminderToggle={reminderToggle}
                            scheduleToggle={scheduleToggle}
                            contactsEmail={formData.contactsEmail ?? ''}
                            selectedQuestionnaire={selectedQuestionnaire ?? {}}
                            setReminderToggle={setReminderToggle}
                            setScheduleToggle={setScheduleToggle}
                            renewalScheduleType={
                                formData.renewalScheduleType as NonNullable<
                                    VendorResponseDto['renewalScheduleType']
                                >
                            }
                            onQuestionnaireChange={handleQuestionnaireChange}
                            onContactEmailChange={handleContactEmailChange}
                            onRenewalScheduleTypeChange={
                                handleRecurringReviewsChange
                            }
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Continue`,
                            level: 'primary',
                            colorScheme: 'primary',
                            onClick: () => {
                                handleSubmit();
                            },
                            isLoading: isUpdating,
                        },
                    ]}
                />
            </>
        );
    },
);

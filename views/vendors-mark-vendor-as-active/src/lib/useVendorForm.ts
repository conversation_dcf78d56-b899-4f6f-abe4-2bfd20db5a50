import { useState } from 'react';
import { modalController } from '@controllers/modal';
import {
    sharedVendorsDetailsController,
    sharedVendorsProspectiveController,
} from '@controllers/vendors';
import type {
    VendorRequestDto,
    VendorResponseDto,
} from '@globals/api-sdk/types';
import { runInAction } from '@globals/mobx';
import { openMarkVendorAsActiveSuccessModal } from '../helpers/open-mark-vendor-as-active-success-modal';
import { MARK_VENDOR_AS_ACTIVE_MODAL_ID } from './constants/mark-vendor-as-active-modals.constant';
import type { QuestionnaireOption, VendorFormData } from './types';

export const useVendorForm = (
    redirectPath?: string,
): {
    formData: VendorFormData;
    reminderToggle: boolean;
    scheduleToggle: boolean;
    selectedQuestionnaire: QuestionnaireOption | undefined;
    setReminderToggle: (value: boolean) => void;
    setScheduleToggle: (value: boolean) => void;
    handleRecurringReviewsChange: (
        renewalScheduleType: NonNullable<
            VendorResponseDto['renewalScheduleType']
        >,
    ) => void;
    handleQuestionnaireChange: (questionnaire: QuestionnaireOption) => void;
    handleContactEmailChange: (email: string) => void;
    handleRiskChange: (risk: string) => void;
    handleSubmit: () => void;
    isUpdating: boolean;
} => {
    const { data: vendor } = sharedVendorsDetailsController.vendorDetailsQuery;
    const [formData, setFormData] = useState<VendorFormData>({
        risk: 'NONE',
        renewalScheduleType: 'ONE_YEAR',
        questionnaireIds: [],
        contactsEmail: vendor?.contactsEmail ?? null,
    });

    const [reminderToggle, setReminderToggle] = useState(false);
    const [scheduleToggle, setScheduleToggle] = useState(false);
    const [selectedQuestionnaire, setSelectedQuestionnaire] =
        useState<QuestionnaireOption>();

    const handleRecurringReviewsChange = (
        renewalScheduleType: NonNullable<
            VendorResponseDto['renewalScheduleType']
        >,
    ) => {
        setFormData((prev) => ({
            ...prev,
            renewalScheduleType,
        }));
    };

    const handleQuestionnaireChange = (questionnaire: QuestionnaireOption) => {
        setSelectedQuestionnaire(questionnaire);
        setFormData((prev) => ({
            ...prev,
            questionnaireIds: [Number(questionnaire.id)],
        }));
    };

    const handleContactEmailChange = (email: string) => {
        setFormData((prev) => ({
            ...prev,
            contactsEmail: email,
        }));
    };

    const handleRiskChange = (risk: string) => {
        setFormData((prev) => ({
            ...prev,
            risk: risk as VendorRequestDto['risk'],
        }));
    };

    const handleSubmit = () => {
        if (!vendor) {
            return;
        }

        runInAction(() => {
            sharedVendorsProspectiveController.activateProspectiveVendor(
                vendor,
                {
                    risk: formData.risk,
                    renewalScheduleType: formData.renewalScheduleType,
                    reminderToggle,
                    scheduleToggle,
                    questionnaireIds: formData.questionnaireIds,
                    contactsEmail: formData.contactsEmail,
                },
                () => {
                    modalController.closeModal(MARK_VENDOR_AS_ACTIVE_MODAL_ID);
                    openMarkVendorAsActiveSuccessModal(redirectPath);
                },
            );
        });
    };

    return {
        formData,
        reminderToggle,
        scheduleToggle,
        selectedQuestionnaire,
        setReminderToggle,
        setScheduleToggle,
        handleRecurringReviewsChange,
        handleQuestionnaireChange,
        handleContactEmailChange,
        handleRiskChange,
        handleSubmit,
        isUpdating: sharedVendorsProspectiveController.isUpdating,
    };
};

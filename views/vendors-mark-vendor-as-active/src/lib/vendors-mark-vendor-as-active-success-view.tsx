import { calculateReviewDeadlineDate } from '@components/vendors-security-reviews';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import { Loader } from '@cosmos/components/loader';
import { Modal } from '@cosmos/components/modal';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { DateTime } from '@cosmos-lab/components/date-time';
import { List } from '@cosmos-lab/components/list';
import { t, Trans } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';

export interface VendorsMarkVendorAsActiveSuccessViewProps {
    onClose: () => void;
    redirectPath?: string;
}

export const VendorsMarkVendorAsActiveSuccessView = observer(
    ({
        onClose,
        redirectPath,
    }: VendorsMarkVendorAsActiveSuccessViewProps): React.JSX.Element => {
        const { data: vendor } =
            sharedVendorsDetailsController.vendorDetailsQuery;
        const navigate = useNavigate();

        if (!vendor) {
            return (
                <Stack align="center" justify="center" p="4x">
                    <Loader label={t`Loading vendor data`} />
                </Stack>
            );
        }

        const vendorName = vendor.name;
        const vendorRisk = vendor.risk;

        return (
            <>
                <Modal.Header
                    title={t`Mark ${vendorName} as active`}
                    closeButtonAriaLabel={t`Close success message`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <Text type="title" size="200">
                        {t`${vendorName} is set as an active vendor.`}
                    </Text>
                    <Stack p={'4x'}>
                        <List
                            items={[
                                <Text key="risk-level" type="body" size="200">
                                    {t`${vendorName} has a risk level of ${vendorRisk}.`}
                                </Text>,
                                <Text
                                    key="next-review-deadline"
                                    type="body"
                                    size="200"
                                >
                                    <Trans>
                                        The next review deadline for this vendor
                                        will be on{' '}
                                        <DateTime
                                            format="field"
                                            date={calculateReviewDeadlineDate(
                                                vendor.renewalScheduleType,
                                            )}
                                        />
                                        .
                                    </Trans>
                                </Text>,
                                <Text
                                    key="all-documents"
                                    type="body"
                                    size="200"
                                >
                                    {t`All documents from this review will be added to Reports and Docs.`}
                                </Text>,
                            ]}
                        />
                    </Stack>
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Close`,
                            level: 'primary',
                            onClick: () => {
                                onClose();
                                if (redirectPath) {
                                    navigate(redirectPath);
                                }
                            },
                        },
                    ]}
                />
            </>
        );
    },
);

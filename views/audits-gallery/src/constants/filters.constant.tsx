import type { DatatableProps } from '@cosmos/components/datatable';
import type { AuditListResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';

export const getAuditsFilters =
    (): DatatableProps<AuditListResponseDto>['filterProps'] => ({
        clearAllButtonLabel: t`Reset filters`,
        filters: [
            {
                filterType: 'checkbox',
                id: 'status',
                label: t`Status`,
                value: ['ACTIVE_AUDITS'],
                options: [
                    {
                        label: t`Active`,
                        value: 'ACTIVE_AUDITS',
                    },
                    {
                        label: t`Completed`,
                        value: 'COMPLETED',
                    },
                ],
            },
        ],
        triggerLabel: t`Filter`,
    });

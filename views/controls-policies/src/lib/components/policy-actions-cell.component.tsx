import {
    sharedControlPoliciesController,
    sharedControlsUnmapPolicyMutationController,
} from '@controllers/controls';
import { Icon } from '@cosmos/components/icon';
import { SchemaDropdown } from '@cosmos/components/schema-dropdown';
import type { PolicyWithControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { runInAction, when } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

interface PolicyActionsCellProps {
    row: {
        original: PolicyWithControlWorkspaceGroupResponseDto;
    };
}

export const PolicyActionsCell = ({
    row,
}: PolicyActionsCellProps): React.JSX.Element => {
    const unmapController = sharedControlsUnmapPolicyMutationController;
    const controlPoliciesController = sharedControlPoliciesController;

    const handleUnmapPolicy = () => {
        runInAction(() => {
            const policyId = row.original.policy.id;
            const { controlId } = controlPoliciesController;

            openConfirmationModal({
                title: t`Unmap policy?`,
                body: t`This policy will no longer be mapped to this control.`,
                confirmText: t`Unmap`,
                cancelText: t`Cancel`,
                type: 'danger',
                onConfirm: () => {
                    runInAction(() => {
                        unmapController.unmapPolicyFromControl(
                            policyId,
                            controlId,
                        );

                        when(
                            () => !unmapController.isUnmapping,
                            () => {
                                closeConfirmationModal();
                            },
                        );
                    });
                },
                onCancel: closeConfirmationModal,
                isLoading: () => unmapController.isUnmapping,
            });
        });
    };

    return (
        <SchemaDropdown
            isIconOnly
            label="Actions"
            level="tertiary"
            startIconName="Action"
            data-testid="PolicyActionsCell"
            data-id="s0nK5Fbh"
            colorScheme="neutral"
            items={[
                {
                    id: 'unmap-policy-option',
                    label: t`Unmap policy`,
                    type: 'item',
                    value: 'unmap',
                    startSlot: <Icon name="Unlink" size="200" />,
                    onClick: handleUnmapPolicy,
                },
            ]}
        />
    );
};

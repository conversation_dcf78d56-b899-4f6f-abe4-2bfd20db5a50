import { isEmpty, isNil } from 'lodash-es';
import { useMemo } from 'react';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import type { ControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';

export const LinkedWorkspacesCell = ({
    row,
}: {
    row: {
        original: {
            controlWorkspaceGroup: ControlWorkspaceGroupResponseDto;
        };
    };
}): React.ReactNode => {
    const { linkedWorkspaces } = row.original.controlWorkspaceGroup;

    const workspaceGroups = useMemo(() => {
        if (isEmpty(linkedWorkspaces) || isNil(linkedWorkspaces)) {
            return [];
        }

        return linkedWorkspaces.flatMap((ws) => {
            return {
                name: ws.workspace.name,
                id: ws.workspace.id,
            };
        });
    }, [linkedWorkspaces]);

    const uniqueWorkspaces = useMemo(
        () =>
            workspaceGroups.filter(
                (ws, index, array) =>
                    array.findIndex((ws2) => ws2.name === ws.name) === index,
            ),
        [workspaceGroups],
    );

    const displayedWorkspaces = useMemo(
        () => uniqueWorkspaces.slice(0, 2),
        [uniqueWorkspaces],
    );

    const remainingCount = useMemo(
        () => uniqueWorkspaces.length - displayedWorkspaces.length,
        [uniqueWorkspaces.length, displayedWorkspaces.length],
    );

    if (isEmpty(linkedWorkspaces) || isNil(linkedWorkspaces)) {
        return <EmptyValue label="-" />;
    }

    return (
        <Stack
            direction="row"
            gap="2x"
            wrap="wrap"
            data-id="dyCTpnAo"
            data-testid="LinkedWorkspacesCell"
        >
            {displayedWorkspaces.map((ws) => (
                <Metadata
                    key={ws.id}
                    colorScheme="neutral"
                    label={ws.name}
                    type="tag"
                    data-id="x09zNXRM"
                />
            ))}
            {remainingCount > 0 && (
                <Metadata
                    colorScheme="neutral"
                    label={`+${remainingCount}`}
                    type="tag"
                />
            )}
        </Stack>
    );
};

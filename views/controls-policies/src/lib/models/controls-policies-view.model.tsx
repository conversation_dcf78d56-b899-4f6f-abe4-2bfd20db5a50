import { isNil } from 'lodash-es';
import {
    handleOpenCompareToDefaultsModal,
    openMapPoliciesModal,
} from '@components/controls';
import {
    sharedControlDetailsController,
    sharedControlPoliciesController,
    sharedControlsDownloadController,
} from '@controllers/controls';
import { Button } from '@cosmos/components/button';
import type { DatatableProps, TableAction } from '@cosmos/components/datatable';
import type { PolicyWithControlWorkspaceGroupResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, runInAction } from '@globals/mobx';
import { COLUMN_SIZES } from '@helpers/table';
import { sharedCompareToDefaultsPoliciesModalModel } from '@models/controls';
import {
    LinkedWorkspacesCell,
    PolicyActionsCell,
    PolicyOwnerCell,
    PolicyRenewalDateCell,
    PolicyStatusCell,
    PolicyVersionCell,
} from '../components';

class ControlsPoliciesViewModel {
    constructor() {
        makeAutoObservable(this);
    }

    get displayCompareToDefaults(): boolean {
        const { hasControlTemplatePermission } = sharedFeatureAccessModel;
        const { controlDetails } = sharedControlDetailsController;
        const isTemplatedControl = !isNil(
            controlDetails?.fk_control_template_id,
        );

        return hasControlTemplatePermission && isTemplatedControl;
    }

    get shouldShowLinkedWorkspacesColumn(): boolean {
        const { isMultipleWorkspacesEnabled } = sharedFeatureAccessModel;

        return isMultipleWorkspacesEnabled;
    }

    get emptyStateProps(): DatatableProps<PolicyWithControlWorkspaceGroupResponseDto>['emptyStateProps'] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        return {
            illustrationName: 'AddPage',
            title: t`Map policies to this control`,
            description: t`Controls help enforce your policies.`,
            leftAction: this.displayCompareToDefaults ? (
                <Button
                    label={t`Compare to defaults`}
                    level="tertiary"
                    onClick={() => {
                        const { controlId } = sharedControlPoliciesController;

                        if (controlId) {
                            sharedCompareToDefaultsPoliciesModalModel.loadCompareToDefaultsData();
                        }

                        handleOpenCompareToDefaultsModal(
                            sharedCompareToDefaultsPoliciesModalModel,
                        );
                    }}
                />
            ) : undefined,
            rightAction: hasWriteControlPermission ? (
                <Button
                    label={t`Map policies`}
                    level="primary"
                    onClick={this.openMapPoliciesModal}
                />
            ) : undefined,
        };
    }

    get tableSearchProps(): DatatableProps<PolicyWithControlWorkspaceGroupResponseDto>['tableSearchProps'] {
        return {
            hideSearch: true,
        };
    }

    get columns(): DatatableProps<PolicyWithControlWorkspaceGroupResponseDto>['columns'] {
        const { hasWriteControlPermission } = sharedFeatureAccessModel;

        return [
            ...(hasWriteControlPermission
                ? [
                      {
                          id: 'actions',
                          isActionColumn: true,
                          enableSorting: false,
                          meta: { shouldIgnoreRowClick: true },
                          cell: PolicyActionsCell,
                          maxSize: COLUMN_SIZES.SMALL,
                          minSize: COLUMN_SIZES.SMALL,
                      },
                  ]
                : []),
            {
                id: 'POLICY_NAME',
                accessorKey: 'policy.name',
                header: t`Name`,
                enableSorting: true,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'POLICY_VERSION',
                accessorKey: 'policy',
                header: t`Version`,
                enableSorting: true,
                cell: PolicyVersionCell,
                minSize: COLUMN_SIZES.MEDIUM,
                maxSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'POLICY_VERSION_STATUS',
                accessorKey: 'policy',
                header: t`Status`,
                enableSorting: true,
                cell: PolicyStatusCell,
                minSize: COLUMN_SIZES.MEDIUM,
                maxSize: COLUMN_SIZES.MEDIUM,
            },
            {
                id: 'RENEWAL_DATE',
                accessorKey: 'policy',
                header: t`Renewal date`,
                enableSorting: true,
                cell: PolicyRenewalDateCell,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
            {
                id: 'POLICY_OWNER',
                accessorKey: 'policy',
                header: t`Owner`,
                enableSorting: true,
                cell: PolicyOwnerCell,
                minSize: COLUMN_SIZES.LARGE,
                maxSize: COLUMN_SIZES.LARGE,
            },
            ...(this.shouldShowLinkedWorkspacesColumn
                ? [
                      {
                          id: 'linkedWorkspaces',
                          accessorKey: 'controlWorkspaceGroup',
                          header: t`Linked workspaces`,
                          enableSorting: false,
                          cell: LinkedWorkspacesCell,
                          minSize: COLUMN_SIZES.LARGE,
                          maxSize: COLUMN_SIZES.LARGE,
                      },
                  ]
                : []),
        ];
    }

    handleDownloadPolicies = (): void => {
        const { controlId } = sharedControlPoliciesController;
        const { downloadControlPolicies } = sharedControlsDownloadController;

        if (controlId) {
            runInAction(() => {
                downloadControlPolicies(controlId);
            });
        }
    };

    openMapPoliciesModal = (): void => {
        const { controlId } = sharedControlDetailsController;

        if (!controlId) {
            return;
        }

        openMapPoliciesModal({ controlId });
    };

    get tableActions(): TableAction[] {
        const { total } = sharedControlPoliciesController;
        const { hasWriteControlPermission } = sharedFeatureAccessModel;
        const hasPolicies = total > 0;

        if (!hasPolicies) {
            return [];
        }

        return [
            {
                actionType: 'button',
                id: 'download-button',
                typeProps: {
                    startIconName: 'Download',
                    level: 'tertiary',
                    colorScheme: 'neutral',
                    label: t`Download`,
                    isLoading:
                        sharedControlsDownloadController.isDownloadControlPoliciesLoading,
                    onClick: this.handleDownloadPolicies,
                },
            } satisfies TableAction,
            ...(this.displayCompareToDefaults
                ? [
                      {
                          actionType: 'button',
                          id: 'compare-to-defaults-button',
                          typeProps: {
                              level: 'tertiary',
                              label: t`Compare to defaults`,
                              onClick: () => {
                                  handleOpenCompareToDefaultsModal(
                                      sharedCompareToDefaultsPoliciesModalModel,
                                  );
                              },
                          },
                      } satisfies TableAction,
                  ]
                : []),
            ...(hasWriteControlPermission
                ? [
                      {
                          actionType: 'button',
                          id: 'map-policies-button',
                          typeProps: {
                              level: 'secondary',
                              label: t`Map policies`,
                              onClick: this.openMapPoliciesModal,
                          },
                      } satisfies TableAction,
                  ]
                : []),
        ];
    }
}

export const sharedControlsPoliciesViewModel = new ControlsPoliciesViewModel();

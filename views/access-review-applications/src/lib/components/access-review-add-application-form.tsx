import { z } from 'zod';
import { sharedUsersInfiniteController } from '@controllers/users';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { Form, type FormValues } from '@ui/forms';

interface AccessReviewAddApplicationFormProps {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit: (values: FormValues) => void;
}

export const AccessReviewAddApplicationForm = observer(
    ({
        formRef,
        onSubmit,
    }: AccessReviewAddApplicationFormProps): React.JSX.Element => {
        const {
            hasNextPage: hasMoreUsers,
            isFetching: isFetchingUsers,
            isLoading: isLoadingUsers,
            onFetchUsers,
            options,
        } = sharedUsersInfiniteController;

        return (
            <Form
                hasExternalSubmitButton
                formId="access-review-add-application-form"
                ref={formRef}
                data-testid="AccessReviewAddApplicationForm"
                data-id="IK6BzEgE"
                schema={{
                    name: {
                        type: 'text',
                        initialValue: '',
                        label: t`Application name`,
                        validator: z
                            .string()
                            .nonempty({
                                message: t`Application name is required`,
                            })
                            .max(191, {
                                message: t`Application name must contain at most 191 characters`,
                            }),
                    },
                    websiteUrl: {
                        type: 'text',
                        initialValue: '',
                        label: t`Website URL`,
                        validator: z
                            .string()
                            .nonempty({
                                message: t`Website URL is required`,
                            })
                            .max(191, {
                                message: t`Website URL must contain at most 191 characters`,
                            })
                            .url({
                                message: t`Website URL must be a valid URL`,
                            }),
                    },
                    reviewersId: {
                        type: 'combobox',
                        label: t`Reviewers`,
                        isMultiSelect: true,
                        initialValue: [],
                        isLoading: isFetchingUsers && isLoadingUsers,
                        removeAllSelectedItemsLabel: t`Clear all`,
                        getSearchEmptyState: () => t`No employees found`,
                        loaderLabel: t`Loading`,
                        placeholder: t`Search by name or email`,
                        options,
                        hasMore: hasMoreUsers,
                        onFetchOptions: onFetchUsers,
                        validator: z
                            .array(
                                z.object({
                                    id: z.string(),
                                    label: z.string(),
                                    value: z.string(),
                                }),
                            )
                            .min(1, t`At least one reviewer is required`),
                        isOptional: false,
                    },
                }}
                onSubmit={onSubmit}
            />
        );
    },
);

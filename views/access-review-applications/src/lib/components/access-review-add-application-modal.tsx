import { useCallback, useState } from 'react';
import { activeAccessReviewsApplicationsController } from '@controllers/access-reviews-applications';
import { Modal } from '@cosmos/components/modal';
import type { AccessReviewApplicationResponseDto } from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { observer, runInAction } from '@globals/mobx';
import { useNavigate } from '@remix-run/react';
import { type FormValues, useFormSubmit } from '@ui/forms';
import {
    extractName,
    extractReviewers,
    extractUrl,
} from '../helpers/form-value-extractors.helper';
import { AccessReviewAddApplicationForm } from './access-review-add-application-form';

interface AccessReviewAddApplicationModalProps {
    onClose: () => void;
    onApplicationCreated?: (
        application: AccessReviewApplicationResponseDto,
    ) => void;
}

export const AccessReviewAddApplicationModal = observer(
    ({
        onClose,
        onApplicationCreated,
    }: AccessReviewAddApplicationModalProps): React.JSX.Element => {
        const navigate = useNavigate();
        const { formRef, triggerSubmit } = useFormSubmit();
        const [isSubmitting, setIsSubmitting] = useState(false);

        const handleSubmitAsync = useCallback(
            (values: {
                name: string;
                websiteUrl: string;
                reviewersId: string[];
            }) => {
                setIsSubmitting(true);
                const name = extractName(values.name);
                const websiteUrl = extractUrl(values.websiteUrl);
                const reviewersId = extractReviewers(values.reviewersId);

                activeAccessReviewsApplicationsController.createApplication(
                    {
                        name,
                        websiteUrl,
                        reviewersId,
                    },
                    navigate,
                    onClose,
                    onApplicationCreated
                        ? (application) => {
                              onApplicationCreated(application);
                          }
                        : undefined,
                    () => {
                        setIsSubmitting(false);
                    },
                );
            },
            [navigate, onClose, onApplicationCreated],
        );

        const handleSubmit = useCallback(
            (values: {
                name: string;
                websiteUrl: string;
                reviewersId: string[];
            }) => {
                runInAction(() => {
                    handleSubmitAsync(values);
                });
            },
            [handleSubmitAsync],
        );

        return (
            <>
                <Modal.Header
                    title={t`Add application`}
                    closeButtonAriaLabel={t`Close add application modal`}
                    onClose={onClose}
                />
                <Modal.Body>
                    <AccessReviewAddApplicationForm
                        formRef={formRef}
                        onSubmit={(values: FormValues) => {
                            handleSubmit(
                                values as {
                                    name: string;
                                    websiteUrl: string;
                                    reviewersId: string[];
                                },
                            );
                        }}
                    />
                </Modal.Body>
                <Modal.Footer
                    rightActionStack={[
                        {
                            label: t`Cancel`,
                            level: 'secondary',
                            onClick: onClose,
                        },
                        {
                            label: t`Complete`,
                            isLoading: isSubmitting,
                            a11yLoadingLabel: t`Adding application...`,
                            onClick: () => {
                                triggerSubmit();
                                setIsSubmitting(false);
                            },
                        },
                    ]}
                />
            </>
        );
    },
);

import { Text } from '@cosmos/components/text';
import type {
    AccessApplicationSummaryResponseDto,
    AccessReviewApplicationResponseDto,
    AccessReviewPeriodApplicationResponseDto,
    UserResponseDto,
} from '@globals/api-sdk/types';
import { observer, toJS } from '@globals/mobx';
import { getApplicationTypeLabel } from '../../../helpers';

type ApplicationWithReviewers = AccessApplicationSummaryResponseDto & {
    reviewers: UserResponseDto[];
    needsReviewer: boolean;
};

export const ApplicationTypeCell = observer(
    ({
        row: { original },
    }: {
        row: {
            original:
                | AccessReviewApplicationResponseDto
                | ApplicationWithReviewers
                | AccessReviewPeriodApplicationResponseDto;
        };
    }): React.JSX.Element => {
        const { source } = original;
        const sourceToDisplay = toJS(source);

        return (
            <Text data-testid="ApplicationTypeCell" data-id="CT8uZm6q">
                {getApplicationTypeLabel(sourceToDisplay)}
            </Text>
        );
    },
);

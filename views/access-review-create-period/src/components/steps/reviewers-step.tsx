import { isEmpty } from 'lodash-es';
import { AppDatatable } from '@components/app-datatable';
import {
    sharedCreateReviewPeriodApplicationsDatatableController,
    sharedCreateReviewPeriodController,
} from '@controllers/access-reviews';
import {
    DEFAULT_PAGE,
    DEFAULT_PAGE_INDEX,
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { Feedback } from '@cosmos/components/feedback';
import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { dimension170x } from '@cosmos/constants/tokens';
import { t, Trans } from '@globals/i18n/macro';
import { observer, toJS } from '@globals/mobx';

export const ReviewersStep = observer((): React.JSX.Element => {
    const { selectedApplications } = sharedCreateReviewPeriodController;
    const hasApplicationsNeedingReviewers = !isEmpty(
        selectedApplications.filter((app) => isEmpty(app.reviewers)),
    );

    const applications = toJS(selectedApplications);

    return (
        <Stack
            gap="xl"
            direction="column"
            data-testid="ReviewersStep"
            data-id="access-review-review-step"
            maxWidth={dimension170x}
        >
            <Stack gap="lg" direction="column">
                <Text type="subheadline" size="400">
                    <Trans>Review added applications</Trans>
                </Text>
                <Text type="body">
                    <Trans>
                        All applications in a review period must have at least
                        one reviewer. You can update the reviewers for an
                        application by editing its details. Reviewers receive an
                        email when you add or remove them from a review.
                    </Trans>
                </Text>
            </Stack>

            <Stack gap="lg" direction="column">
                <Stack direction="row" gap="sm" align="center">
                    <Text type="title" size="200">
                        <Trans>Added applications:</Trans>
                    </Text>
                    <Metadata
                        label={applications.length.toString()}
                        type="number"
                        colorScheme="neutral"
                        data-id="added-applications-count"
                    />
                </Stack>

                <AppDatatable
                    isSortable={false}
                    data-id="reviewers-step-table"
                    controller={
                        sharedCreateReviewPeriodApplicationsDatatableController
                    }
                    defaultPaginationOptions={{
                        page: DEFAULT_PAGE,
                        pageIndex: DEFAULT_PAGE_INDEX,
                        pageSize: DEFAULT_PAGE_SIZE,
                        pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
                    }}
                    emptyStateProps={{
                        title: t`No applications added`,
                        description: t`Go back to add applications to your review period.`,
                    }}
                />

                {hasApplicationsNeedingReviewers && (
                    <Feedback
                        title={t`All selected applications must have a reviewer assigned`}
                        description={t`Please assign a reviewer to all applications before saving this setup.`}
                        severity="critical"
                        data-id="missing-reviewers-feedback"
                    />
                )}

                {!hasApplicationsNeedingReviewers && !isEmpty(applications) && (
                    <Feedback
                        title={t`All applications have reviewers assigned`}
                        description={t`You can proceed to create the access review period.`}
                        severity="success"
                        data-id="all-reviewers-assigned-feedback"
                    />
                )}
            </Stack>
        </Stack>
    );
});

import { sharedRiskSettingsController } from '@controllers/risk';
import { sharedRiskDetailsController } from '@controllers/risk-details';
import { Skeleton } from '@cosmos/components/skeleton';
import { logger } from '@globals/logger';
import { observer } from '@globals/mobx';
import {
    type RiskSection,
    SKELETON_BAR_COUNTS,
} from '../helpers/risk-configuration.helper';
import {
    AssessmentReadOnlyContent,
    DetailsReadOnlyContent,
    OwnersReadOnlyContent,
    TreatmentReadOnlyContent,
} from './read-only-content';

interface RiskSectionReadOnlyCardProps {
    section: RiskSection;
}

export const RiskSectionReadOnlyCard = observer(
    ({ section }: RiskSectionReadOnlyCardProps): React.JSX.Element => {
        const { riskDetails, isLoading } = sharedRiskDetailsController;
        const { riskSettings } = sharedRiskSettingsController;

        if (isLoading) {
            const barCount = SKELETON_BAR_COUNTS[section];

            return <Skeleton barCount={barCount} />;
        }

        switch (section) {
            case 'assessment': {
                return (
                    <AssessmentReadOnlyContent
                        riskDetails={riskDetails}
                        riskSettings={riskSettings}
                    />
                );
            }
            case 'details': {
                return <DetailsReadOnlyContent riskDetails={riskDetails} />;
            }
            case 'treatment': {
                return <TreatmentReadOnlyContent riskDetails={riskDetails} />;
            }
            case 'owners': {
                return <OwnersReadOnlyContent riskDetails={riskDetails} />;
            }
            default: {
                // Log error for debugging in production
                logger.error({
                    message:
                        'Unknown risk section encountered in RiskSectionReadOnlyCard',
                    additionalInfo: {
                        section,
                        component: 'RiskSectionReadOnlyCard',
                        availableSections: [
                            'assessment',
                            'details',
                            'treatment',
                            'owners',
                        ],
                    },
                });

                return <div>Unknown section</div>;
            }
        }
    },
);

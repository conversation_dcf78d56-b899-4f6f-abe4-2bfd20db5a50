import type { CosmosFileObject } from '@cosmos/components/file-upload';
import type {
    RiskCategoryResponseDto,
    RiskUserResponseDto,
} from '@globals/api-sdk/types';

export type RiskTreatmentPlanValue =
    | 'UNTREATED'
    | 'ACCEPT'
    | 'TRANSFER'
    | 'AVOID'
    | 'MITIGATE';

export interface SelectOption {
    id: string;
    label: string;
    value: string;
}

export interface UserSelectOption extends SelectOption {
    avatar?: {
        imgAlt: string;
        imgSrc?: string;
    };
}

export interface AssessmentFormData {
    impact: SelectOption | null;
    likelihood: SelectOption | null;
}

export interface AssessmentFormValues {
    impact?: SelectOption;
    likelihood?: SelectOption;
}

export interface DetailsFormData {
    title: string;
    description: string;
    type: SelectOption;
    identifiedAt: string;
    categories: SelectOption[];
    documents: CosmosFileObject[];
}

export interface DetailsFormValues {
    title?: string;
    description?: string;
    type?: SelectOption;
    identifiedAt?: string;
    categories?: SelectOption[];
    documents?: CosmosFileObject[];
}

export interface TreatmentFormData {
    treatmentPlan: SelectOption;
    treatmentDetails: string;
    residualImpact: SelectOption | null;
    residualLikelihood: SelectOption | null;
    anticipatedCompletionDate: string;
    completionDate: string;
}

export interface TreatmentFormValues {
    treatmentPlan?: SelectOption;
    treatmentDetails?: string;
    residualImpact?: SelectOption;
    residualLikelihood?: SelectOption;
    treatmentDueDate?: string;
    treatmentCompletionDate?: string;
    reviewers?: UserSelectOption[];
}

export interface OwnersFormData {
    owners: UserSelectOption[];
}

export interface OwnersFormValues {
    owners?: UserSelectOption[];
}

export interface ReviewersFormData {
    reviewers: UserSelectOption[];
}

export interface ReviewersFormValues {
    reviewers?: UserSelectOption[];
}

export interface RiskUpdateRequestBody {
    impact?: number | null;
    likelihood?: number | null;
    score?: number | null;
    title?: string;
    description?: string;
    type?: string;
    identifiedAt?: string;
    categories?: { id: number }[];
    treatmentPlan?: RiskTreatmentPlanValue;
    treatmentDetails?: string;
    residualImpact?: number | null;
    residualLikelihood?: number | null;
    residualScore?: number | null;
    anticipatedCompletionDate?: string;
    completionDate?: string;
    owners?: { id: number }[];
    reviewers?: { id: number }[];
}

export type AssessmentSubmitHandler = (
    values: AssessmentFormData,
) => void | Promise<void>;
export type DetailsSubmitHandler = (
    values: DetailsFormData,
) => void | Promise<void>;
export type TreatmentSubmitHandler = (
    values: TreatmentFormData,
) => void | Promise<void>;
export type OwnersSubmitHandler = (
    values: OwnersFormData,
) => void | Promise<void>;
export type ReviewersSubmitHandler = (
    values: ReviewersFormData,
) => void | Promise<void>;

export interface FormComponentProps<T> {
    formRef: React.ForwardedRef<HTMLFormElement>;
    onSubmit?: (values: T) => void | Promise<void>;
}

export type AssessmentEditFormProps = FormComponentProps<AssessmentFormData>;
export type DetailsEditFormProps = FormComponentProps<DetailsFormData>;
export type TreatmentEditFormProps = FormComponentProps<TreatmentFormData>;
export type OwnersEditFormProps = FormComponentProps<OwnersFormData>;
export type ReviewersEditFormProps = FormComponentProps<ReviewersFormData>;

export interface CategoryTransformData {
    categories: RiskCategoryResponseDto[];
    selectedCategoryIds: number[];
}

export interface UserTransformData {
    users: RiskUserResponseDto[];
    selectedUserIds: number[];
}

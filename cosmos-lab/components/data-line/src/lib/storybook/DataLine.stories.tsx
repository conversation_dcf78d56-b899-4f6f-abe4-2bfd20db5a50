import {
    dataCategorizeBlue2,
    dataCategorizeRed2,
} from '@cosmos/constants/tokens';
import type { Meta } from '@storybook/react-vite';
import { DataLine } from '../data-line';
import type { DataLineProps } from '../types/data-line-props.type';

interface ChartData {
    name: string;
    firstValue: number;
    secondValue: number;
}

const meta: Meta<DataLineProps<ChartData>> = {
    title: 'Information & Data/DataLine',
    component: DataLine as React.FC<DataLineProps<ChartData>>,
    tags: ['Lab'],
    args: {
        'data-id': 'cosmos-lab-data-line',
        data: [
            {
                name: 'Jan',
                firstValue: 3,
                secondValue: 0,
            },
            {
                name: 'Feb',
                firstValue: 11,
                secondValue: 0,
            },
            {
                name: 'Mar',
                firstValue: 3,
                secondValue: 6,
            },
            {
                name: 'Apr',
                firstValue: 0,
                secondValue: 7,
            },
        ],
        categoryKey: 'name',
        width: '100%',
        height: 200,
        lines: [
            {
                dataKey: 'firstValue',
                stroke: dataCategorizeRed2,
                strokeWidth: 2,
                type: 'linear',
                dot: false,
                activeDot: true,
            },
            {
                dataKey: 'secondValue',
                stroke: dataCategorizeBlue2,
                strokeWidth: 2,
                type: 'linear',
                dot: false,
                activeDot: true,
            },
        ],
    },
};

export default meta;

// Playground should be first and have all controls enabled so they are accessible on Docs page
export { Playground } from './stories';

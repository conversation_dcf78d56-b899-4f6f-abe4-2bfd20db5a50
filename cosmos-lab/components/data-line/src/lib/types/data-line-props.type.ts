import type { LineConfig } from './line-config.type';
import type { TooltipContentProps } from './tooltip-content-props.type';

export interface DataLineProps<T extends object> {
    /**
     * Optional unique identifier for testing purposes.
     * Used as a data attribute for test selection.
     */
    'data-id': string;

    /**
     * The width of the chart.
     * Can be specified as a string (e.g., '100%') or a number of pixels.
     * Defaults to '100%' if not provided.
     */
    width?: string | number;

    /**
     * The height of the chart.
     * Can be specified as a string (e.g., '200px') or a number of pixels.
     * Defaults to 200 if not provided.
     */
    height?: string | number;

    /**
     * The array of data objects to be visualized in the chart.
     * Each object represents a data point with properties for category and values.
     */
    data: T[];

    /**
     * The key in the data object that contains the category labels for the x-axis.
     * Must be a string property name from type T.
     */
    categoryKey: Extract<keyof T, string>;

    /**
     * Configuration for each line series to be displayed in the chart.
     * Each LineConfig defines how a specific data property should be visualized.
     */
    lines: LineConfig<T>[];

    /**
     * Optional custom tooltip component to display when hovering over lines.
     * This should be a React component that receives TooltipContentProps.
     * If not provided, the default TooltipContent component will be used.
     */
    tooltipComponent?: React.FC<TooltipContentProps>;
}

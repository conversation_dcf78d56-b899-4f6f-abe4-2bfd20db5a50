import type { DataColor } from './data-color.type';

export interface LineConfig<T extends object> {
    /**
     * The key in the data object that contains the values to be displayed in the line.
     * Must be a string property name from type T.
     */
    dataKey: Extract<keyof T, string>;

    /**
     * The color to stroke the line with.
     * Must be a data-related color token
     * See data-color.type.ts for the list of accepted color tokens.
     */
    stroke: DataColor;

    /**
     * The width of the line stroke in pixels.
     * Defaults to 2 if not provided.
     */
    strokeWidth?: number;

    /**
     * The type of line to draw.
     * - 'monotone': Smooth curved line
     * - 'linear': Straight line segments
     * - 'step': Step line
     * Defaults to 'monotone' if not provided.
     */
    type?: 'monotone' | 'linear' | 'step';

    /**
     * Whether to show dots at data points.
     * Defaults to false if not provided.
     */
    dot?: boolean;

    /**
     * Whether to show active dot on hover.
     * When true, shows a square dot that matches the DataLegend squares.
     * Defaults to true if not provided.
     */
    activeDot?: boolean;
}

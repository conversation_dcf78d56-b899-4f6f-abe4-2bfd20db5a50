import { sharedControlsController } from '@controllers/controls';
import { snackbarController } from '@controllers/snackbar';
import { grcControllerAssociateControlsMutation } from '@globals/api-sdk/queries';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable, ObservedMutation, when } from '@globals/mobx';
import { sharedRequirementDetailsController } from './requirement-details-controller';

class RequirementAssociateControlsMutationController {
    constructor() {
        makeAutoObservable(this);
    }

    #postAssociateControlsMutation = new ObservedMutation(
        grcControllerAssociateControlsMutation,
    );

    get hasError(): boolean {
        return this.#postAssociateControlsMutation.hasError;
    }
    get isPending(): boolean {
        return this.#postAssociateControlsMutation.isPending;
    }

    handleAssociateControls = (controlIds: number[], requirementId: number) => {
        this.#postAssociateControlsMutation.mutate({
            path: { requirementId },
            body: { controlIds },
        });

        when(
            () => !this.isPending,
            () => {
                if (this.hasError) {
                    snackbarController.addSnackbar({
                        id: 'associate-controls-error',
                        props: {
                            title: t`Controls could not be associated`,
                            description: t`An error occurred while associating controls. Please try again.`,
                            severity: 'critical',
                            closeButtonAriaLabel: t`Close`,
                        },
                    });
                } else {
                    sharedControlsController.controlsListQuery.invalidate();
                    sharedRequirementDetailsController.invalidate();
                }
            },
        );
    };
}
export const sharedRequirementAssociateControlsMutationController =
    new RequirementAssociateControlsMutationController();

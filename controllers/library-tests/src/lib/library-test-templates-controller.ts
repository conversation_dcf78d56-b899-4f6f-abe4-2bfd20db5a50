import { isEmpty, omit } from 'lodash-es';
import type {
    DatatableRowSelectionState,
    FetchDataResponseParams,
    FilterProps,
} from '@cosmos/components/datatable';
import {
    libraryTestTemplateControllerGetTestTemplateIdsOptions,
    libraryTestTemplateControllerSearchTemplatesOptions,
} from '@globals/api-sdk/queries';
import type { LibraryTestTemplateResponseDto } from '@globals/api-sdk/types';
import {
    makeAutoObservable,
    ObservedQuery,
    runInAction,
    toJS,
    when,
} from '@globals/mobx';
import { generateFilters } from './helpers/generate-filters.helper';
import {
    generateQueryParams,
    type LibraryTestTemplateSearchParams,
} from './helpers/generate-query-params.helper';

class LibraryTestTemplatesController {
    selectedTemplates: LibraryTestTemplateResponseDto[] = [];
    isAllRowsSelected = false;
    queryParams: LibraryTestTemplateSearchParams = {
        page: 1,
        limit: 10,
        q: '',
    };

    constructor() {
        makeAutoObservable(this, {
            loadTestTemplates: false,
        });
    }

    filterPropsObserved: FilterProps = {
        clearAllButtonLabel: 'Reset',
        triggerLabel: 'Filters',
        filters: [],
    };

    pagination = {
        page: 1,
        pageSize: 10,
    };

    filters = {
        search: '',
    };

    testTemplatesQuery = new ObservedQuery(
        libraryTestTemplateControllerSearchTemplatesOptions,
    );

    testTemplateIdsQuery = new ObservedQuery(
        libraryTestTemplateControllerGetTestTemplateIdsOptions,
    );

    get error(): Error | null {
        return this.testTemplatesQuery.error;
    }

    get isLoading(): boolean {
        return this.testTemplatesQuery.isLoading;
    }

    get rawData() {
        return this.testTemplatesQuery.data;
    }

    get getTestTemplates(): LibraryTestTemplateResponseDto[] {
        return this.rawData?.templates ?? [];
    }

    get getTotal(): number {
        return this.rawData?.counts.searchTotal ?? 0;
    }

    get testsTotals() {
        return (
            this.rawData?.counts ?? {
                total: 0,
                active: 0,
                available: 0,
            }
        );
    }

    get filtersData() {
        return (
            this.rawData?.filters ?? {
                statuses: [],
                ratings: [],
                categories: [],
                frameworks: [],
                connections: [],
                resources: [],
            }
        );
    }

    get filterProps(): FilterProps {
        return toJS(this.filterPropsObserved);
    }

    getTestTemplateIds = async (): Promise<number[]> => {
        this.testTemplateIdsQuery.load({
            query: omit(this.queryParams, ['page', 'limit']),
        });

        await when(() => !this.testTemplateIdsQuery.isLoading);

        return this.testTemplateIdsQuery.data?.ids ?? [];
    };

    loadTestTemplates = (params?: FetchDataResponseParams): void => {
        runInAction(() => {
            const defaultParams: FetchDataResponseParams = {
                pagination: {
                    page: 1,
                    pageSize: 10,
                    pageIndex: 0,
                    pageSizeOptions: [10, 25, 50, 100],
                },
                globalFilter: {
                    search: '',
                    filters: {},
                },
                sorting: [],
            };

            const effectiveParams = params ?? defaultParams;

            this.queryParams = generateQueryParams(effectiveParams);

            const plainQueryParams = toJS(this.queryParams);

            this.testTemplatesQuery.load({
                query: plainQueryParams,
            });

            when(
                () => !this.isLoading && !this.error,
                () => {
                    if (this.rawData?.filters) {
                        this.filterPropsObserved.filters = generateFilters(
                            this.filtersData,
                        );
                    }
                },
            );
        });
    };

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ) => {
        const { selectedRows, isAllRowsSelected } = currentRowSelectionState;

        const selectedTemplateIds = Object.keys(selectedRows);

        // If no rows are selected and not "select all", clear everything
        if (isEmpty(selectedTemplateIds) && !isAllRowsSelected) {
            this.selectedTemplates = [];
            this.isAllRowsSelected = isAllRowsSelected;

            return;
        }

        // Keep selections from other pages + add current page selections
        const otherPageSelections = this.selectedTemplates.filter(
            (template) =>
                !this.getTestTemplates.some(
                    (current) => current.templateId === template.templateId,
                ),
        );

        const currentPageSelections = this.getTestTemplates.filter((template) =>
            selectedTemplateIds.includes(String(template.templateId)),
        );

        this.isAllRowsSelected = isAllRowsSelected;

        this.selectedTemplates = [
            ...otherPageSelections,
            ...currentPageSelections,
        ];
    };
}

export const libraryTestTemplatesController =
    new LibraryTestTemplatesController();

# Pattern Index - Your Starting Point

**Last Updated**: 2025-08-11 | **Version**: 2.0

**For LLM Agents**: This is your main reference. When coding and you need to know:
- How to import something → Check "Import Violations" below
- How to name a file → Check "Creating a New File" below
- Any other pattern → Search this file for keywords

You do NOT need to read other files unless this index points you there.

## 🎯 QUICK DECISION TREE

**What are you doing?**
- Creating a component? → [Line 158](#building-components)
- Creating a controller? → [Line 209](#creating-controllers)
- Using the API? → [Line 183](#api-sdk-auto-generated)
- Writing tests? → [Line 144](#testing--tdd)
- Working with forms? → [Line 213](#forms)
- Handling navigation? → [Line 96](#navigation-components)
- Working with tables? → [Line 104](#data-tables)
- Need state management? → [Line 169](#state-management)

## 📊 PRIORITY LEGEND

- 🔴 **CRITICAL** - Will break build or cause runtime errors
- 🟡 **IMPORTANT** - Common patterns you should follow
- 🟢 **REFERENCE** - Detailed docs to read when needed

## ⚡ QUICK WINS (90% of Tasks)

```typescript
// Controller: Always use ObservedQuery for fetching
class MyController {
  #query = new ObservedQuery(apiOptions);
  get data() { return this.#query.data; }
}

// Component: Always wrap with observer
export const MyComponent = observer(() => { ... });

// Button: Never disabled, use isLoading or cosmosUseWithCaution_isDisabled
<AppButton isLoading={loading} a11yLoadingLabel="Saving...">

// Navigation: Use AppLink/AppButton, not Link
<AppLink href="/path">Link</AppLink>
<AppButton href="/path">Button</AppButton>

// Imports: Always from @globals
import { observer, ObservedQuery } from '@globals/mobx';
import { t } from '@globals/i18n/macro';
```

## 📋 BEFORE YOU COMMIT CHECKLIST

```bash
# Run these commands EVERY TIME before committing:
pnpm run format      # Auto-fix formatting
pnpm run typecheck   # Catch type errors
pnpm run lint        # Catch code issues
```

**Quick Checks:**
- [ ] All imports from `@globals/*` not direct packages
- [ ] No `disabled` prop on buttons
- [ ] All text uses `t` from i18n
- [ ] Components wrapped with `observer`
- [ ] No default exports (except routes)

## 🚨 FIRST: AI Code Quality Guidelines

**BEFORE implementing ANY code**, read: [critical-rules/ai-code-quality-guidelines.md](./critical-rules/ai-code-quality-guidelines.md)

This contains your CORE OPERATING PRINCIPLES:
- Pattern Propagation Rule (code will be copied 50+ times)
- Quality Gates (Correctness, Consistency, Maintainability, Testability, Performance, Copy-Safety)
- Critical Red Flags (Auto-Reject criteria)
- Decision Trees for abstractions, deviations, and error handling
- Type Safety Requirements and Implementation Standards

## 🚨 CRITICAL: Found Incorrect Documentation?

**If you discover any inaccuracy in .llm files:**
1. **NOTIFY THE USER IMMEDIATELY** - Don't silently work around it
2. **Create a todo** to fix the documentation
3. **Provide specific details**: file path, line number, what's wrong, suggested fix
4. **Continue with the correct approach** but ensure the docs get updated

Example: "I found that the import path in api-sdk-patterns.md line 45 is outdated. The correct import is X not Y. I'll add a todo to fix this."

## 🚨 IMPORTANT: Adding New Documentation

**DO NOT EDIT THIS INDEX FILE** to add detailed information. This is only a quick reference!

📖 **See [DOCUMENTATION_GUIDELINES.md](./DOCUMENTATION_GUIDELINES.md) for where to add new content**

When adding new patterns or documentation:
1. Check if a relevant pattern file already exists in `.llm/development-patterns/`, `.llm/critical-rules/`, or `.llm/build-time-rules/`
2. Add your detailed documentation to the appropriate existing file
3. Only update this index with a brief pointer to that documentation
4. If no appropriate file exists, create a new one in the correct directory

Example:
- ❌ WRONG: Adding 10 lines about API workflow to this index
- ✅ RIGHT: Adding details to `api-sdk-patterns.md` and a one-line pointer here

## 🎯 QUICK START PATTERNS

### Project Structure
- Controllers live in: `controllers/[feature]/src/lib/[feature].controller.ts`
- API SDK is in: `globals/api-sdk/src/lib/generated/`
- Components live in: `components/[feature]/src/lib/`

### Need to use the API?
1. Check if endpoint exists: Look in `globals/api-sdk/src/lib/generated/sdk.gen.ts`
2. If not found: The endpoint doesn't exist yet - coordinate with backend team
3. Update SDK: Run `pnpm run update-api-sdk` after backend adds the endpoint

### Creating a Controller?
Jump to: [Creating Controllers](#creating-controllers) section below for complete example

### Creating a Component?
Jump to: [Building Components](#building-components) section below

## 🚨 CRITICAL BUILD-BREAKING RULES

These violations will cause ESLint errors or runtime failures:

### 🔴 Import Violations
**Keywords**: `import`, `mobx`, `lingui`, `@tanstack`, `react-table`
- **Rule**: Use global modules for restricted imports
- **File**: [pitfalls.md#import-restrictions](./pitfalls.md)
- **Examples**:
```typescript
// ❌ WRONG - Will fail ESLint
import { observer } from 'mobx-react-lite';
import { t } from '@lingui/macro';
import { useTable } from '@tanstack/react-table';

// ✅ CORRECT
import { observer } from '@globals/mobx';
import { t } from '@globals/i18n/macro';
import { AppDatatable } from '@cosmos/components/datatable';
```

### 🔴 Button Components
**Keywords**: `Button`, `disabled`, `cosmosUseWithCaution_isDisabled`, `isLoading`, `a11yLoadingLabel`, `accessibility`
- **Rule**: NEVER use `disabled` prop - use proper loading/disabled patterns
- **ESLint**: `custom/button-loading-props` enforces proper loading state patterns
- **Examples**:
```typescript
// ❌ WRONG - Never use disabled prop
<AppButton disabled={!isValid}>Submit</AppButton>

// ✅ CORRECT - Loading state (API calls, async work)
<AppButton isLoading={isSaving} a11yLoadingLabel="Saving...">Save</AppButton>

// ✅ CORRECT - Non-loading disabled (permissions, features)
<AppButton cosmosUseWithCaution_isDisabled={!hasPermission}>Delete</AppButton>
```
- **Files**:
  - [development-patterns/button-prop-decision-tree.md](./development-patterns/button-prop-decision-tree.md) - Decision tree for choosing props
  - [critical-rules/button-disabled-prop.md](./critical-rules/button-disabled-prop.md) - CRITICAL: disabled prop forbidden
  - [build-time-rules/button-loading-eslint.md](./build-time-rules/button-loading-eslint.md) - ESLint rule enforcement
  - [development-patterns/button-patterns.md](./development-patterns/button-patterns.md) - Complete button usage guide

### 🔴 Non-Interactive Event Handlers
**Keywords**: `onClick on div`, `Box onClick`, `Stack onClick`, `keyboard`, `role`, `tabIndex`
- **Rule**: Never add event handlers to non-interactive elements (div, span, Box, Stack). Use AppLink/AppButton instead.
- **File**: [critical-rules/noninteractive-event-handlers.md](./critical-rules/noninteractive-event-handlers.md)
- **Examples**:
```typescript
// ❌ WRONG - onClick on non-interactive element
<Box onClick={handleClick}>Click me</Box>
<div onClick={() => navigate('/path')}>Navigate</div>

// ✅ CORRECT - Use interactive components
<AppButton onClick={handleClick}>Click me</AppButton>
<AppLink href="/path">Navigate</AppLink>
```


### 🔴 Navigation Components
**Keywords**: `Link`, `navigate`, `href`, `routing`, `redirect`
- **Rule**: Use AppLink/AppButton for declarative navigation, useNavigate for programmatic
- **Examples**:
```typescript
// ❌ WRONG
import { Link } from '@remix-run/react';
<Link to="/path">Click me</Link>

// ✅ CORRECT - Declarative navigation
import { AppLink } from '@ui/app-link';
import { AppButton } from '@ui/app-button';
<AppLink href="/path">Click me</AppLink>
<AppButton href="/path">Button link</AppButton>

// ✅ CORRECT - Programmatic navigation
import { useNavigate } from '@remix-run/react';
const navigate = useNavigate();
navigate('/path');
```
- **Files**:
  - [development-patterns/navigation-patterns.md](./development-patterns/navigation-patterns.md) - Complete navigation guide
  - [critical-rules/applink-usage.md](./critical-rules/applink-usage.md) - AppLink details
  - [development-patterns/appbutton-href-usage.md](./development-patterns/appbutton-href-usage.md) - AppButton navigation

### 🔴 Data Tables
**Keywords**: `table`, `datatable`, `grid`, `rows`, `row actions`, `rowActionsProps`
- **Rule**: Always use AppDatatable, never legacy Datatable
- **Files**:
  - [critical-rules/appdatatable-usage.md](./critical-rules/appdatatable-usage.md)
  - [critical-rules/appdatatable-cells.md](./critical-rules/appdatatable-cells.md)
  - [development-patterns/datatable-row-actions.md](./development-patterns/datatable-row-actions.md)
- **Examples**:
```typescript
// ❌ WRONG - Legacy datatable
import { Datatable } from '@cosmos/components/datatable';

// ✅ CORRECT - Always use AppDatatable
import { AppDatatable } from '@components/app-datatable';
```

### 🔴 Export Rules
**Keywords**: `export`, `default`, `module`
- **Rule**: Named exports only (except routes)
- **File**: [critical-rules/export-patterns.md](./critical-rules/export-patterns.md)

### 🟡 Test Import Handling
**Keywords**: `test import`, `module not found`, `await outside async`, `TDD imports`
- **Rule**: Handle non-existent imports gracefully in tests
- **File**: [critical-rules/test-import-handling.md](./critical-rules/test-import-handling.md)
- **Examples**:
```typescript
// ✅ CORRECT - Handle non-existent imports in TDD
let MyComponent: any;
beforeEach(async () => {
  try {
    ({ MyComponent } = await import('./my-component'));
  } catch {
    MyComponent = () => null; // Stub for red phase
  }
});
```

### 🔴 Render Props Anti-Pattern
**Keywords**: `render*`, `renderAction`, `renderItem`, `renderRow`, `render function`, `component inside function`, `const someComponent =`
- **Rule**: NEVER create render functions OR component variables inside components - breaks React reconciliation
- **Pattern**: Any function starting with "render" that returns JSX, OR `const someComponent = <Component />` inside render
- **File**: [critical-rules/no-render-props-antipattern.md](./critical-rules/no-render-props-antipattern.md)
- **Examples**:
```typescript
// ❌ WRONG - Render function inside component
const MyComponent = () => {
  const renderItem = (item) => <div>{item.name}</div>;
  return items.map(renderItem);
};

// ✅ CORRECT - Extract to component or inline JSX
const ItemDisplay = ({ item }) => <div>{item.name}</div>;
const MyComponent = () => {
  return items.map(item => <ItemDisplay key={item.id} item={item} />);
};
```

## 📋 PATTERN LOOKUP BY TASK

### 🟡 Creating a New File
**Keywords**: `new file`, `create`, `naming`, `test`, `spec`
1. **File Naming**: [build-time-rules/file-naming.md](./build-time-rules/file-naming.md)
   - Format: `export-name.modifier.extension`
   - Use kebab-case only
   - Test files use `.spec.ts` or `.spec.tsx` (NOT `.test.ts`)
2. **Single Export**: [build-time-rules/single-export-files.md](./build-time-rules/single-export-files.md)
   - One export per file
   - Filename matches export name

### 🟡 Testing & TDD
**Keywords**: `test`, `tdd`, `spec`, `vitest`, `testing-library`, `red phase`, `failing test`
1. **TDD Patterns**: [development-patterns/tdd-testing-patterns.md](./development-patterns/tdd-testing-patterns.md)
   - Writing tests before implementation
   - Handling non-existent imports
   - Proper test structure
   - Common TDD mistakes to avoid
2. **Testing Pitfalls**: [development-patterns/testing-pitfalls.md](./development-patterns/testing-pitfalls.md)
   - Common mistakes and how to avoid them
   - Pre-submission checklist
   - Real examples from the codebase
3. **Test Naming**: Use `.spec.ts` or `.spec.tsx` extension
4. **Test Structure**: Lowercase describe blocks, proper spacing

### 🟡 Building Components
**Keywords**: `component`, `react`, `UI`
1. **Design Pattern**: [development-patterns/component-design.md](./development-patterns/component-design.md)
   - Separation of concerns
   - Composition over configuration
2. **Styling**: [development-patterns/styling.md](./development-patterns/styling.md)
   - CSS modules + design tokens
   - No child selectors
3. **Data Attributes**: [development-patterns/conventions.md#component-requirements](./development-patterns/conventions.md)
   - Add `data-testid` and `data-id`

### 🟡 State Management
**Keywords**: `state`, `mobx`, `controller`, `model`, `useState`
1. **MobX Pattern**: [development-patterns/state-management.md](./development-patterns/state-management.md)
   - Use Controllers + Models
   - Avoid React state for business logic
2. **Mutations**: [critical-rules/mobx-mutations.md](./critical-rules/mobx-mutations.md)
   - ObservedMutation + onSuccess + when()
3. **Data Fetching**: [development-patterns/data-fetching.md](./development-patterns/data-fetching.md)
   - ObservedQuery for fetching
   - `.load()` and `.invalidate()`

### 🟡 API SDK (Auto-Generated)
**Keywords**: `api`, `sdk`, `openapi`, `generated`, `fetch`, `endpoint`
1. **Complete Guide**: [development-patterns/api-sdk-patterns.md](./development-patterns/api-sdk-patterns.md)
2. **Quick Check**: API files are in `globals/api-sdk/src/lib/`
   - Generated files: `globals/api-sdk/src/lib/generated/`
   - **Endpoint Index**: `globals/api-sdk/src/lib/generated/api-endpoints.json` (auto-generated)
3. **Quick Usage**: Import from `@globals/api-sdk`
   - **NEVER** edit files in `@globals/api-sdk` - they're auto-generated!
   - Use `pnpm run update-api-sdk` to sync with backend
4. **Finding Endpoints**:
   - Check the auto-generated index: `globals/api-sdk/src/lib/generated/api-endpoints.json`
   - This file contains all controllers, queries, and mutations
   - Updated automatically when you run `pnpm run update-api-sdk`
   - See [Finding Available Endpoints](./development-patterns/api-sdk-patterns.md#finding-available-endpoints) for the complete workflow
5. **Can't Find an Endpoint?**
   - If endpoint doesn't exist in `api-endpoints.json`, it hasn't been added to backend yet
   - Check with backend team or create a ticket for the new endpoint
   - Once backend adds it, run `pnpm run update-api-sdk`
6. **Query Pattern**: See [Data Fetching with ObservedQuery](./development-patterns/api-sdk-patterns.md#data-fetching-with-observedquery)
7. **Mutation Pattern**: See [Data Mutations with ObservedMutation](./development-patterns/api-sdk-patterns.md#data-mutations-with-observedmutation)

### 🟡 Creating Controllers
**Keywords**: `controller`, `new controller`, `create controller`, `mobx controller`
- **Complete Guide**: [Controller Patterns](./development-patterns/controller-patterns.md)
- **Quick Info**: Controllers live in `controllers/[feature-name]/src/lib/[feature-name].controller.ts`
- **Key Concepts**: ObservedQuery for fetching, ObservedMutation for actions
- **State Management**: [State Management Patterns](./development-patterns/state-management.md)

### 🔴 Forms
**Keywords**: `form`, `input`, `validation`, `zod`, `submit`
**CRITICAL**: NEVER disable submit buttons for validation - let users submit and show feedback
1. **Form Submission**: [development-patterns/form-submission-patterns.md](./development-patterns/form-submission-patterns.md)
   - CRITICAL: Submit button patterns and validation handling
2. **Form System**: [development-patterns/form-system.md](./development-patterns/form-system.md)
   - Schema-based with Zod
3. **Form Fields**: [development-patterns/form-fields.md](./development-patterns/form-fields.md)
   - Field types reference
4. **Validation**: [development-patterns/form-validation.md](./development-patterns/form-validation.md)
   - Zod schemas and patterns

### 🟡 Wizard Component
**Keywords**: `wizard`, `multi-step`, `onStepChange`, `onComplete`, `step`
**CRITICAL**: `onStepChange` is NOT called for the last step - handle it in `onComplete`
1. **Wizard Patterns**: [development-patterns/wizard-patterns.md](./development-patterns/wizard-patterns.md)
   - Last step behavior, conditional steps, validation patterns

### 🔴 TypeScript
**Keywords**: `type`, `interface`, `satisfies`, `as`, `array`, `[]`, `assertion`, `type safety`
**🚨 CRITICAL**: Never use type assertions (`as`, `!`) as quick fixes - they bypass type safety
1. **No Type Assertions**: [critical-rules/no-type-assertions.md](./critical-rules/no-type-assertions.md)
   - **CRITICAL**: Why type assertions are dangerous and proper alternatives
2. **TypeScript Patterns**: [build-time-rules/typescript-patterns.md](./build-time-rules/typescript-patterns.md)
   - Use `satisfies` operator
   - Detailed type assertion guidance
   - Use bracket syntax `[]` for array types (not `Array<>`)
3. **Data Mapping**: [development-patterns/data-mapping-patterns.md](./development-patterns/data-mapping-patterns.md)
   - Switch statements over objects

### 🟢 ESLint & Code Quality
**Keywords**: `eslint`, `lint`, `formatting`, `code style`, `import order`
1. **ESLint & TypeScript Rules**: [build-time-rules/eslint-typescript-rules.md](./build-time-rules/eslint-typescript-rules.md)
   - Comprehensive ESLint rules guide
   - TypeScript configuration and common issues
   - Auto-fix commands and IDE integration
2. **Testing Pitfalls**: [development-patterns/testing-pitfalls.md](./development-patterns/testing-pitfalls.md)
   - Common testing mistakes to avoid

### 🟡 Internationalization
**Keywords**: `i18n`, `translate`, `t`, `Trans`, `locale`
1. **i18n Patterns**: [development-patterns/internationalization.md](./development-patterns/internationalization.md)
   - Function-based translations
   - Avoid constants with t``
   - Import from `@globals/i18n/macro`

### 🟢 Error Handling
**Keywords**: `error`, `exception`, `api error`, `logging`, `snackbar`, `user message`
1. **Error Handling**: [development-patterns/error-handling.md](./development-patterns/error-handling.md)
   - Two-step process: log for engineering, help the customer
   - User-friendly messages with internationalization
   - Never expose internal system details

### 🟡 Permissions & Access
**Keywords**: `permission`, `access`, `auth`, `feature flag`
1. **Access Control**: [development-patterns/permissions-and-access.md](./development-patterns/permissions-and-access.md)
   - Always use FeatureAccessModel
   - Never direct permission checks

### 🟢 Package/Module Creation
**Keywords**: `package`, `barrel`, `export`, `public API`
1. **Package Exports**: [build-time-rules/package-exports.md](./build-time-rules/package-exports.md)
   - Explicit barrel exports
   - No `export *`

### 🟢 File Organization
**Keywords**: `structure`, `folder`, `organize`, `directory`
1. **Organization**: [development-patterns/file-organization.md](./development-patterns/file-organization.md)
   - Domain-driven structure
   - Co-location principles

## 🔍 QUICK VIOLATION CHECKER

Before committing code, check for these common violations:

### 🔴 Commit Message Format
**Keywords**: `commit`, `git`, `conventional`, `changelog`
📖 **Complete Documentation**: [critical-rules/conventional-commits.md](./critical-rules/conventional-commits.md)

```bash
# ❌ WRONG - Non-conventional format
"Add new feature"
"Fixed bug"
"Updated docs"

# ✅ CORRECT - Angular conventional commits
feat(auth): add OAuth2 integration
fix(datatable): resolve pagination reset on filter
docs(readme): update installation instructions
```

```typescript
// ❌ VIOLATIONS THAT WILL FAIL
import { observer } from 'mobx-react-lite'; // → @globals/mobx
import { Link } from '@remix-run/react'; // → @ui/app-link
export default MyComponent; // → export const MyComponent
const label = t`Hello`; // → const getLabel = () => t`Hello`
const user = data as User; // → Use type guards instead
const name = user!.name; // → Use optional chaining user?.name
if (user.permissions.includes('admin')) // → featureAccessModel.canAccess()

// ✅ CORRECT PATTERNS
import { observer } from '@globals/mobx';
import { AppLink } from '@ui/app-link';
export const MyComponent = observer(() => {
  const getLabel = () => t`Hello`;
  if (sharedFeatureAccessModel.canAccess('admin')) {
    // ...
  }
});
```

## 🎯 CONTEXT TRIGGERS

These keywords should trigger loading specific documentation:

- **"import"** → Check import restrictions
- **"export"** → Check export patterns
- **"state"** → Load state management patterns
- **"form"** → Load form system docs
- **"table"** → Load AppDatatable patterns
- **"row actions"/"rowActionsProps"** → Load datatable row actions guide
- **"button"/"disabled"** → Load button patterns (NEVER use disabled prop!)
- **"link"/"navigate"/"redirect"** → Load navigation patterns
- **"permission"/"access"** → Load FeatureAccessModel docs
- **"style"/"css"** → Load styling patterns
- **"controller"/"model"** → Load MobX patterns & Creating Controllers
- **"translate"/"i18n"** → Load internationalization
- **"api"/"sdk"/"fetch"** → Load API SDK patterns
- **"error"/"exception"/"logging"/"snackbar"** → Load error handling patterns
- **"create controller"/"new controller"** → Load Creating Controllers section
- **"endpoint"/"openapi"** → Load API SDK patterns
- **"test"/"tdd"/"spec"/"vitest"** → Load TDD testing patterns
- **"red phase"/"failing test"** → Load TDD patterns for tests before implementation
- **"testing mistake"/"test error"/"lint error"** → Load testing pitfalls
- **"wizard"/"multi-step"/"onStepChange"/"onComplete"** → Load wizard patterns
- **"eslint"/"lint"/"formatting"/"code style"/"import order"** → Load ESLint & TypeScript rules
- **"type error"/"typescript error"/"type assertion"/"as"/"!"/"type safety"** → Load TypeScript section & No Type Assertions rule
- **"commit"/"git"/"conventional"/"changelog"** → Load conventional commits format

## 📍 NAVIGATION SHORTCUTS

- **Project Setup**: [README.md](./README.md) → [project-docs/overview.md](./project-docs/overview.md)
- **Code Style**: [development-patterns/conventions.md](./development-patterns/conventions.md) → [project-docs/tooling.md](./project-docs/tooling.md)
- **Common Issues**: [development-patterns/pitfalls.md](./development-patterns/pitfalls.md)
- **Contributing**: [project-docs/contribution.md](./project-docs/contribution.md)
- **Development Patterns**: [development-patterns/](./development-patterns/)
- **Critical Rules**: [critical-rules/](./critical-rules/)
- **Build-Time Rules**: [build-time-rules/](./build-time-rules/)

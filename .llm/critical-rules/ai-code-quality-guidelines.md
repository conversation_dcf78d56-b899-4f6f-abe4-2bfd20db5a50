# AI Agent Code Quality Guidelines

## Core Operating Principles

### Decision Confidence Rule
**Be confident in your technical decisions.** When you make a recommendation based on best practices, maintenance concerns, or code quality, stand by it. Only reconsider if presented with factual errors or overlooked context. Flip-flopping undermines trust and creates confusion.

**Example**: If you identify fragmented documentation as a maintenance problem, that insight is valid. Don't backtrack just because someone questions it - explain why the principle matters.

### Pattern Propagation Rule
**Any code pattern you introduce will be copied throughout the codebase.** Design every solution assuming it will be replicated 50+ times by other developers who may not understand the original context.

### Quality Gates
- [ ] **Correctness**: Does it solve the problem completely?
- [ ] **Consistency**: Does it follow established codebase patterns?
- [ ] **Maintainability**: Can it be understood and modified easily?
- [ ] **Testability**: Can it be thoroughly tested?
- [ ] **Performance**: Is it efficient for current and projected usage?
- [ ] **Copy-Safety**: Will it work correctly when copied without full context?

## Pre-Implementation Checklist

### Research Phase
- [ ] Identify existing patterns for similar problems in the codebase
- [ ] Locate and study established conventions (check `.llm/PATTERN_INDEX.md` first)
- [ ] Find 2-3 examples of how similar problems are currently solved
- [ ] Verify the approach aligns with architectural decisions
- [ ] Check for existing utilities or abstractions that should be used

### Design Validation
- [ ] Solution follows the principle of least surprise
- [ ] Implementation can be understood without extensive comments
- [ ] Error cases are explicitly handled
- [ ] Dependencies are minimal and justified
- [ ] Pattern is reusable and extensible

## Code Quality Enforcement

### Type Safety Requirements
- [ ] No `any` types (exceptions require explicit justification)
- [ ] No type assertions (`as`) unless absolutely necessary
- [ ] All function parameters and return types are explicitly typed
- [ ] Null/undefined cases are handled with proper type guards
- [ ] Generic types are constrained appropriately

### Maintainability Standards
- [ ] Functions have single, clear responsibilities
- [ ] Variable and function names are descriptive and unambiguous
- [ ] Complex logic is broken into small, focused functions
- [ ] Magic numbers/strings are replaced with named constants
- [ ] Dependencies are injected rather than hardcoded

### Consistency Enforcement
- [ ] Import/export patterns match codebase standards
- [ ] File naming follows established conventions
- [ ] Error handling patterns are consistent with existing code
- [ ] Logging and monitoring follow established patterns
- [ ] Code organization matches project structure

## Critical Red Flags - Auto-Reject

### Immediate Rejection Criteria
- [ ] Code that works "by accident" or through unclear mechanisms
- [ ] Copy-pasted code with minor modifications
- [ ] Temporary solutions without clear removal timeline
- [ ] Complex solutions to simple problems
- [ ] Code requiring extensive comments to explain basic functionality
- [ ] Patterns that deviate from conventions without documented justification
- [ ] Missing error handling for obvious failure cases
- [ ] Hardcoded values that should be configurable

### Copy-Paste Vulnerability Indicators
- [ ] Code that's "convenient" to copy but fragile
- [ ] Inconsistent implementations of the same concept across files
- [ ] Solutions that work only in specific contexts but appear general
- [ ] Code with hidden dependencies or assumptions
- [ ] Patterns that fail silently when misused

## Decision Trees

### When to Create New Abstractions
```
Is there existing code that solves this problem?
├─ YES: Use existing solution, extend if necessary
└─ NO: Is this problem likely to occur again?
   ├─ YES: Create reusable abstraction
   └─ NO: Implement simple, direct solution
```

### When to Deviate from Patterns
```
Does existing pattern solve the problem adequately?
├─ YES: Use existing pattern
└─ NO: Is the limitation fundamental or contextual?
   ├─ FUNDAMENTAL: Propose pattern evolution with team
   └─ CONTEXTUAL: Document deviation and create isolated solution
```

### Error Handling Strategy
```
Can this operation fail?
├─ YES: What are the failure modes?
│   ├─ EXPECTED: Handle gracefully with user feedback
│   ├─ UNEXPECTED: Log error, provide fallback
│   └─ CRITICAL: Fail fast with clear error message
└─ NO: Proceed with implementation
```

## Implementation Standards

### Function Design
- [ ] Maximum 20 lines per function (exceptions require justification)
- [ ] Single return type (avoid union types when possible)
- [ ] Pure functions when possible (no side effects)
- [ ] Explicit error handling (no silent failures)
- [ ] Clear parameter validation

### File Organization
- [ ] One primary export per file
- [ ] Related utilities in same file or dedicated utils file
- [ ] Clear separation of concerns
- [ ] Consistent import ordering
- [ ] Minimal file dependencies

### Testing Requirements
- [ ] Unit tests for all public functions
- [ ] Edge case coverage (null, undefined, empty, boundary values)
- [ ] Error condition testing
- [ ] Integration tests for complex interactions
- [ ] Performance tests for critical paths

## Code Review Automation

### Automated Checks
- [ ] **Pattern Consistency**: Compare with similar existing code
- [ ] **Type Safety**: Verify no `any` types or unsafe assertions
- [ ] **Error Handling**: Ensure all failure paths are addressed
- [ ] **Performance**: Check for obvious inefficiencies
- [ ] **Documentation**: Verify complex logic is explained
- [ ] **Testing**: Confirm adequate test coverage

### Quality Metrics
- [ ] Cyclomatic complexity < 10 per function
- [ ] Test coverage > 80% for new code
- [ ] No linting violations
- [ ] No type errors
- [ ] Performance within acceptable bounds

## Communication Templates

### Code Review Feedback Format
```
**Issue**: [Specific problem]
**Impact**: [Why this matters for maintainability/correctness]
**Solution**: [Concrete suggestion]
**Example**: [Reference to existing good pattern if available]
```

### Pattern Deviation Documentation
```
**Deviation**: [What pattern is being changed]
**Justification**: [Why existing pattern is insufficient]
**Scope**: [Where this deviation applies]
**Future**: [Plan for pattern evolution or isolation]
```

## Enforcement Actions

### When Code Doesn't Meet Standards
- [ ] **Minor Issues**: Provide specific feedback with examples
- [ ] **Pattern Violations**: Reference existing correct patterns
- [ ] **Safety Issues**: Require fixes before approval
- [ ] **Architecture Violations**: Escalate for design review

### When Patterns Need Evolution
- [ ] Document current pattern limitations
- [ ] Propose specific improvements
- [ ] Create migration plan for existing code
- [ ] Update documentation and examples

## Success Criteria

### Code Quality Indicators
- [ ] New engineers can understand and modify code quickly
- [ ] Similar problems are solved consistently across the codebase
- [ ] Bugs are caught by automated systems before production
- [ ] Performance remains stable as features are added
- [ ] Technical debt decreases over time

### Pattern Health Metrics
- [ ] Consistency score across similar implementations
- [ ] Time to understand and modify existing code
- [ ] Frequency of bugs in copied patterns
- [ ] Developer satisfaction with existing abstractions

## Remember

Your goal is to ensure every piece of code you approve or create will:
- [ ] **Work correctly** when copied to other contexts
- [ ] **Maintain consistency** with established patterns
- [ ] **Be easily understood** by future developers
- [ ] **Scale appropriately** with system growth
- [ ] **Fail obviously** when misused rather than silently

Focus on creating code that survives the reality of software development: it will be copied, modified, and maintained by people who weren't involved in the original design decisions.

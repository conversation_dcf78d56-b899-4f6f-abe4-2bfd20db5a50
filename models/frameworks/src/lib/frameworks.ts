import {
    sharedRequirementDetailsController,
    sharedRequirementDissociateControlsMutationController,
} from '@controllers/requirements';
import type {
    BulkAction,
    DatatableRef,
    DatatableRowSelectionState,
} from '@cosmos/components/datatable';
import { sharedCurrentUserController } from '@globals/current-user';
import { t } from '@globals/i18n/macro';
import { action, makeAutoObservable } from '@globals/mobx';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

class FrameworksModel {
    selectedRowIds: number[] = [];

    constructor() {
        makeAutoObservable(this);
    }

    bulkActions(datatableRef: React.RefObject<DatatableRef>): BulkAction[] {
        const { requirement } = sharedRequirementDetailsController;

        const handleOpenModal = action(() => {
            openConfirmationModal({
                title: t`Unmap requirements?`,
                body: t`This requirement will no longer be mapped to the selected controls.`,
                confirmText: t`Unmap`,
                cancelText: t`Cancel`,
                type: 'danger',
                size: 'md',
                onConfirm: () => {
                    if (requirement) {
                        datatableRef.current?.resetRowSelection();
                        sharedRequirementDissociateControlsMutationController.handleDissociateControls(
                            this.selectedRowIds,
                            requirement.id,
                        );
                    }
                    closeConfirmationModal();
                },
                onCancel: closeConfirmationModal,
            });
        });

        return [
            {
                actionType: 'button',
                id: 'unmap-control-bulk-button',
                typeProps: {
                    label: t`Unmap controls`,
                    level: 'tertiary',
                    onClick: handleOpenModal,
                },
            },
        ];
    }

    handleRowSelection = (
        currentRowSelectionState: DatatableRowSelectionState,
    ): void => {
        const { selectedRows } = currentRowSelectionState;

        this.selectedRowIds = Object.entries(selectedRows)
            .filter(([, isSelected]) => isSelected)
            .map(([rowId]) => Number(rowId));
    };

    get isFrameworksDomainEnabled(): boolean {
        return sharedCurrentUserController.hasUserPermission(
            'Framework',
            'READ',
        );
    }
}

export const sharedFrameworksModel = new FrameworksModel();

import type { CreateReviewPeriodRequestDto } from '@globals/api-sdk/types';
import { makeAutoObservable } from '@globals/mobx';
import { addMonths } from '@helpers/date-time';

const DEFAULT_ADD_MONTHS_TO_FROM_TODAY = 3;

export class CreatePeriodModel {
    constructor() {
        makeAutoObservable(this);
    }

    get defaultStartingDate(): string {
        return new Date().toISOString().split('T')[0];
    }

    get defaultEndingDate(): string {
        const date = new Date(
            addMonths(
                this.defaultStartingDate,
                DEFAULT_ADD_MONTHS_TO_FROM_TODAY,
            ),
        );

        date.setDate(date.getDate() + 1);

        return date.toISOString().split('T')[0];
    }

    get defaultFormData(): CreateReviewPeriodRequestDto {
        return {
            startingDate: this.defaultStartingDate,
            endingDate: this.defaultEndingDate,
            applications: [],
        };
    }
}

export const createPeriodModel = new CreatePeriodModel();

import type { ComponentProps } from 'react';
import type { ActionStack } from '@cosmos/components/action-stack';
import type {
    VendorResponseDto,
    VendorSecurityReviewResponseDto,
} from '@globals/api-sdk/types';
import { t } from '@globals/i18n/macro';
import { getVendorSecurityReviewHeaderActions } from '../helpers/vendor-security-review-header-actions.helper';

export const buildStatus = (
    status: VendorSecurityReviewResponseDto['status'],
): string => {
    switch (status) {
        case 'COMPLETED': {
            return t`Completed`;
        }
        case 'IN_PROGRESS': {
            return t`In progress`;
        }
        case 'NOT_YET_STARTED': {
            return t`Not yet started`;
        }
        default: {
            return '-';
        }
    }
};

export const buildImpactLevel = (
    impactLevel: VendorResponseDto['impactLevel'] | undefined,
): string => {
    switch (impactLevel) {
        case 'INSIGNIFICANT': {
            return t`Insignificant`;
        }
        case 'MINOR': {
            return t`Minor`;
        }
        case 'MODERATE': {
            return t`Moderate`;
        }
        case 'MAJOR': {
            return t`Major`;
        }
        case 'CRITICAL': {
            return t`Critical`;
        }
        case 'UNSCORED': {
            return t`Unscored`;
        }
        default: {
            return '-';
        }
    }
};

export const getHeaderStackActions = (
    navigateCallback: () => void,
): ComponentProps<typeof ActionStack>['stacks'] => {
    return getVendorSecurityReviewHeaderActions(navigateCallback);
};

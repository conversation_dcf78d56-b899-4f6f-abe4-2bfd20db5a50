import { getVendorSecurityReviewLabelByType } from '@components/vendors-security-reviews';
import { VendorSecurityReviewsSocActionStackNavigateComponent } from '@components/vendors-security-reviews-soc';
import {
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewDocumentsController,
} from '@controllers/vendors';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import type { VendorCategory } from '@models/vendors-profile';
import { getHeaderStackActions } from './config/vendor-security-review-soc-header.config';

export class SocReportPageHeaderModel {
    #parentUrl: string | null = null;
    #vendorType: VendorCategory = 'current';

    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'vendors-profile-security-review-soc-page';

    get breadcrumbs(): Breadcrumb[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { vendor } = securityReviewDetails ?? {};
        const vendorId = vendor?.id;
        const vendorTypePrefix = this.#vendorType;

        return [
            { label: t`Vendors`, pathname: `vendors/${vendorTypePrefix}` },
            {
                label: vendor?.name ?? '',
                pathname: `vendors/${vendorTypePrefix}/${vendorId}`,
            },
            {
                label: t`Security reviews`,
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/security-reviews`,
            },
        ];
    }

    get title(): string {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { reviewDocument } =
            sharedVendorsSecurityReviewDocumentsController;

        const { title } = securityReviewDetails ?? {};

        if (title) {
            return title;
        }

        return reviewDocument?.vendorReview
            ? getVendorSecurityReviewLabelByType({
                  type: 'SOC_REPORT',
                  requestedAt:
                      reviewDocument.vendorReview.reportIssueDate ?? '',
              })
            : '';
    }

    get actionStack(): React.JSX.Element {
        return (
            <VendorSecurityReviewsSocActionStackNavigateComponent
                data-id="vendors-profile-security-review-soc-page-action-stack"
                parentUrl={this.#parentUrl ?? ''}
                actions={(navigateCallback: () => void) =>
                    getHeaderStackActions(navigateCallback)
                }
            />
        );
    }

    setParentUrl(parentUrl: string): void {
        this.#parentUrl = parentUrl;
    }

    setVendorType(vendorType: VendorCategory): void {
        this.#vendorType = vendorType;
    }
}

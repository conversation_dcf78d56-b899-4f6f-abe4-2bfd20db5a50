import type { ComponentProps } from 'react';
import { openDeleteVendorModalFromProfile } from '@components/vendors-current-delete-vendor';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';
import { openMarkVendorAsActiveModal } from '@views/vendors-mark-vendor-as-active';

const VENDORS_PROSPECTIVE_PROFILE_HEADER_KEY = 'vendors-current-profile-header';

export const getVendorsProspectiveProfileHeaderActionValues = (): NonNullable<
    ComponentProps<typeof ActionStack>['stacks']
> => {
    const { data: vendor } = sharedVendorsDetailsController.vendorDetailsQuery;
    const isArchived = vendor?.status === 'ARCHIVED';

    return [
        {
            actions: [
                {
                    actionType: 'dropdown',
                    id: `${VENDORS_PROSPECTIVE_PROFILE_HEADER_KEY}-horizontal-menu`,
                    typeProps: {
                        isIconOnly: true,
                        startIconName: 'HorizontalMenu',
                        level: 'tertiary',
                        label: 'Options',
                        align: 'end',
                        items: [
                            {
                                id: 'vendors-prospective-row-dropdown-archive-vendor',
                                label: isArchived
                                    ? t`Restore vendor`
                                    : t`Archive vendor`,
                                type: 'item',
                                value: 'ARCHIVE_VENDOR',
                                onClick: () => {
                                    runInAction(() => {
                                        const currentVendor =
                                            sharedVendorsDetailsController
                                                .vendorDetailsQuery.data;

                                        if (currentVendor) {
                                            const currentIsArchived =
                                                currentVendor.status ===
                                                'ARCHIVED';

                                            if (currentIsArchived) {
                                                sharedVendorsDetailsController.restoreVendor(
                                                    currentVendor.id,
                                                );
                                            } else {
                                                sharedVendorsDetailsController.archiveVendor(
                                                    currentVendor.id,
                                                );
                                            }
                                        }
                                    });
                                },
                            },
                            {
                                id: 'vendors-prospective-row-dropdown-delete-vendor',
                                label: t`Delete vendor`,
                                type: 'item',
                                value: 'DELETE_VENDOR',
                                colorScheme: 'critical',
                                onClick: () => {
                                    runInAction(() => {
                                        const currentVendor =
                                            sharedVendorsDetailsController
                                                .vendorDetailsQuery.data;

                                        if (currentVendor) {
                                            openDeleteVendorModalFromProfile({
                                                isProspective: true,
                                            });
                                        }
                                    });
                                },
                            },
                        ],
                    },
                },
                {
                    actionType: 'button',
                    id: `${VENDORS_PROSPECTIVE_PROFILE_HEADER_KEY}-mark-as-active-button`,
                    typeProps: {
                        label: t`Mark vendor as active`,
                        level: 'secondary',
                        onClick: () => {
                            runInAction(() => {
                                const currentVendor =
                                    sharedVendorsDetailsController
                                        .vendorDetailsQuery.data;

                                if (currentVendor) {
                                    openMarkVendorAsActiveModal();
                                }
                            });
                        },
                    },
                },
            ],
            id: `prospective-${vendor?.id}-documents-and-reports-header-actions-stack`,
        },
    ];
};

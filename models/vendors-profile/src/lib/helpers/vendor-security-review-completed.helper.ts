import { noop } from 'lodash-es';
import {
    sharedSOCReviewFinalizeReviewController,
    sharedSOCReviewReopenController,
} from '@components/vendors-security-reviews-soc';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
    sharedVendorsSecurityReviewMutationController,
} from '@controllers/vendors';
import type { ActionStackProps } from '@cosmos/components/action-stack';
import type { SchemaDropdownItems } from '@cosmos/components/schema-dropdown';
import type { VendorResponseDto } from '@globals/api-sdk/types';
import { sharedFeatureAccessModel } from '@globals/feature-access';
import { t } from '@globals/i18n/macro';
import { action } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import {
    closeConfirmationModal,
    openConfirmationModal,
} from '@helpers/temp-confirmation-modal';

export const handleDeleteReview = action(
    (securityReviewId: number, onSuccess?: () => void): void => {
        openConfirmationModal({
            title: t`Delete Security Review`,
            body: t`Confirm that you'd like to delete this Security Review. Any uploaded documents to this review will also be removed.`,
            confirmText: t`Yes, delete review`,
            cancelText: t`No, take me back`,
            type: 'danger',
            size: 'md',
            onConfirm: action(() => {
                sharedVendorsSecurityReviewMutationController.deleteSecurityReviewWithCallback(
                    securityReviewId,
                    onSuccess,
                );
            }),
            onCancel: () => {
                closeConfirmationModal();
            },
        });
    },
);

export const navigateToReopenedSecurityReview = action(
    (
        reopenedSecurityReviewId: number,
        vendorId: number,
        vendorStatus?: VendorResponseDto['status'],
    ): void => {
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        if (!workspaceId) {
            return;
        }

        const vendorType =
            vendorStatus === 'PROSPECTIVE' ? 'prospective' : 'current';

        sharedProgrammaticNavigationController.navigateTo(
            `/workspaces/${workspaceId}/vendors/${vendorType}/${vendorId}/security-reviews/${reopenedSecurityReviewId}`,
        );
    },
);

export const handleReopenReview = action(
    (
        securityReviewId: number,
        vendorStatus?: VendorResponseDto['status'],
    ): void => {
        const { isVendorEditable } = sharedFeatureAccessModel;
        const workspaceId = sharedWorkspacesController.currentWorkspaceId;

        if (!isVendorEditable || !workspaceId) {
            return;
        }

        const isSocReview =
            sharedVendorsSecurityReviewDetailsController.securityReviewDetails
                ?.type === 'SOC_REPORT';

        if (isSocReview) {
            sharedSOCReviewReopenController.reOpenSocReviewAsync().catch(noop);

            return;
        }

        sharedVendorsSecurityReviewMutationController.reopenSpecificSecurityReview(
            securityReviewId,
            (reopenedSecurityReviewId: number, vendorId: number) => {
                navigateToReopenedSecurityReview(
                    reopenedSecurityReviewId,
                    vendorId,
                    vendorStatus,
                );
            },
        );
    },
);

export const handleArchiveVendor = action((vendorId: number): void => {
    const workspaceId = sharedWorkspacesController.currentWorkspaceId;

    if (!workspaceId) {
        return;
    }

    const redirectPath = `/workspaces/${workspaceId}/vendors/current/${vendorId}/overview`;

    sharedVendorsDetailsController.archiveVendorAndRedirect(
        vendorId,
        redirectPath,
    );
});

export const handleDownloadSummary = action(
    (securityReviewId: number): void => {
        const { isVendorEditable } = sharedFeatureAccessModel;

        if (!isVendorEditable) {
            return;
        }

        sharedVendorsSecurityReviewMutationController.downloadSecurityReviewSummary(
            securityReviewId,
        );
    },
);

const buildCompletedHeaderDropdownItems = (
    vendorStatus: VendorResponseDto['status'] | undefined,
    securityReviewId: number,
    vendorId: number,
    onDeleteSuccess?: () => void,
    isSocReview = false,
): SchemaDropdownItems => {
    const { isVendorEditable } = sharedFeatureAccessModel;

    if (!isVendorEditable) {
        return [
            {
                id: 'vendor-security-review-completed-header-download-summary-review',
                label: isSocReview
                    ? t`Download report review`
                    : t`Download summary`,
                type: 'item',
                value: 'DOWNLOAD_SUMMARY',
                colorScheme: 'neutral',
                onSelect: action(() => {
                    if (isSocReview) {
                        sharedSOCReviewFinalizeReviewController
                            .downloadReportAsync(securityReviewId, vendorId)
                            .catch(noop);
                    } else {
                        handleDownloadSummary(securityReviewId);
                    }
                }),
            },
        ];
    }

    const defaultActionItems: SchemaDropdownItems = [
        {
            id: 'vendor-security-review-completed-header-delete-review',
            label: t`Delete review`,
            type: 'item',
            value: 'DELETE_VENDOR',
            colorScheme: 'critical',
            onSelect: () => {
                handleDeleteReview(securityReviewId, onDeleteSuccess);
            },
        },
    ];

    if (vendorStatus === 'PROSPECTIVE') {
        return [
            {
                id: 'vendor-security-review-completed-header-download-summary-review',
                label: isSocReview
                    ? t`Download report review`
                    : t`Download summary`,
                type: 'item',
                value: 'DOWNLOAD_SUMMARY',
                colorScheme: 'neutral',
                onSelect: action(() => {
                    if (isSocReview) {
                        sharedSOCReviewFinalizeReviewController
                            .downloadReportAsync(securityReviewId, vendorId)
                            .catch(noop);
                    } else {
                        handleDownloadSummary(securityReviewId);
                    }
                }),
            },
            ...defaultActionItems,
        ];
    }

    if (vendorStatus === 'ARCHIVED') {
        return [
            {
                id: 'vendor-security-review-completed-header-download-summary-review',
                label: isSocReview
                    ? t`Download report review`
                    : t`Download summary`,
                type: 'item',
                value: 'DOWNLOAD_SUMMARY',
                colorScheme: 'neutral',
                onSelect: action(() => {
                    if (isSocReview) {
                        sharedSOCReviewFinalizeReviewController
                            .downloadReportAsync(securityReviewId, vendorId)
                            .catch(noop);
                    } else {
                        handleDownloadSummary(securityReviewId);
                    }
                }),
            },
            ...defaultActionItems,
        ];
    }

    return [
        {
            id: 'vendor-security-review-completed-header-archive-review',
            label: t`Archive vendor`,
            type: 'item',
            value: 'ARCHIVE_VENDOR',
            colorScheme: 'neutral',
            onSelect: () => {
                handleArchiveVendor(vendorId);
            },
        },
        {
            id: 'vendor-security-review-completed-header-download-summary-review',
            label: isSocReview
                ? t`Download report review`
                : t`Download summary`,
            type: 'item',
            value: 'DOWNLOAD_SUMMARY',
            colorScheme: 'neutral',
            onSelect: action(() => {
                if (isSocReview) {
                    sharedSOCReviewFinalizeReviewController
                        .downloadReportAsync(securityReviewId, vendorId)
                        .catch(noop);
                } else {
                    handleDownloadSummary(securityReviewId);
                }
            }),
        },
        ...defaultActionItems,
    ];
};

export const buildCompletedHeaderStackActions = (
    vendorStatus: VendorResponseDto['status'] | undefined,
    securityReviewId: number,
    vendorId: number,
    onDeleteSuccess?: () => void,
    isSocReview = false,
): ActionStackProps['stacks'] => {
    const { isVendorEditable } = sharedFeatureAccessModel;

    const actions: NonNullable<ActionStackProps['stacks']>[number]['actions'] =
        [
            {
                actionType: 'dropdown',
                id: 'vendor-security-review-completed-header-dropdown',
                typeProps: {
                    isIconOnly: true,
                    startIconName: 'HorizontalMenu',
                    level: 'tertiary',
                    label: 'Options',
                    align: 'end',
                    items: buildCompletedHeaderDropdownItems(
                        vendorStatus,
                        securityReviewId,
                        vendorId,
                        onDeleteSuccess,
                        isSocReview,
                    ),
                },
            },
        ];

    if (isVendorEditable) {
        actions.push({
            actionType: 'button',
            id: 'vendor-security-review-completed-header-finalize-review',
            typeProps: {
                label: t`Re-open review`,
                level: 'secondary',
                onClick: () => {
                    handleReopenReview(securityReviewId, vendorStatus);
                },
            },
        });
    }

    return [
        {
            actions,
            id: 'vendor-security-review-completed-header-actions-stack',
        },
    ];
};

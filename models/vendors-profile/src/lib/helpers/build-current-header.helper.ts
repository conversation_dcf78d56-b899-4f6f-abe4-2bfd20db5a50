import type { ComponentProps } from 'react';
import { openArchiveVendorModalFromProfile } from '@components/vendors-current-archive-vendor';
import { openDeleteVendorModalFromProfile } from '@components/vendors-current-delete-vendor';
import { openRecurringReviewsModal } from '@components/vendors-recurring-reviews';
import { sharedVendorsDetailsController } from '@controllers/vendors';
import type { ActionStack } from '@cosmos/components/action-stack';
import { t } from '@globals/i18n/macro';
import { runInAction } from '@globals/mobx';

const VENDORS_CURRENT_PROFILE_HEADER_KEY = 'vendors-current-profile-header';

export const getVendorsCurrentProfileHeaderActionValues = (): NonNullable<
    ComponentProps<typeof ActionStack>['stacks']
> => {
    const { data: vendor } = sharedVendorsDetailsController.vendorDetailsQuery;
    const isArchived = vendor?.status === 'ARCHIVED';

    return [
        {
            actions: [
                {
                    actionType: 'dropdown',
                    id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-horizontal-menu`,
                    typeProps: {
                        isIconOnly: true,
                        startIconName: 'HorizontalMenu',
                        level: 'tertiary',
                        label: 'Options',
                        align: 'end',
                        items: [
                            {
                                id: 'vendors-prospective-row-dropdown-archive-vendor',
                                label: isArchived
                                    ? t`Restore vendor`
                                    : t`Archive vendor`,
                                type: 'item',
                                value: 'ARCHIVE_VENDOR',
                                onClick: () => {
                                    runInAction(() => {
                                        const currentVendor =
                                            sharedVendorsDetailsController
                                                .vendorDetailsQuery.data;

                                        if (currentVendor) {
                                            const currentIsArchived =
                                                currentVendor.status ===
                                                'ARCHIVED';

                                            if (currentIsArchived) {
                                                sharedVendorsDetailsController.restoreVendor(
                                                    currentVendor.id,
                                                );
                                            } else {
                                                openArchiveVendorModalFromProfile(
                                                    {
                                                        isProspective: false,
                                                    },
                                                );
                                            }
                                        }
                                    });
                                },
                            },
                            {
                                id: 'vendors-prospective-row-dropdown-delete-vendor',
                                label: t`Delete vendor`,
                                type: 'item',
                                value: 'DELETE_VENDOR',
                                colorScheme: 'critical',
                                onClick: () => {
                                    runInAction(() => {
                                        const currentVendor =
                                            sharedVendorsDetailsController
                                                .vendorDetailsQuery.data;

                                        if (currentVendor) {
                                            openDeleteVendorModalFromProfile({
                                                isProspective: false,
                                            });
                                        }
                                    });
                                },
                            },
                        ],
                    },
                },
                {
                    actionType: 'button',
                    id: `${VENDORS_CURRENT_PROFILE_HEADER_KEY}-recurring-reviews-button`,
                    typeProps: {
                        label: t`Manage recurring reviews`,
                        level: 'secondary',
                        onClick: () => {
                            runInAction(() => {
                                const currentVendor =
                                    sharedVendorsDetailsController
                                        .vendorDetailsQuery.data;

                                if (currentVendor) {
                                    openRecurringReviewsModal(currentVendor.id);
                                }
                            });
                        },
                    },
                },
            ],
            id: `current-${vendor?.id}-documents-and-reports-header-actions-stack`,
        },
    ];
};

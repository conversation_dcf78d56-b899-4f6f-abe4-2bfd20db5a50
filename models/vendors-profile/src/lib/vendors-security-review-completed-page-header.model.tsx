import {
    openEditImpactLevelModal,
    openEditReviewDeadlineModal,
} from '@components/vendors-security-reviews';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import { routeController } from '@controllers/route';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimension3x } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import {
    buildImpactLevel,
    buildRequesterUser,
    buildStatus,
} from './config/vendor-security-review-header.config';
import { buildCompletedHeaderStackActions } from './helpers/vendor-security-review-completed.helper';
import type { VendorCategory } from './types/vendor-types';

export class VendorsSecurityReviewCompletedPageHeaderModel {
    #vendorType: VendorCategory = 'current';

    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'vendors-security-review-completed-page';

    get breadcrumbs(): Breadcrumb[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { vendor } = securityReviewDetails ?? {};
        const vendorId = vendor?.id;
        const vendorTypePrefix = this.#vendorType;

        return [
            { label: t`Vendors`, pathname: `vendors/${vendorTypePrefix}` },
            {
                label: vendor?.name ?? '',
                pathname: `vendors/${vendorTypePrefix}/${vendorId}`,
            },
            {
                label: t`Security reviews`,
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/security-reviews`,
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        const { vendorDetails } = sharedVendorsDetailsController;
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { status } = vendorDetails ?? {};
        const securityReviewId = securityReviewDetails?.id ?? 0;
        const vendorId = vendorDetails?.id ?? 0;

        const handleDeleteSuccess = () => {
            // Navigate to vendor overview page after successful deletion
            if (!vendorId) {
                return;
            }

            const vendorType =
                status === 'PROSPECTIVE' ? 'prospective' : 'current';

            sharedProgrammaticNavigationController.navigateTo(
                `${routeController.userPartOfUrl}/vendors/${vendorType}/${vendorId}/overview`,
            );
        };

        return (
            <ActionStack
                data-id="vendors-security-review-completed-page-action-stack"
                gap={dimension3x}
                stacks={buildCompletedHeaderStackActions(
                    status,
                    securityReviewId,
                    vendorId,
                    handleDeleteSuccess,
                )}
            />
        );
    }

    get title(): string {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return '';
        }

        return securityReviewDetails.title ?? t`Untitled security review`;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return [];
        }

        const { vendor, status, reviewDeadlineAt, requesterUser } =
            securityReviewDetails;

        const isCompleted = status === 'COMPLETED';

        const defaultKVP: KeyValuePairProps[] = [
            {
                id: 'vendor-security-review-completed-header-status',
                'data-id': 'vendor-security-review-completed-header-status-id',
                label: t`Status`,
                value: {
                    label: buildStatus(status),
                    type: 'status',
                    colorScheme: isCompleted ? 'success' : 'neutral',
                },
                type: 'BADGE',
            },
        ];

        if (vendor?.status === 'PROSPECTIVE') {
            return [
                ...defaultKVP,
                {
                    id: 'vendor-security-review-completed-header-requester',
                    'data-id':
                        'vendor-security-review-completed-header-requester',
                    label: t`Requester`,
                    value: buildRequesterUser(requesterUser),
                    type: 'USER',
                },
                {
                    id: 'vendor-security-review-completed-header-impact-level',
                    'data-id':
                        'vendor-security-review-completed-header-impact-level-id',
                    label: t`Impact level`,
                    value: buildImpactLevel(vendor.impactLevel),
                    type: 'TEXT',
                    onClick: openEditImpactLevelModal,
                    iconName: 'Edit',
                    iconSize: '100',
                    ariaLabel: t`Edit impact level`,
                },
                {
                    id: 'vendor-security-review-completed-header-review-deadline',
                    'data-id':
                        'vendor-security-review-completed-header-review-deadline',
                    label: t`Review deadline`,
                    value: formatDate(
                        'sentence',
                        reviewDeadlineAt
                            ? reviewDeadlineAt.split('T')[0]
                            : undefined,
                    ),
                    type: 'TEXT',
                    onClick: openEditReviewDeadlineModal,
                    iconName: 'Edit',
                    iconSize: '100',
                    ariaLabel: t`Edit review deadline`,
                },
            ];
        }

        return [
            ...defaultKVP,
            {
                id: 'vendor-security-review-completed-header-impact-level',
                'data-id':
                    'vendor-security-review-completed-header-impact-level-id',
                label: t`Impact level`,
                value: buildImpactLevel(vendor?.impactLevel),
                type: 'TEXT',
            },
        ];
    }

    setVendorType(vendorType: VendorCategory): void {
        this.#vendorType = vendorType;
    }
}

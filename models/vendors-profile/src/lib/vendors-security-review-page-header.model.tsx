import { sharedVendorsSecurityReviewDetailsController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimension3x } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { makeAutoObservable } from '@globals/mobx';
import { formatDate } from '@helpers/date-time';
import { getFullName } from '@helpers/formatters';
import {
    buildImpactLevel,
    buildStatus,
    getHeaderStackActions,
} from './config/vendor-security-review-header.config';
import type { VendorCategory } from './types/vendor-types';

export class VendorsSecurityReviewPageHeaderModel {
    #vendorType: VendorCategory = 'current';

    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'vendors-profile-security-review-page';

    get breadcrumbs(): Breadcrumb[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { vendor } = securityReviewDetails ?? {};
        const vendorId = vendor?.id;
        const vendorTypePrefix = this.#vendorType;

        return [
            { label: 'Vendors', pathname: `vendors/${vendorTypePrefix}` },
            {
                label: vendor?.name ?? 'Vendor',
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/overview`,
            },
            {
                label: 'Security reviews',
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/security-reviews`,
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        return (
            <ActionStack
                data-id="vendors-profile-security-review-page-action-stack"
                gap={dimension3x}
                stacks={getHeaderStackActions()}
            />
        );
    }

    get title(): string {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return '';
        }

        return securityReviewDetails.title ?? 'Untitled security review';
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return [];
        }

        const { vendor, status, reviewDeadlineAt } = securityReviewDetails;

        const isCompleted = status === 'COMPLETED';

        const kvpStatus: KeyValuePairProps = {
            id: 'vendor-security-review-header-status',
            'data-id': 'vendor-security-review-header-status-id',
            label: 'Status',
            value: {
                label: buildStatus(status),
                type: 'status',
                colorScheme: isCompleted ? 'success' : 'neutral',
            },
            type: 'BADGE',
        };

        if (vendor?.status !== 'PROSPECTIVE') {
            return [
                kvpStatus,
                {
                    id: 'vendor-security-review-header-impact-level',
                    'data-id': 'vendor-security-review-header-impact-level-id',
                    label: 'Impact level',
                    value: buildImpactLevel(vendor?.impactLevel),
                    type: 'TEXT',
                },
            ];
        }

        return [
            kvpStatus,
            {
                id: 'vendor-security-review-header-requester',
                'data-id': 'vendor-security-review-header-requester',
                label: 'Requester',
                value: getFullName(
                    vendor.user?.firstName,
                    vendor.user?.lastName,
                ),
                type: 'TEXT',
            },
            {
                id: 'vendor-security-review-header-review-deadline',
                'data-id': 'vendor-security-review-header-review-deadline',
                label: 'Review deadline',
                value: formatDate('sentence', reviewDeadlineAt),
                type: 'TEXT',
            },
        ];
    }

    setVendorType(type: VendorCategory): void {
        this.#vendorType = type;
    }
}

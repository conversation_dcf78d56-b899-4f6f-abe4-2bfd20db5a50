import { getVendorSecurityReviewLabelByType } from '@components/vendors-security-reviews';
import { sharedProgrammaticNavigationController } from '@controllers/programmatic-navigation';
import {
    sharedVendorsDetailsController,
    sharedVendorsSecurityReviewDetailsController,
} from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { dimension3x } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { buildCompletedHeaderStackActions } from './helpers/vendor-security-review-completed.helper';
import type { VendorCategory } from './types/vendor-types';

export class VendorsSecurityReviewSOCCompletedPageHeaderModel {
    #vendorType: VendorCategory = 'current';

    constructor() {
        makeAutoObservable(this);
    }

    pageId = 'vendors-security-review-soc-completed-page';

    get breadcrumbs(): Breadcrumb[] {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { vendor } = securityReviewDetails ?? {};
        const vendorId = vendor?.id;
        const vendorTypePrefix = this.#vendorType;

        return [
            { label: t`Vendors`, pathname: `vendors/${vendorTypePrefix}` },
            {
                label: vendor?.name ?? '',
                pathname: `vendors/${vendorTypePrefix}/${vendorId}`,
            },
            {
                label: t`Security reviews`,
                pathname: `vendors/${vendorTypePrefix}/${vendorId}/security-reviews`,
            },
        ];
    }

    get actionStack(): React.JSX.Element {
        const { vendorDetails } = sharedVendorsDetailsController;
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;
        const { status } = vendorDetails ?? {};
        const securityReviewId = securityReviewDetails?.id ?? 0;
        const vendorId = vendorDetails?.id ?? 0;

        const handleDeleteSuccess = () => {
            // Navigate to vendor overview page after successful deletion
            const workspaceId = sharedWorkspacesController.currentWorkspaceId;

            if (vendorId && workspaceId) {
                sharedProgrammaticNavigationController.navigateTo(
                    `/workspaces/${workspaceId}/vendors/current/${vendorId}/overview`,
                );
            }
        };

        return (
            <ActionStack
                data-id="vendors-security-review-soc-completed-page-action-stack"
                gap={dimension3x}
                stacks={buildCompletedHeaderStackActions(
                    status,
                    securityReviewId,
                    vendorId,
                    handleDeleteSuccess,
                    true,
                )}
            />
        );
    }

    get title(): string {
        const { securityReviewDetails } =
            sharedVendorsSecurityReviewDetailsController;

        if (!securityReviewDetails) {
            return '';
        }

        return (
            securityReviewDetails.title ??
            getVendorSecurityReviewLabelByType(securityReviewDetails)
        );
    }

    setVendorType(vendorType: VendorCategory): void {
        this.#vendorType = vendorType;
    }
}

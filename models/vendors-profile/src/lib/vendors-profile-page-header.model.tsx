import { sharedVendorsDetailsController } from '@controllers/vendors';
import { ActionStack } from '@cosmos/components/action-stack';
import { Banner } from '@cosmos/components/banner';
import type { KeyValuePairProps } from '@cosmos/components/key-value-pair';
import { dimension3x } from '@cosmos/constants/tokens';
import type { Breadcrumb } from '@cosmos-lab/components/breadcrumbs';
import { Organization } from '@cosmos-lab/components/organization';
import { t } from '@globals/i18n/macro';
import { makeAutoObservable } from '@globals/mobx';
import { buildProfileHeader } from './config/vendor-profile-header.config';
import { getVendorsCurrentProfileHeaderActionValues } from './helpers/build-current-header.helper';
import { getVendorsProspectiveProfileHeaderActionValues } from './helpers/build-prospective-header.helper';

export class VendorsProfilePageHeaderModel {
    constructor() {
        makeAutoObservable(this);
    }

    #parentUrl: string | null = null;

    get pageId(): string {
        return 'vendors-profile-overview-page';
    }

    get breadcrumbs(): Breadcrumb[] {
        return this.#parentUrl
            ? [
                  {
                      label: t`Vendors`,
                      pathname: this.#parentUrl,
                  },
              ]
            : [];
    }

    get banner(): React.JSX.Element | undefined {
        const { profileHeaderData } = sharedVendorsDetailsController;

        if (!profileHeaderData?.showSecurityReviewsInfoBanner) {
            return undefined;
        }

        return (
            <Banner
                data-id="vendors-profile-overview-banner-test-id"
                displayMode="section"
                title={t`Vendor review in progress`}
                body={t`You are currently conducting a security review of this vendor.`}
            />
        );
    }

    get actionStack(): React.JSX.Element {
        const { isProspectiveVendor } = sharedVendorsDetailsController;

        const stacks = isProspectiveVendor
            ? getVendorsProspectiveProfileHeaderActionValues()
            : getVendorsCurrentProfileHeaderActionValues();

        return (
            <ActionStack
                data-id="vendors-profile-overview-action-stack-test-id"
                gap={dimension3x}
                stacks={stacks}
            />
        );
    }

    get slot(): React.JSX.Element {
        return (
            <Organization
                data-id="vendors-profile-overview-slot-test-id"
                imgAlt="AWS"
                imgSrc={
                    sharedVendorsDetailsController.vendorDetails?.logoUrl ?? ''
                }
            />
        );
    }

    get title(): string {
        return sharedVendorsDetailsController.vendorDetails?.name ?? '';
    }

    get isLoading(): boolean {
        return sharedVendorsDetailsController.isLoading;
    }

    get keyValuePairs(): KeyValuePairProps[] {
        const { profileHeaderData } = sharedVendorsDetailsController;

        if (!profileHeaderData) {
            return [];
        }

        return buildProfileHeader(profileHeaderData);
    }

    setParentUrl(parentUrl: string): void {
        this.#parentUrl = parentUrl;
    }
}

import { Grid } from '@cosmos/components/grid';

interface ContentAsideProps {
    children: React.ReactNode;
    content?: React.ReactNode;
}
export const ContentAside = ({
    children,
    content,
}: ContentAsideProps): React.JSX.Element => {
    return (
        <Grid
            columns="13fr 7fr"
            height="100%"
            data-testid="ContentAside"
            data-id="45upwb1n"
        >
            <Grid pr="2xl">{children}</Grid>
            <Grid>{content}</Grid>
        </Grid>
    );
};

import { isEmpty } from 'lodash-es';
import { LocationBanners } from '@components/location-banners';
import { MainAppTopicsNavComponent } from '@components/main-app-topics-nav';
import { BannerLocation } from '@controllers/banner-service';
import { routeController } from '@controllers/route';
import { Box } from '@cosmos/components/box';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { observer } from '@globals/mobx';
import { Outlet, useLocation } from '@remix-run/react';
import {
    MAX_CENTERED_APPMAIN_WIDTH,
    MIN_APPMAIN_WIDTH,
} from '@ui/layout-constants';
import { ContentNavigationMenu } from '@ui/page-content';
import { PageHeaderUi } from '@ui/page-header';
import styles from './domain-content.module.css';

export const DomainContent = observer((): React.JSX.Element => {
    const location = useLocation();
    const { contentNavItems, pageHeader } = routeController;

    const isCentered = pageHeader?.isCentered;

    return (
        <Stack
            data-id="domains-flat-parent-stack"
            display="flex"
            direction="row"
            height="100%"
            minHeight="0"
            minWidth="0"
            flexGrow="1"
            flexShrink="1"
            flexBasis="0"
        >
            <MainAppTopicsNavComponent />

            {/* The Box allows Stack to horizontally overflow within Box */}
            <Box
                height="100%"
                width="100%"
                overflowX="auto"
                className={styles['domain-content']}
            >
                <Stack
                    data-id="domains-flat-child-stack"
                    display="flex"
                    direction="column"
                    height="100%"
                    minHeight="0"
                    overflowY="scroll"
                    minWidth={MIN_APPMAIN_WIDTH}
                    {...(isCentered ? { align: 'center' } : {})}
                >
                    <PageHeaderUi />

                    {!isEmpty(contentNavItems) && (
                        <ContentNavigationMenu
                            data-id="page-header-ui-content-navigation-menu"
                            value={location.pathname}
                            items={contentNavItems}
                        />
                    )}
                    {isEmpty(contentNavItems) && (
                        <Divider orientation="horizontal" size="sm" />
                    )}

                    <Stack
                        data-id="domains-flat-content-stack"
                        width="100%"
                        minWidth={MIN_APPMAIN_WIDTH}
                        height="100%"
                        minHeight="0"
                        p="3xl"
                        direction="column"
                        {...(isCentered
                            ? { maxWidth: MAX_CENTERED_APPMAIN_WIDTH }
                            : {})}
                    >
                        <LocationBanners
                            location={BannerLocation.DOMAIN_CONTENT}
                            dataIdPrefix="domain-content"
                        />
                        <Outlet />
                    </Stack>
                </Stack>
            </Box>
        </Stack>
    );
});

import { Icon, type IconName } from '@cosmos/components/icon';
import {
    getNavLinkState,
    StyledNavLinkAnchor,
} from '@cosmos/components/nav-link';
import { Text } from '@cosmos/components/text';
import { Tooltip } from '@cosmos/components/tooltip';
import { NavLink } from '@remix-run/react';

interface NavAppLinkProps {
    href: string;
    label: string;
    startIconName?: IconName;
    type: 'collapsed' | 'expanded';
    shouldShowTooltip?: boolean;
}

export const NavAppLinkUi = ({
    label,
    href,
    type,
    startIconName,
    shouldShowTooltip,
}: NavAppLinkProps): React.JSX.Element => {
    const { isCollapsed, padding, gap, borderRadius, stateOptions } =
        getNavLinkState({ type });

    // Extract the last part of the href for data-id
    const hrefParts = href.split('/');
    const navItemId = hrefParts[hrefParts.length - 1] || 'nav-link';

    return (
        <Tooltip
            isInteractive
            text={label}
            preferredSide="right"
            isDisabled={!shouldShowTooltip}
            delayDuration={250}
            data-testid="NavAppLinkUi"
            data-id="StWooX12"
        >
            <StyledNavLinkAnchor
                prefetch="intent"
                as={NavLink}
                to={href}
                data-id={`nav-${navItemId}`}
                $isCollapsed={isCollapsed}
                $stateOptions={stateOptions}
                $padding={padding}
                $gap={gap}
                $borderRadius={borderRadius}
            >
                {startIconName ? (
                    <Icon
                        name={startIconName}
                        size="200"
                        colorScheme="inherit"
                    />
                ) : null}
                {isCollapsed ? null : (
                    <Text as="span" colorScheme="inherit">
                        {label}
                    </Text>
                )}
            </StyledNavLinkAnchor>
        </Tooltip>
    );
};

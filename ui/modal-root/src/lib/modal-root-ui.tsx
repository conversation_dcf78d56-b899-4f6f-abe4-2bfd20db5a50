import { modalController } from '@controllers/modal';
import { Modal } from '@cosmos/components/modal';
import { action, observer } from '@globals/mobx';

export const ModalRoot = observer(() => {
    const { topModal } = modalController;

    if (!topModal) {
        return null;
    }

    /**
     * TODO: Should close be handled here?
     */
    return (
        <Modal
            data-testid="ModalRoot"
            data-id="BTU0R4Mp"
            size={topModal.size}
            disableClickOutsideToClose={topModal.disableClickOutsideToClose}
            onClose={action(() => {
                modalController.closeModal(topModal.id);
            })}
        >
            {topModal.content()}
        </Modal>
    );
});

# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## 🚨 CRITICAL: Check Conventions First!

**BEFORE writing ANY code**, ALWAYS check `.llm/PATTERN_INDEX.md` for the correct patterns to follow.

**MANDATORY FIRST STEP** when implementing ANY feature:
1. Read `.llm/PATTERN_INDEX.md`
2. Search for keywords related to the task
3. Follow the files it points to for detailed patterns

⚠️ **DO NOT** assume patterns or search the codebase directly without first checking PATTERN_INDEX.md!

## Development Commands

### Quick Start
```sh
# Install dependencies, update API SDK, generate tokens, and start dev server
pnpm run qs
```

### Core Commands
```sh
pnpm run app:drata:dev     # Development server
pnpm run test              # Run tests
pnpm run typecheck         # Type check
pnpm run lint              # Lint
pnpm run format            # Format code
pnpm run update-api-sdk    # Update API SDK from OpenAPI spec
```

### Module Generators (Interactive)
```sh
pnpm run generate:component
pnpm run generate:controller
pnpm run generate:model
pnpm run generate:view
```

## Architecture Overview

- **Pattern**: Custom MVC with MobX state management and Remix routing
- **Modules**: Controllers (business logic), Models (state), Views (pages), Components (UI)
- **State**: MobX with singleton controllers (`sharedXxxController`)
- **API**: Auto-generated SDK from OpenAPI specs
- **Routing**: Remix file-based routing in `/apps/drata/app/routes/`

## Key Reminders

### After Editing TypeScript
ALWAYS run: `pnpm run format && pnpm run typecheck && pnpm run lint`

### Critical Rules (will break build)
- Import violations - use global modules (`@globals/mobx`, `@globals/i18n/macro`)
- NEVER use `disabled` prop on buttons - use `isLoading` or `cosmosUseWithCaution_isDisabled`
- All user-facing text must use i18n - no hardcoded strings
- No default exports (except routes)

## Directory Structure
```
/controllers/    → Business logic with ObservedQuery/Mutation
/models/        → State containers with MobX
/views/         → Page components with observer HOC
/components/    → Reusable UI components
/globals/       → Shared services (api-sdk, i18n, mobx, auth)
```

## 📍 For ALL Patterns and Conventions

Check `.llm/PATTERN_INDEX.md` - it's your single source of truth for:
- Import rules and violations
- Component patterns
- API SDK usage
- Form patterns
- Testing patterns
- And much more...

The PATTERN_INDEX.md uses a keyword-based lookup system. Search for your task keywords to find the right documentation quickly.
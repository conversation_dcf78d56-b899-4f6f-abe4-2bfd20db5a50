import { sharedRiskLibraryDetailsController } from '@controllers/risk';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Skeleton } from '@cosmos/components/skeleton';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';

export const DetailsSection = observer((): React.JSX.Element => {
    const { riskLibraryDetails, isLoading } =
        sharedRiskLibraryDetailsController;
    const categories = riskLibraryDetails?.categories ?? [];

    return (
        <Stack
            direction="column"
            gap="2xl"
            data-testid="DetailsSection"
            data-id="25_rIWtK"
        >
            <Text type="title" size="400">
                {t`Details`}
            </Text>
            {isLoading ? (
                <Skeleton barCount={3} />
            ) : (
                <>
                    <KeyValuePair
                        label={t`Description`}
                        value={riskLibraryDetails?.description ?? '—'}
                    />
                    <KeyValuePair
                        label={t`Categories`}
                        value={categories
                            .map((category) => category.name)
                            .join(', ')}
                    />
                </>
            )}
        </Stack>
    );
});

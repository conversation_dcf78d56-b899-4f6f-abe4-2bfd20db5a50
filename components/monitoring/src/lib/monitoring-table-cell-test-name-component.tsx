import { Metadata } from '@cosmos/components/metadata';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';
import { TEST_TYPE_OPTIONS_ENUM } from './test-type-options.constants';
import type { MonitoringTableCellProps } from './types/monitoring.types';

export const MonitoringTableCellTestNameComponent = ({
    row: { original },
}: MonitoringTableCellProps): React.JSX.Element => {
    const { testName, testType, isNew } = original;

    return (
        <Stack
            gap="md"
            data-testid="MonitoringTableCellTestNameComponent"
            data-id="M0eljoQs"
        >
            {isNew && (
                <Metadata colorScheme="education" type="tag" label={t`New`} />
            )}
            {(testType === TEST_TYPE_OPTIONS_ENUM.CUSTOM_DRAFT ||
                testType === TEST_TYPE_OPTIONS_ENUM.DRATA_CUSTOM_DRAFT) && (
                <Metadata colorScheme="neutral" type="tag" label={t`Draft`} />
            )}
            <Text>{testName}</Text>
        </Stack>
    );
};

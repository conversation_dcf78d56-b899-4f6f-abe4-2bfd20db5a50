import type React from 'react';
import { sharedConnectionsController } from '@controllers/connections';
import type { CreateTicketPayload } from '@controllers/create-ticket';
import { modalController } from '@controllers/modal';
import {
    sharedRiskTicketCreationController,
    sharedRiskTicketsController,
} from '@controllers/risk';
import { sharedTicketAutomationController } from '@controllers/ticket-automation';
import {
    DEFAULT_PAGE_SIZE,
    DEFAULT_PAGE_SIZE_OPTIONS,
} from '@cosmos/components/datatable';
import { observer } from '@globals/mobx';
import { useParams } from '@remix-run/react';
import { CreateTicketModalView } from '@views/create-ticket-modal';
import { openClosedTicketsModal } from './helpers/open-closed-tickets-modal.helper';
import { UtilitiesTicketingBaseComponent } from './utilities-ticketing-base-component';

const CREATE_TICKET_MODAL_ID = 'create-ticket-modal';

const openCreateTicketModal = (
    onCreateTicket: (payload: CreateTicketPayload) => void,
    defaultDescription?: string,
): void => {
    sharedTicketAutomationController.loadTicketAutomation({
        globalFilter: {
            search: '',
            filters: {},
        },
        sorting: [],
        pagination: {
            pageIndex: 0,
            pageSizeOptions: DEFAULT_PAGE_SIZE_OPTIONS,
            pageSize: DEFAULT_PAGE_SIZE,
        },
    });
    sharedConnectionsController.allConfiguredConnectionsQuery.load();
    modalController.openModal({
        id: CREATE_TICKET_MODAL_ID,
        size: 'lg',
        centered: true,
        disableClickOutsideToClose: true,
        content: () => (
            <CreateTicketModalView
                defaultDescription={defaultDescription}
                data-id="I8pa5g9P"
                onCreateTicket={onCreateTicket}
            />
        ),
    });
};

export const UtilitiesTicketingForRiskManagementComponent = observer(
    (): React.JSX.Element => {
        const { riskId } = useParams();

        const {
            objectName,
            ticketsInProgress,
            isLoadingTicketsInProgress,
            isLoadingTicketsCompleted,
            totalTicketsInProgress,
            totalTicketsCompleted,
            userHasPermissionToCreateTicket,
            hasNextPage,
            loadNextPage,
            deleteTicket,
            isDeletingTicket,
            loadClosedTicketsInfinite,
        } = sharedRiskTicketsController;

        const createRiskTicket = (payload: CreateTicketPayload): void => {
            if (!riskId) {
                return;
            }

            sharedRiskTicketCreationController.createRiskTicket(
                payload,
                riskId,
            );
        };

        const handleCreateTicket = () => {
            if (!riskId) {
                return;
            }

            const defaultDescription = `Created in Drata for Risk ID: ${riskId}`;

            openCreateTicketModal(createRiskTicket, defaultDescription);
        };

        const handleViewClosedTickets = () => {
            if (!riskId) {
                return;
            }

            loadClosedTicketsInfinite(riskId);

            openClosedTicketsModal({
                objectName,
                objectId: 0,
                ticketsController: sharedRiskTicketsController,
            });
        };

        return (
            <UtilitiesTicketingBaseComponent
                objectName={objectName}
                ticketsInProgress={ticketsInProgress}
                isLoadingTicketsInProgress={isLoadingTicketsInProgress}
                isLoadingTicketsCompleted={isLoadingTicketsCompleted}
                totalTicketsInProgress={totalTicketsInProgress}
                totalTicketsCompleted={totalTicketsCompleted}
                hasNextPage={hasNextPage}
                loadNextPage={loadNextPage}
                isDeletingTicket={isDeletingTicket}
                data-id="6sPccBP6"
                deleteTicket={(ticketId) => {
                    deleteTicket(ticketId);
                }}
                userHasPermissionToCreateTicket={
                    userHasPermissionToCreateTicket
                }
                onCreateTicket={handleCreateTicket}
                onViewClosedTickets={handleViewClosedTickets}
            />
        );
    },
);

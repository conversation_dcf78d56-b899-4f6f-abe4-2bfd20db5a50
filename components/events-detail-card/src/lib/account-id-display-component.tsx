import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { Text } from '@cosmos/components/text';
import { t } from '@globals/i18n/macro';

export interface AccountIdDisplayProps {
    clientId: string;
    clientAlias?: string;
}

export const AccountIdDisplay = ({
    clientId,
    clientAlias,
}: AccountIdDisplayProps): React.JSX.Element => (
    <KeyValuePair
        label={t`Account ID / alias`}
        type="REACT_NODE"
        data-testid="AccountIdDisplay"
        data-id="cbfL9zfR"
        value={
            <Stack>
                <Text size="200" as="p">
                    {clientId}
                </Text>
                {clientAlias && <Text size="200">({clientAlias})</Text>}
            </Stack>
        }
    />
);

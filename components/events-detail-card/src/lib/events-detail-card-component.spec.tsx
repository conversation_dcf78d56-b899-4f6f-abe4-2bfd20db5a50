import { beforeEach, describe, expect, test, vi } from 'vitest';
import { sharedEventsDetailsController } from '@controllers/events-details';
import { render, screen } from '@testing-library/react';
import { EventsDetailCardComponent } from './events-detail-card-component';

// Define the type inline to avoid import issues
interface EventDetailsResponseDto {
    id: string;
    description: string;
    category: string;
    type: string;
    createdAt: string;
    status?: string;
    controlTestInstance?: {
        name: string;
        testId: string;
    } | null;
}

// Mock all the dependencies
vi.mock('@controllers/events-details', () => ({
    sharedEventsDetailsController: {
        eventsDetailsData: null,
        isLoading: false,
    },
}));

vi.mock('@globals/i18n/macro', () => ({
    t: (str: string) => str,
}));

vi.mock('@components/events', () => ({
    getEventCategoryLabel: vi.fn(),
    getEventTypeLabel: vi.fn(),
}));

vi.mock('@globals/providers', () => ({
    providers: {},
}));

vi.mock('@helpers/formatters', () => ({
    generateFallbackText: vi.fn(),
}));

vi.mock('./constants/events-details.constants', () => ({
    CHECK_RESULT__STATUS_COLOR: {},
    CHECK_RESULT_STATUS_ICONS: {},
    CHECK_RESULT_STATUS_LABELS: {},
}));

interface AppLinkProps {
    href?: string;
    size?: string;
    label?: string;
    [key: string]: unknown;
}

vi.mock('@ui/app-link', () => ({
    AppLink: ({ href, size, label, ...props }: AppLinkProps) => (
        <a
            href={href}
            data-size={size}
            data-testid="AppLink"
            {...props}
            data-id="IC9Rw-t1"
        >
            {label}
        </a>
    ),
}));

interface CardProps {
    title?: React.ReactNode;
    body?: React.ReactNode;
    [key: string]: unknown;
}

vi.mock('@cosmos/components/card', () => ({
    Card: ({ title, body, ...props }: CardProps) => (
        <div data-testid="Card" {...props} data-id="F_Enltxz">
            <h2>{title}</h2>
            {body}
        </div>
    ),
}));

interface Badge {
    label: string;
}

interface KeyValuePairProps {
    label?: React.ReactNode;
    value?: unknown;
    type?: string;
    [key: string]: unknown;
}

const renderKeyValuePairValue = (
    value: unknown,
    type?: string,
): React.ReactNode => {
    if (type === 'REACT_NODE') {
        return value as React.ReactNode;
    }
    if (type === 'BADGE') {
        return (
            <span data-testid="BadgeValue">
                {Array.isArray(value)
                    ? value.map((badge: Badge) => (
                          <span
                              key={badge.label}
                              data-testid="Badge"
                              data-id="GI5Njstn"
                          >
                              {badge.label}
                          </span>
                      ))
                    : 'Badge'}
            </span>
        );
    }

    return (
        <span data-testid="renderKeyValuePairValue" data-id="-ThghBh7">
            {value as React.ReactNode}
        </span>
    );
};

vi.mock('@cosmos/components/key-value-pair', () => ({
    KeyValuePair: ({ label, value, type, ...props }: KeyValuePairProps) => (
        <div data-testid="KeyValuePair" {...props} data-id="nvGiPhs5">
            <span>{label}</span>
            {renderKeyValuePairValue(value, type)}
        </div>
    ),
}));

interface StackProps {
    children?: React.ReactNode;
    [key: string]: unknown;
}

vi.mock('@cosmos/components/stack', () => ({
    Stack: ({ children, ...props }: StackProps) => (
        <div data-testid="Stack" {...props} data-id="AGLZJ16j">
            {children}
        </div>
    ),
}));

interface TextProps {
    children?: React.ReactNode;
    [key: string]: unknown;
}

interface DateTimeProps {
    value?: string;
    [key: string]: unknown;
}

interface EmptyValueProps {
    [key: string]: unknown;
}

interface OrganizationIdentityProps {
    [key: string]: unknown;
}

vi.mock('@cosmos/components/text', () => ({
    Text: ({ children, ...props }: TextProps) => (
        <span data-testid="Text" {...props} data-id="rt9_mSAW">
            {children}
        </span>
    ),
}));

vi.mock('@cosmos-lab/components/date-time', () => ({
    DateTime: ({ value, ...props }: DateTimeProps) => (
        <span data-testid="DateTime" {...props} data-id="-CFFSpm5">
            {value}
        </span>
    ),
}));

vi.mock('@cosmos-lab/components/empty-value', () => ({
    EmptyValue: (props: EmptyValueProps) => (
        <span data-testid="EmptyValue" {...props} data-id="C4BdDS9H">
            -
        </span>
    ),
}));

vi.mock('@cosmos-lab/components/identity', () => ({
    OrganizationIdentity: (props: OrganizationIdentityProps) => (
        <div data-testid="OrganizationIdentity" {...props} data-id="ck9OwY_z">
            Organization Identity
        </div>
    ),
}));

interface EventsDetailCardSkeletonProps {
    [key: string]: unknown;
}

interface BadgeProps {
    label?: string | React.ReactNode;
    [key: string]: unknown;
}

interface IconProps {
    name?: string;
    [key: string]: unknown;
}

vi.mock('./events-detail-card-skeleton', () => ({
    EventsDetailCardSkeleton: (props: EventsDetailCardSkeletonProps) => (
        <div
            data-testid="EventsDetailCardSkeleton"
            {...props}
            data-id="MTDLq3Ua"
        >
            Loading...
        </div>
    ),
}));

// Mock additional components that might cause rendering issues
vi.mock('@cosmos/components/badge', () => ({
    Badge: ({ label, ...props }: BadgeProps) => (
        <span data-testid="Badge" {...props} data-id="8EQvROc6">
            {label}
        </span>
    ),
}));

vi.mock('@cosmos/components/icon', () => ({
    Icon: ({ name, ...props }: IconProps) => (
        <span data-testid="Icon" {...props} data-id="_NjNxtZ3">
            {name}
        </span>
    ),
}));

// Mock MobX observer
vi.mock('@globals/mobx', () => ({
    observer: <T,>(component: T): T => component,
    makeAutoObservable: vi.fn(),
    ObservedQuery: vi.fn(),
    reaction: vi.fn(), // Add this missing export
}));

describe('eventsDetailCardComponent', () => {
    const mockEventData: EventDetailsResponseDto = {
        id: 'event-123',
        description: 'Test event description',
        category: 'AUTOPILOT',
        type: 'AP_TEST_EVENT',
        createdAt: '2024-01-15T10:30:00Z',
        status: 'PASSED',
        controlTestInstance: {
            name: 'Test Control Instance',
            testId: 'test-123',
        },
    };

    beforeEach(() => {
        vi.clearAllMocks();
        Object.assign(sharedEventsDetailsController, {
            eventsDetailsData: null,
            isLoading: false,
        });
    });

    test('basic test to check if vitest is working', () => {
        expect(1 + 1).toBe(2);
    });

    test('renders KeyValuePair with AppLink size="md"', () => {
        // Setup: Put mock data in controller
        Object.assign(sharedEventsDetailsController, {
            eventsDetailsData: mockEventData,
        });
        // Act: Render the component
        render(<EventsDetailCardComponent />);
        // Assert: Check if the component is rendered correctly
        expect(screen.getByText('Test')).toBeTruthy();

        const appLink = screen.getByTestId('AppLink');

        expect(appLink.getAttribute('data-size')).toBe('md'); // Main assertion you requested
    });
});

import { getEventCategory<PERSON>abel, getEventTypeLabel } from '@components/events';
import { sharedEventsDetailsController } from '@controllers/events-details';
import { routeController } from '@controllers/route';
import { Card } from '@cosmos/components/card';
import { KeyValuePair } from '@cosmos/components/key-value-pair';
import { Stack } from '@cosmos/components/stack';
import { DateTime } from '@cosmos-lab/components/date-time';
import { EmptyValue } from '@cosmos-lab/components/empty-value';
import { OrganizationIdentity } from '@cosmos-lab/components/identity';
import { t } from '@globals/i18n/macro';
import { observer } from '@globals/mobx';
import { providers } from '@globals/providers';
import { generateFallbackText } from '@helpers/formatters';
import { AppLink } from '@ui/app-link';
import { AccountIdDisplay } from './account-id-display-component';
import {
    CHECK_RESULT__STATUS_COLOR,
    CHECK_RESULT_STATUS_ICONS,
    CHECK_RESULT_STATUS_LABELS,
} from './constants/events-details.constants';
import { EventsDetailCardSkeleton } from './events-detail-card-skeleton';

export const EventsDetailCardComponent = observer((): React.JSX.Element => {
    const { eventsDetailsData, isLoading } = sharedEventsDetailsController;
    const userPart = routeController.userPartOfUrl;

    if (isLoading) {
        return <EventsDetailCardSkeleton />;
    }

    return (
        <Stack
            gap="2xl"
            direction="column"
            data-testid="EventsDetailCardComponent"
            data-id="mZWmWKUc"
        >
            <Card
                title={t`Details`}
                body={
                    <Stack direction="column" gap="2xl">
                        {eventsDetailsData?.user && (
                            <KeyValuePair
                                label={t`User`}
                                type="USER"
                                value={[
                                    {
                                        username: `${eventsDetailsData.user.firstName} ${eventsDetailsData.user.lastName}`,
                                        avatarProps: {
                                            fallbackText:
                                                generateFallbackText(
                                                    `${eventsDetailsData.user.firstName} ${eventsDetailsData.user.lastName}`,
                                                ) || '',
                                            imgSrc:
                                                eventsDetailsData.user
                                                    .avatarUrl || '',
                                            imgAlt: `${eventsDetailsData.user.firstName} ${eventsDetailsData.user.lastName}`,
                                        },
                                    },
                                ]}
                            />
                        )}
                        <KeyValuePair
                            label={t`Timestamp`}
                            type="REACT_NODE"
                            value={
                                eventsDetailsData?.createdAt ? (
                                    <DateTime
                                        date={eventsDetailsData.createdAt}
                                        format="sentence_time"
                                    />
                                ) : (
                                    <EmptyValue label="-" />
                                )
                            }
                        />
                        {eventsDetailsData?.status && (
                            <KeyValuePair
                                label={t`Result`}
                                type="BADGE"
                                value={[
                                    {
                                        label:
                                            CHECK_RESULT_STATUS_LABELS[
                                                eventsDetailsData.status
                                            ] || '',
                                        colorScheme:
                                            CHECK_RESULT__STATUS_COLOR[
                                                eventsDetailsData.status
                                            ],
                                        iconName:
                                            CHECK_RESULT_STATUS_ICONS[
                                                eventsDetailsData.status
                                            ],
                                    },
                                ]}
                            />
                        )}
                        <KeyValuePair
                            label={t`Description`}
                            value={eventsDetailsData?.description || ''}
                            type="TEXT"
                        />
                        {eventsDetailsData?.connection &&
                            (() => {
                                const { clientId, clientAlias } =
                                    eventsDetailsData.connection;

                                return (
                                    <AccountIdDisplay
                                        clientId={clientId}
                                        clientAlias={clientAlias}
                                        data-id="9FJxmz3i"
                                    />
                                );
                            })()}
                        {eventsDetailsData?.category === 'AUTOPILOT' && (
                            <>
                                {eventsDetailsData.controlTestInstance && (
                                    <KeyValuePair
                                        label={t`Test`}
                                        type="REACT_NODE"
                                        value={
                                            <AppLink
                                                size="md"
                                                href={`${userPart}/compliance/monitoring/production/${
                                                    eventsDetailsData
                                                        .controlTestInstance
                                                        .testId
                                                }/overview`}
                                                label={
                                                    eventsDetailsData
                                                        .controlTestInstance
                                                        .name
                                                }
                                            />
                                        }
                                    />
                                )}
                                {eventsDetailsData.connection && (
                                    <KeyValuePair
                                        label={t`Connection`}
                                        type="REACT_NODE"
                                        value={
                                            <OrganizationIdentity
                                                data-id={`${eventsDetailsData.connection.clientType}-name-column`}
                                                data-testid="AccessReviewApplicationNameCell"
                                                fallbackText={
                                                    eventsDetailsData.connection
                                                        .clientType
                                                }
                                                imgSrc={
                                                    providers[
                                                        eventsDetailsData
                                                            .connection
                                                            .clientType as keyof typeof providers
                                                    ].logo
                                                }
                                                primaryLabel={
                                                    providers[
                                                        eventsDetailsData
                                                            .connection
                                                            .clientType as keyof typeof providers
                                                    ].name
                                                }
                                            />
                                        }
                                    />
                                )}
                                <KeyValuePair
                                    label={t`Request`}
                                    type="TEXT"
                                    value={
                                        eventsDetailsData.requestDescription ??
                                        t`The requests Drata sends to internal and external systems generate a response that determines the result of the control test.`
                                    }
                                />
                            </>
                        )}
                        <KeyValuePair
                            label={t`Category`}
                            type="TEXT"
                            value={
                                eventsDetailsData?.category ? (
                                    getEventCategoryLabel(
                                        eventsDetailsData.category,
                                    )
                                ) : (
                                    <EmptyValue label="-" />
                                )
                            }
                        />
                        <KeyValuePair
                            label={t`Type`}
                            type="TEXT"
                            value={
                                eventsDetailsData?.type ? (
                                    getEventTypeLabel(eventsDetailsData.type)
                                ) : (
                                    <EmptyValue label="-" />
                                )
                            }
                        />
                    </Stack>
                }
            />
        </Stack>
    );
});

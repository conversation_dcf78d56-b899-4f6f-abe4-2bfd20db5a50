import { isNil } from 'lodash-es';
import { AvatarIdentity } from '@cosmos-lab/components/identity';
import { t } from '@globals/i18n/macro';
import { getFullName } from '@helpers/formatters';
import { getUserInitials } from '@helpers/user';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

const fallbackLabel = () => t`N/A`;

export const PersonnelOwnerCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { user, username } = original;
    const userLabel = username ?? user?.email ?? fallbackLabel();

    return (
        <AvatarIdentity
            secondaryLabel={isNil(user) ? '' : user.email}
            data-testid="PersonnelOwnerCell"
            data-id="OBQoq0mC"
            primaryLabel={
                isNil(user)
                    ? userLabel
                    : getFullName(user.firstName, user.firstName)
            }
            imgSrc={
                isNil(user) || isNil(user.avatarUrl)
                    ? undefined
                    : user.avatarUrl
            }
            fallbackText={
                isNil(user)
                    ? getUserInitials({ firstName: userLabel, lastName: '' })
                    : getUserInitials({
                          firstName: user.firstName,
                          lastName: user.lastName,
                      })
            }
        />
    );
};

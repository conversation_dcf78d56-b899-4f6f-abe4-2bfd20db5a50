import { isNil } from 'lodash-es';
import { EmptyStateTableCell } from '@cosmos/components/datatable';
import { Text } from '@cosmos/components/text';
import type { UserAccessReviewApplicationResponseDtoProps } from '../types/access-review-cell.types';

export const JobTitleCell = ({
    row: { original },
}: UserAccessReviewApplicationResponseDtoProps): React.JSX.Element => {
    const { user } = original;

    if (isNil(user?.jobTitle)) {
        return (
            <EmptyStateTableCell
                data-id="JG3g9H-9"
                data-testid="JobTitleCell"
            />
        );
    }

    return (
        <Text data-testid="JobTitleCell" data-id="JG3g9H-9">
            {user.jobTitle}
        </Text>
    );
};

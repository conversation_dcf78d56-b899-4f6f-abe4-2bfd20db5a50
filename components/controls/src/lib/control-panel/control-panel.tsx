import { useCallback, useEffect } from 'react';
import {
    sharedControlApprovalsController,
    sharedControlDetailsController,
    sharedControlDetailsOrchestratorController,
    sharedControlFrameworksController,
    sharedControlOwnersController,
    sharedControlsDetailsStatsController,
} from '@controllers/controls';
import { panelController } from '@controllers/panel';
import { Loader } from '@cosmos/components/loader';
import { Metadata } from '@cosmos/components/metadata';
import {
    PanelBody,
    PanelControls,
    PanelFooter,
    PanelHeader,
} from '@cosmos/components/panel';
import { Stack } from '@cosmos/components/stack';
import { Divider } from '@cosmos-lab/components/divider';
import { t } from '@globals/i18n/macro';
import { action, observer } from '@globals/mobx';
import { sharedWorkspacesController } from '@globals/workspaces';
import { DetailsSection } from './components/details-section';
import { ReadinessSection } from './components/readiness-section';
import type { ControlPanelProps } from './control-panel-props.type';
import { sharedControlPanelModel } from './model/control-panel.model';

const handleClosePanel = () => {
    panelController.closePanel();
};

export const ControlPanel = observer(
    ({
        'data-id': dataId,
        controlSource,
    }: ControlPanelProps): React.JSX.Element => {
        const {
            controlsToUse,
            currentControlIndex,
            panelFooterActions,
            shouldDisplayReadinessSection,
            shouldDisplayControlsPageLink,
            codeColorScheme,
        } = sharedControlPanelModel;
        const { currentWorkspace } = sharedWorkspacesController;
        const { controlDetails, isLoading: detailsIsLoading } =
            sharedControlDetailsController;
        const { isLoading: ownersIsLoading } = sharedControlOwnersController;
        const { isLoading: frameworksIsLoading } =
            sharedControlFrameworksController;
        const { isLoading: controlsDetailsStatsIsLoading } =
            sharedControlsDetailsStatsController;
        const { isLoading: isLoadingApprovals } =
            sharedControlApprovalsController;
        const isLoading =
            detailsIsLoading ||
            ownersIsLoading ||
            frameworksIsLoading ||
            controlsDetailsStatsIsLoading ||
            isLoadingApprovals;

        useEffect(() => {
            action(() => {
                sharedControlPanelModel.setControlPanelSource(controlSource);
            })();
        }, [controlSource]);

        const handleOnNextPageClick = useCallback(() => {
            if (currentControlIndex === controlsToUse.length - 1) {
                return;
            }
            const nextControl = controlsToUse[currentControlIndex + 1];

            action(() => {
                sharedControlDetailsOrchestratorController.load(
                    Number(nextControl.id),
                    controlSource,
                );
            })();
        }, [controlsToUse, currentControlIndex, controlSource]);

        const handleOnPrevPageClick = useCallback(() => {
            if (currentControlIndex === 0) {
                return;
            }
            const prevControl = controlsToUse[currentControlIndex - 1];

            action(() => {
                sharedControlDetailsOrchestratorController.load(
                    Number(prevControl.id),
                    controlSource,
                );
            })();
        }, [controlsToUse, currentControlIndex, controlSource]);

        if (isLoading) {
            return (
                <Stack
                    align="center"
                    justify="center"
                    height="100%"
                    width="100%"
                >
                    <Loader size="lg" label={t`Loading...`} />
                </Stack>
            );
        }

        return (
            <Stack
                data-testid="ControlPanel"
                data-id={dataId}
                direction="column"
                height="100%"
                minHeight="0"
            >
                <PanelControls
                    closeButtonLabel={t`Close`}
                    data-id="control-panel-controls"
                    pagination={{
                        currentItem: currentControlIndex + 1,
                        onNextPageClick: handleOnNextPageClick,
                        onPrevPageClick: handleOnPrevPageClick,
                        totalItems: controlsToUse.length,
                    }}
                    onClose={handleClosePanel}
                />
                <PanelHeader
                    data-id="control-panel-header"
                    title={controlDetails?.name ?? ''}
                    openPageLink={
                        shouldDisplayControlsPageLink
                            ? `/workspaces/${currentWorkspace?.id}/compliance/controls/${controlDetails?.id}/overview`
                            : undefined
                    }
                    slot={
                        <Metadata
                            label={controlDetails?.code ?? ''}
                            type="tag"
                            colorScheme={codeColorScheme}
                        />
                    }
                />
                <PanelBody data-id="panel-body">
                    <Stack gap="3xl" direction="column">
                        {shouldDisplayReadinessSection && (
                            <>
                                <ReadinessSection />
                                <Divider />
                            </>
                        )}
                        <DetailsSection />
                    </Stack>
                </PanelBody>
                <PanelFooter leftActionStack={panelFooterActions} />
            </Stack>
        );
    },
);

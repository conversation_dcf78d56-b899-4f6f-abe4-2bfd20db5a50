import { RuleTester } from 'eslint';
import { describe, expect, test } from 'vitest';
import * as typescriptEslintParser from '@typescript-eslint/parser';
import rule from './datatable-prefer-row-actions.mjs';

const ruleTester = new RuleTester({
    languageOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        parser: typescriptEslintParser,
        parserOptions: {
            ecmaFeatures: {
                jsx: true,
            },
            project: null,
            tsconfigRootDir: null,
        },
    },
});

describe('datatable-prefer-row-actions', () => {
    test('should detect manual action columns', () => {
        expect(() => {
            ruleTester.run('datatable-prefer-row-actions', rule, {
                valid: [
                    // Valid: Regular column without action-related properties
                    {
                        code: `
                        const columns = [
                            {
                                id: 'name',
                                header: 'Name',
                                cell: NameCell
                            }
                        ];
                    `,
                    },
                    // Valid: Using rowActionsProps instead of manual columns
                    {
                        code: `
                        <Datatable
                            columns={columns}
                            rowActionsProps={{
                                type: 'dropdown',
                                getRowActions: (row) => []
                            }}
                        />
                    `,
                    },
                ],
                invalid: [
                    // Invalid: Column with action ID
                    {
                        code: `
                        const columns = [
                            {
                                id: 'actions',
                                header: 'Actions',
                                cell: SomeCell
                            }
                        ];
                    `,
                        errors: [
                            {
                                messageId: 'manualActionColumn',
                            },
                        ],
                    },
                    // Invalid: Column with isActionColumn flag
                    {
                        code: `
                        const columns = [
                            {
                                id: 'menu',
                                header: 'Menu',
                                isActionColumn: true,
                                cell: SomeCell
                            }
                        ];
                    `,
                        errors: [
                            {
                                messageId: 'manualActionColumn',
                            },
                        ],
                    },
                    // Invalid: Function returning columns with action column
                    {
                        code: `
                        function getColumns() {
                            return [
                                {
                                    id: 'row-actions',
                                    header: 'Actions',
                                    cell: SomeCell
                                }
                            ];
                        }
                    `,
                        errors: [
                            {
                                messageId: 'manualActionColumn',
                            },
                        ],
                    },
                ],
            });
        }).not.toThrow();
    });
});

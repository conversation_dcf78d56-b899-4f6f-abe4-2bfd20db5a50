import { RuleTester } from 'eslint';
import { describe, test } from 'vitest';
import * as typescriptEslintParser from '@typescript-eslint/parser';
import buttonLoadingProps from './button-loading-props.mjs';

const ruleTester = new RuleTester({
    languageOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
        parser: typescriptEslintParser,
        parserOptions: {
            ecmaFeatures: {
                jsx: true,
            },
            project: null,
            tsconfigRootDir: null,
        },
    },
});

describe('button-loading-props', () => {
    // eslint-disable-next-line vitest/expect-expect -- it's fine
    test('rule tests', () => {
        ruleTester.run('button-loading-props', buttonLoadingProps, {
            valid: [
                // Valid: isLoading with a11yLoadingLabel
                {
                    code: `
                <Button
                    isLoading={isSubmitting}
                    a11yLoadingLabel="Saving..."
                    label="Save"
                />
            `,
                },
                // Valid: no isLoading prop
                {
                    code: `
                <Button
                    label="Save"
                    cosmosUseWithCaution_isDisabled={!isValid}
                />
            `,
                },
                // Valid: isLoading with static label (no conditional)
                {
                    code: `
                <Button
                    isLoading={controller.isSubmitting}
                    a11yLoadingLabel="Processing..."
                    label="Submit"
                />
            `,
                },
                // Valid: cosmosUseWithCaution_isDisabled for non-loading state
                {
                    code: `
                <Button
                    isLoading={isSubmitting}
                    a11yLoadingLabel="Saving..."
                    cosmosUseWithCaution_isDisabled={!isValid}
                    label="Save"
                />
            `,
                },
                // Valid: not a Button component
                {
                    code: `
                <div
                    isLoading={isSubmitting}
                />
            `,
                },
            ],
            invalid: [
                // Invalid: isLoading without a11yLoadingLabel
                {
                    code: `
                <Button
                    isLoading={isSubmitting}
                    label="Save"
                />
            `,
                    errors: [
                        {
                            messageId: 'missingA11yLoadingLabel',
                        },
                    ],
                },
                // Invalid: both isLoading and cosmosUseWithCaution_isDisabled for loading
                {
                    code: `
                <Button
                    isLoading={isSubmitting}
                    a11yLoadingLabel="Saving..."
                    cosmosUseWithCaution_isDisabled={isSubmitting}
                    label="Save"
                />
            `,
                    errors: [
                        {
                            messageId: 'redundantLoadingDisabled',
                        },
                    ],
                },
                // Invalid: conditional label with isLoading
                {
                    code: `
                <Button
                    isLoading={isSubmitting}
                    a11yLoadingLabel="Saving..."
                    label={isSubmitting ? "Saving..." : "Save"}
                />
            `,
                    errors: [
                        {
                            messageId: 'conditionalLabelWithIsLoading',
                        },
                    ],
                },
                // Invalid: conditional label with loading in member expression
                {
                    code: `
                <Button
                    isLoading={controller.isLoading}
                    a11yLoadingLabel="Processing..."
                    label={controller.isLoading ? "Processing..." : "Submit"}
                />
            `,
                    errors: [
                        {
                            messageId: 'conditionalLabelWithIsLoading',
                        },
                    ],
                },
                // Invalid: multiple violations
                {
                    code: `
                <Button
                    isLoading={isSubmitting}
                    cosmosUseWithCaution_isDisabled={isSubmitting}
                    label={isSubmitting ? "Saving..." : "Save"}
                />
            `,
                    errors: [
                        {
                            messageId: 'missingA11yLoadingLabel',
                        },
                        {
                            messageId: 'redundantLoadingDisabled',
                        },
                        {
                            messageId: 'conditionalLabelWithIsLoading',
                        },
                    ],
                },
            ],
        });
    });
});
